<script setup lang="ts">

import { guard } from '@/plugins/guard'
import { computed, ref } from 'vue'
import { onClickOutside } from '@vueuse/core'
import { useRoute, useRouter } from 'vue-router'
import { setNotificationOptions } from '@lib/store/snackbar'
import { formatCurrency, viewSetup } from '@lib/scripts/utils'
import { countriesList, facilitiesList, importStatusList, shippingMethodsList } from '@/store'
import { backorderRulesList, getDraftOrder, paymentTypesList, updateDraftOrder } from '@/modules/orders/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Input from '@lib/components/inputs/Input.vue'
import Select from '@lib/components/inputs/Select.vue'
import Loader from '@lib/components/blocks/Loader.vue'
import Toggle from '@lib/components/inputs/Toggle.vue'
import Button from '@lib/components/buttons/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import Details from '@/modules/orders/views/draft-details/components/Details.vue'
import Products from '@/modules/orders/views/draft-details/components/Products.vue'
import PublishBar from '@/components/PublishBar.vue'
import RecordError from '@/components/RecordError.vue'
import ImportErrorsBar from '@/components/ImportErrorsBar.vue'

import type { IconName } from '@lib/store/icon'
import type { ImportError, ResolveSchema } from '@/types'
import type { DraftOrder, DraftOrderLineItem, OrderBilling, OrderShipping } from '@/modules/orders/types'

interface orderView {
  id:      1 | 2
  name:    string
  icon:    IconName
  disable: boolean
}

const route               = useRoute()
const order               = ref<DraftOrder>( null )
const total               = ref<number>( 0 )
const router              = useRouter()
const orderId             = computed<number>(() => Number.parseInt( route.params.orderId as string ))
const itemsErrors         = ref<ImportError<DraftOrderLineItem>[]>( [] )
const eventsElement       = ref<HTMLElement>( null )
const lineItemsLoaded     = ref<boolean>( false )
const hasOrderLineItems   = ref<boolean>( false )

const editable            = computed<boolean>(() => [ 'Draft', 'Failed' ].includes( order.value?.importStatus ))
const isOrderEmpty        = computed<boolean>(() => !hasOrderLineItems.value && !order.value.masterRecordId )
const canEditDetails      = computed<boolean>(() => ( editable.value && !isGuidedResolution.value ) || !!recordError.value )

/**
 * ---- ERRORS RESOLUTION
 *
 * If The Order has a record error, it means that we can't guide trough the
 * resolve errors form, they will have to try and fix the error by themselves.
 */

const recordError = computed(() => order.value?.importErrors?.find( e => e.propertyName === 'record' )?.errorMsg )

/*
 * ---- GUIDED RESOLUTION
 *
 * Resolve errors schemas and functions
 */

const draftOrderResolveSchema = computed<ResolveSchema<DraftOrder>[]>(() => [
  {
    key:   'clientReference',
    type:  'text',
    label: 'Client Reference',
  },
  {
    key:      'backorderRule',
    type:     'select',
    label:    'Backorder Rule',
    options:  backorderRulesList,
    required: true
  },
  {
    key:   'poNumber',
    type:  'text',
    label: 'PO Number',
  },
  {
    key:   'shipHandlingFee',
    type:  'currency',
    label: 'Shipping Handling Fee',
  },
  {
    key:   'groupName',
    type:  'text',
    label: 'Group Name',
  },
  {
    key:   'giftMessage',
    type:  'text',
    label: 'Gift Message',
  },
  {
    key:      'facilityCode',
    type:     'select',
    label:    'Facility',
    options:  facilitiesList.value,
    required: true
  },
  {
    key:      'shippingMethod',
    type:     'select',
    label:    'Shipping Method',
    options:  shippingMethodsList.value,
    required: true
  },
  {
    key:   'holdForRelease',
    type:  'toggle',
    label: 'Hold for Release',
  },
  {
    key:   'isBusinessOrder',
    type:  'toggle',
    label: 'Business Order',
  },
  {
    key:   'isDdp',
    type:  'toggle',
    label: 'DDP',
  },
  {
    key:   'isGift',
    type:  'toggle',
    label: 'Is gift'
  }
] )

const shippingResolveSchema: ResolveSchema<OrderShipping>[] = [
  {
    key:   'firstName',
    type:  'text',
    label: 'First Name',
  },
  {
    key:   'lastName',
    type:  'text',
    label: 'Last Name',
  },
  {
    key:   'address1',
    type:  'text',
    label: 'Address 1',
  },
  {
    key:   'address2',
    type:  'text',
    label: 'Address 2',
  },
  {
    key:   'city',
    type:  'text',
    label: 'City',
  },
  {
    key:   'state',
    type:  'text',
    label: 'State',
  },
  {
    key:   'zip',
    type:  'text',
    label: 'Zip',
  },
  {
    key:     'country',
    type:    'select',
    label:   'Country',
    options: countriesList.value,
  },
  {
    key:   'companyName',
    type:  'text',
    label: 'Company Name',
  },
  {
    key:   'phone',
    type:  'text',
    label: 'Phone',
  },
  {
    key:   'email',
    type:  'text',
    label: 'Email',
  },
  {
    key:   'shipType',
    type:  'select',
    label: 'Shipping Method',
  }
]

const billingResolveSchema: ResolveSchema<OrderBilling>[] = [
  {
    key:   'firstName',
    type:  'text',
    label: 'First Name',
  },
  {
    key:   'lastName',
    type:  'text',
    label: 'Last Name',
  },
  {
    key:   'address1',
    type:  'text',
    label: 'Address 1',
  },
  {
    key:   'address2',
    type:  'text',
    label: 'Address 2',
  },
  {
    key:   'city',
    type:  'text',
    label: 'City',
  },
  {
    key:   'state',
    type:  'text',
    label: 'State',
  },
  {
    key:   'zip',
    type:  'text',
    label: 'Zip',
  },
  {
    key:     'country',
    type:    'select',
    label:   'Country',
    options: countriesList.value,
  },
  {
    key:   'companyName',
    type:  'text',
    label: 'Company Name',
  },
  {
    key:   'phone',
    type:  'text',
    label: 'Phone',
  },
  {
    key:   'email',
    type:  'text',
    label: 'Email',
  },
  {
    key:     'paymentType',
    type:    'select',
    label:   'Payment Type',
    options: paymentTypesList,
  }
]

const errorsSchema        = ref<ResolveSchema<any>[]>( [] )
const resolvePending      = ref<boolean>( false )
const openResolveForm     = ref<boolean>( false )
const canResolveErrors    = computed<boolean>(() => errorsSchema.value.filter( e => e.type !== 'toggle' ).every( e => e.valid ))
const isGuidedResolution  = computed<boolean>(() => !recordError.value && order.value?.importErrors?.length > 0 )

function createResolveSchemas() {

  const orderErrors     = order.value.importErrors
  const billingErrors   = JSON.parse( JSON.stringify( order.value.importErrors )) as ImportError<OrderBilling>[]
  const shippingErrors  = JSON.parse( JSON.stringify( order.value.importErrors )) as ImportError<OrderShipping>[]

  orderErrors.forEach(( error ) => {

    const err = error.errorMsg
    const key = error.propertyName.split( '.' ).at( -1 )

    const orderSchema = draftOrderResolveSchema.value.find( s => s.key === key )

    if ( orderSchema ) {
      orderSchema.error = err
      errorsSchema.value.push( orderSchema )
    }

  })

  shippingErrors.forEach(( error ) => {

    const err = error.errorMsg
    const key = error.propertyName.split( '.' ).at( -1 )
    const anchor = error.propertyName.split( '.' )[0]

    if ( anchor === 'shipping' ) {

      const shippingSchema = shippingResolveSchema.find( s => s.key === key )

      if ( shippingSchema ) {
        shippingSchema.error = err
        shippingSchema.anchor = anchor
        errorsSchema.value.push( shippingSchema )
      }

    }

  })

  billingErrors.forEach(( error ) => {

    const err = error.errorMsg
    const key = error.propertyName.split( '.' ).at( -1 )
    const anchor = error.propertyName.split( '.' )[0]

    if ( anchor === 'billing' ) {

      const billingSchema = billingResolveSchema.find( s => s.key === key )

      if ( billingSchema ) {
        billingSchema.error = err
        billingSchema.anchor = anchor
        errorsSchema.value.push( billingSchema )
      }

    }

  })

  openResolveForm.value = true

}

function closeResolveErrors() {

  errorsSchema.value = []
  openResolveForm.value = false

}

async function resolveErrorsAndUpdateOrder() {

  resolvePending.value = true

  errorsSchema.value.forEach(( error ) => {

    if ( error.type === 'toggle' ) {

      if ( !error?.value || error?.value === null || error?.value === undefined )
        error.value = false

    }

    if ( order.value.hasOwnProperty( error.key ))
      order.value[error.key] = error.value

    if ( error?.anchor ) {
      if ( order.value[error.anchor].hasOwnProperty( error.key ))
        order.value[error.anchor][error.key] = error.value
    }

  })

  const shipping = { ...order.value.shipping, shipType: order.value.shippingMethod }

  order.value.shipping = shipping

  const { error } = await updateDraftOrder( order.value.id, order.value )

  if ( !error ) {

    updateView()

    setNotificationOptions({ message: `Order details error${errorsSchema.value.length > 1 ? 's' : ''} resolved successfully.` })

    closeResolveErrors()

  }

  resolvePending.value = false

}

/**
 * ---- ORDER PUBLISHING
 *
 * Order can be published if the status is Draft and has no record errors.
 */

const canPublishOrder = computed<boolean>(() => {
  return lineItemsLoaded.value // ----------------------------------------- Check if the line items request has been completed
    && order.value?.importStatus === 'Draft' // --------------------------- Check if the order status is Draft
    && ( !recordError.value && order.value.importErrorCount === 0 ) // ---- Check if the order has errors
})

/**
 * Handle Click Outside
 */

function closeSidebarOnClickOutside() {
  openResolveForm.value = false
}

onClickOutside( eventsElement, closeSidebarOnClickOutside, { ignore: [ '.ignore-outside-events' ] })

/**
 * Mobile Views Flow
 */

const viewOrderViews = computed<orderView[]>(() => [
  {
    id:      1,
    name:    'Details',
    icon:    'details',
    disable: false
  },
  {
    id:      2,
    name:    'Products',
    icon:    'product',
    disable: false
  }
] )

const activeView = ref<orderView['id']>( 1 )
const storedView = ref<orderView['id']>( 1 )

/**
 * Get Order Details
 */
async function getOrderDetails( orderId: number ) {

  const { payload, status, error } = await getDraftOrder( orderId )

  if ( !error )
    order.value = updateOrderShipType( payload )

  return {
    error,
    status,
    payload
  }

}

/**
 * Update Order Ship Type
 * @description - Update the ship type based on the shipping method
 * @param {DraftOrder} payload - The Payload from the get or update order request
 * @returns {DraftOrder} - The updated order with the ship type
 */
function updateOrderShipType( payload: DraftOrder ): DraftOrder {

  payload.shipping.shipType = payload.shippingMethod

  if ( payload.shipping.shippingMethod )
    payload.shipping.shipType = shippingMethodsList.value.find( method => method.id === payload.shippingMethod )?.id

  return payload
}

/**
 * ---- View Setup
 */

const { silent, pending, updateView } = viewSetup(
  null,
  null,
  router,
  () => getOrderDetails( orderId.value ),
  orderId,
  true
)

</script>

<template>

  <div class="w-full h-full relative grid grid-rows-[max-content_1fr_max-content] md:grid-rows-[max-content_1fr] bg-core-30 overflow-hidden">

    <!-- Order Details Header -->

    <div class="w-full h-[5.5rem] md:h-12 grid grid-rows-[max-content_max-content] md:grid-rows-1 grid-cols-[1fr_max-content] md:grid-cols-[max-content_1fr_max-content_max-content] bg-core-120">

      <!-- Order Id :: Order Master Id -->

      <div class="h-12 flex items-center border-b border-core-100">

        <p v-if="pending" class="px-4 text-core-10">
          Loading Order Details ...
        </p>

        <div v-else class="h-full flex items-center">

          <!-- Order Id -->

          <div class="h-full px-4 flex items-center space-x-2 md:border-r border-core-100">

            <div class="flex items-center space-x-2">

              <div class="font-medium text-warning px-2 py-px flex items-center space-x-2 bg-warning/30">
                <Icon name="draft-order" size="s" />
                <p>Draft Order:</p>
              </div>

            </div>

            <p class="text-main-50 font-medium">
              [{{ order?.id }}]
            </p>

          </div>

          <!-- Master Id -->

          <div v-if="order?.masterRecordId" class="h-full px-4 hidden lg:flex items-center border-r border-core-100">

            <p class="text-core-10 font-medium">
              Order ID: <router-link :to="{ name: 'Live Order Details', params: { orderId: order?.masterRecordId } }" class="text-main-50 underline">
                [{{ order?.masterRecordId }}]
              </router-link>
            </p>

          </div>

        </div>

      </div>

      <!-- Import Status -->

      <div class="h-full md:w-max px-4 flex items-center row-start-2 md:row-start-auto md:border-r border-core-100">

        <p v-if="order?.importStatus" class="text-core-10 font-medium">
          Import Status:
          <span
            :class="{
              'text-error': order.importStatus === 'Failed',
              'text-success': order.importStatus === 'Processed',
              'text-core-60': order.importStatus === 'Draft',
              'text-main-50': order.importStatus === 'Processing',
              'text-data1-120': order.importStatus === 'Pending',
            }"
          >
            [{{ importStatusList.find(status => status.id === order.importStatus)?.name }}]
          </span>
        </p>

      </div>

      <!-- Other Header Options :: Use this div to add options ( Empty for now ) -->

      <div class="h-10 md:h-12 col-span-2 md:col-span-1 row-start-2 md:row-start-auto flex justify-center" />

      <!-- Close Button -->

      <div class="h-12 border-b md:border-l border-core-100">

        <Button
          mode="naked"
          type="box"
          size="auto"
          class="w-12 h-full text-core-10"
          :icon="{
            name: 'close',
            size: 'm',
          }"
          @click="$router.push({ name: 'Draft Orders' })"
        />

      </div>

    </div>

    <!-- Order Details :: Desktop -->

    <div v-if="pending" class="w-full h-full hidden md:flex justify-center space-x-4 p-10">
      <Loader />
    </div>

    <div v-else class="w-full h-full hidden md:grid grid-cols-[1fr_max-content] overflow-hidden">

      <div class="w-full h-full grid grid-rows-[max-content_1fr_max-content] overflow-hidden">

        <!-- Record Error Disclaimer & Publish bar :: Desktop -->

        <div class="w-full h-auto">

          <ImportErrorsBar
            v-if="(order?.importErrorCount > 0) && !recordError"
            type="order"
            :count="order?.importErrorCount"
            :can-resolve="isGuidedResolution && guard('Order.Write')"
            @resolve="createResolveSchemas"
          />

          <RecordError v-if="!!recordError">

            <p class="text-sm">

              <span class="font-medium">{{ recordError }}</span>
              <span> - </span>
              <span>This order has a record error, which unfortunately can't be resolved through the guided resolution form. Please fix this error manually. </span>

            </p>

          </RecordError>

          <Transition name="publish-bar">

            <PublishBar
              v-if="canPublishOrder"
              :id="orderId"
              type="order"
              :is-empty="isOrderEmpty"
              :can-publish="guard('Order.Write')"
              @update="() => order.importStatus = 'Pending'"
            />

          </Transition>

        </div>

        <!-- Order Errors & Products :: Desktop -->

        <Products
          v-model:items-errors="itemsErrors"
          :order
          :pending
          :editable
          @global-actions-update="(actions) => {
            total = actions.total,
            lineItemsLoaded = true
            hasOrderLineItems = actions.hasOrderLineItems
          }"
          @update-draft-order="(payload) => order = updateOrderShipType(payload)"
        />

        <!-- Total Cost -->

        <div class="w-full h-10 px-4 flex items-center justify-end bg-core-10 border-t border-r border-core-30">

          <p class="font-medium">
            <span class="text-core">Total Cost: </span>{{ formatCurrency(total) }}
          </p>

        </div>

      </div>

      <!-- Order Details Sidebar :: Desktop -->

      <div
        class="h-full grid grid-rows-[1fr_max-content] overflow-hidden transition-[width]"
        :class="{
          'w-0': pending,
          'w-[26rem]': !pending,
        }"
      >

        <Details
          :order
          :editable="canEditDetails"
          :update-pending="silent"
          @update-order="() => updateView('silent')"
        />

      </div>

    </div>

    <!-- Order Details :: Mobile -->

    <div v-if="pending" class="w-full h-full flex md:hidden justify-center space-x-4 p-10">
      <Loader />
    </div>

    <div v-else class="w-full h-full grid grid-rows-[max-content_1fr] md:hidden overflow-hidden">

      <!-- Errors & Publish Bar :: Mobile -->

      <div class="w-full h-auto max-h-56">

        <RecordError v-if="!!recordError">

          <p class="text-sm">

            <span class="font-medium">{{ recordError }}</span>
            <span> - </span>
            <span>This order has a record error, which unfortunately can't be resolved through the guided resolution form. Please fix this error manually. </span>

          </p>

        </RecordError>

        <ImportErrorsBar
          v-if="(order?.importErrorCount > 0) && !recordError"
          type="order"
          :count="order?.importErrorCount"
          :can-resolve="isGuidedResolution && guard('Order.Write')"
          @resolve="createResolveSchemas"
        />

        <Transition name="publish-bar">

          <PublishBar
            v-if="canPublishOrder"
            :id="orderId"
            type="order"
            :is-empty="isOrderEmpty"
            :can-publish="guard('Order.Write')"
            @update="() => order.importStatus = 'Pending'"
          />

        </Transition>

      </div>

      <!-- Order Details & Products -->

      <div class="w-full h-full overflow-hidden">

        <Transition :name="activeView > storedView ? 'mobile-view-right' : 'mobile-view-left'" @enter="() => storedView = activeView">

          <Details
            v-if="activeView === 1"
            :order
            :editable="canEditDetails"
            :update-pending="silent"
            @update-order="() => updateView('silent')"
          />

          <Products
            v-else-if="activeView === 2"
            v-model:items-errors="itemsErrors"
            :order
            :pending
            :editable
            @global-actions-update="(actions) => {
              total = actions.total,
              lineItemsLoaded = true
              hasOrderLineItems = actions.hasOrderLineItems
            }"
            @update-draft-order="(payload) => order = updateOrderShipType(payload)"
          />

        </Transition>

      </div>

    </div>

    <!-- Resolve Errors Form -->

    <Sidebar
      :dim="true"
      :strict="true"
      :open="openResolveForm"
      @close="closeResolveErrors"
    >

      <div class="w-full md:w-[28rem] h-full grid grid-rows-[max-content_1fr_max-content] bg-core-30">

        <div class="w-full h-12 sticky top-0 z-1 flex items-center bg-core-20 border-b border-core-30">

          <div class="h-full px-4 flex items-center space-x-3 grow border-r border-core-30">

            <Icon name="issue-circle" size="m" class="text-error" />

            <p class="text-sm font-medium">
              Resolve Errors
            </p>

          </div>

          <Button
            type="box"
            size="auto"
            mode="naked"
            class="w-12 h-full min-w-[3rem]"
            :disabled="resolvePending"
            :icon="{
              name: 'close',
              size: 'm',
            }"
            @click="closeResolveErrors"
          />

        </div>

        <div class="w-full h-full md:p-4 grid content-start gap-2 overflow-y-auto">

          <div
            v-for="error, index in errorsSchema"
            :key="error.key"
            :class="{
              'pointer-events-none': resolvePending,
            }"
          >

            <div class="p-4 bg-core-20">

              <p class="text-sm">
                <span class="text-error">[{{ index + 1 }}]</span> {{ error.error }}
              </p>

            </div>

            <Input
              v-if="error.type === 'text'"
              v-model="error.value"
              v-model:valid="error.valid"
              :label="error.label"
              :required="error.required"
            />

            <Input
              v-if="error.type === 'currency'"
              v-model="error.value"
              v-model:valid="error.valid"
              type="currency"
              :label="error.label"
              :required="error.required"
            />

            <Input
              v-if="error.type === 'number'"
              v-model="error.value"
              v-model:valid="error.valid"
              type="number"
              :label="error.label"
              :required="error.required"
            />

            <Input
              v-if="error.type === 'strict-number'"
              v-model="error.value"
              v-model:valid="error.valid"
              :label="error.label"
              type="strict-number"
              :required="error.required"
            />

            <Select
              v-else-if="error.type === 'select'"
              v-model="error.value"
              v-model:valid="error.valid"
              :label="error.label"
              :options="error.options"
              :required="error.required"
            />

            <div
              v-if="error.type === 'toggle'"
              class="w-full h-10 px-4 flex items-center bg-core-10"
            >

              <Toggle
                v-model="error.value"
                v-model:valid="error.valid"
              >
                <p class="text-sm px-4">
                  {{ error.label }}
                </p>
              </Toggle>

            </div>

          </div>

        </div>

        <div class="w-full grid">

          <Button
            :disabled="!canResolveErrors"
            :pending="resolvePending"
            @click="resolveErrorsAndUpdateOrder"
          >
            Resolve Errors
          </Button>

        </div>

      </div>

    </Sidebar>

    <!-- Order Details Footer -->

    <div class="w-full h-12 z-1 md:hidden grid grid-cols-2 bg-core-10 border-t border-core-30">

      <Button
        v-for="view in viewOrderViews"
        :key="view.id"
        :icon="{
          name: view.icon,
          size: 'm',
        }"
        mode="naked"
        type="box"
        size="auto"
        class="w-full h-full"
        :class="{ 'text-main bg-core-20': activeView === view.id }"
        :disabled="view.disable"
        @click="activeView = view.id"
      />

    </div>

  </div>

</template>
