<script setup lang="ts">

import { validateModel } from '@lib/scripts/inputValidation'
import { updateDraftOrder } from '@/modules/orders/store'
import { setNotificationOptions } from '@lib/store/snackbar'
import { computed, onMounted, reactive, ref } from 'vue'
import { countriesList, shippingMethodsList } from '@/store'
import { convertObjectToPlain, convertObjectToValidatable } from '@lib/scripts/utils'

import Input from '@lib/components/inputs/Input.vue'
import Button from '@lib/components/buttons/Button.vue'
import Select from '@lib/components/inputs/Select.vue'
import Checkbox from '@lib/components/inputs/Checkbox.vue'

import type { DraftOrder, OrderBilling, OrderShipping } from '@/modules/orders/types'

const props = defineProps<{
  order:   DraftOrder
  pending: boolean
}>()

const emits = defineEmits<{
  ( event: 'close' ): void
  ( event: 'update' ): void
}>()

const billing: OrderBilling   = props.order.billing
const shipping: OrderShipping = props.order.shipping

function mapShippingToBilling() {
  Object.keys( billingModel ).forEach(( key ) => {
    if ( key in shippingModel )
      billingModel[key].value = shippingModel[key]?.value
  })
}

function toggleSameInfo( isSame: boolean ) {

  if ( isSame )
    mapShippingToBilling()

}

function areBillingAndShippingSame() {

  if (

    billingModel.zip.value === shippingModel.zip.value
    && billingModel.city.value === shippingModel.city.value
    && billingModel.state.value === shippingModel.state.value
    && billingModel.email.value === shippingModel.email.value
    && billingModel.phone.value === shippingModel.phone.value
    && billingModel.country.value === shippingModel.country.value
    && billingModel.address1.value === shippingModel.address1.value
    && billingModel.address2.value === shippingModel.address2.value
    && billingModel.lastName.value === shippingModel.lastName.value
    && billingModel.firstName.value === shippingModel.firstName.value
    && billingModel.companyName.value === shippingModel.companyName.value

  ) {
    return true
  }

  return false

}

const billingModel  = reactive<Validatable<OrderBilling>>( convertObjectToValidatable( billing, null, [ 'companyName', 'address2', 'email', 'phone', 'fax', 'paymentType' ] ))
const shippingModel = reactive<Validatable<OrderShipping>>( convertObjectToValidatable( shipping, null, [ 'companyName', 'address2', 'email', 'phone', 'fax', 'methodReference', 'shipType' ] ))

const canUpdate     = computed(() => validateModel( billingModel ) && validateModel( shippingModel ))
const sameModels    = ref<boolean>( true )
const updatePending = defineModel<boolean>( 'pending' )

async function updateOrderDetails() {

  updatePending.value = true

  if ( sameModels.value )
    mapShippingToBilling()

  const orderBilling = convertObjectToPlain( billingModel )
  const orderShipping = convertObjectToPlain( shippingModel )

  const orderData: DraftOrder = {
    ...props.order,
    importStatus: props.order.importStatus === 'Failed' ? 'Draft' : props.order.importStatus,
    billing:      orderBilling,
    shipping:     orderShipping

  }

  const { error } = await updateDraftOrder( props.order.id, orderData )

  if ( !error ) {

    emits( 'update' )
    setNotificationOptions({ message: 'Order details are updated successfully.' })

  }
  updatePending.value = false

}

onMounted(() => {

  // Check if billing and shipping details are the same

  sameModels.value = areBillingAndShippingSame()

})

defineExpose({
  canUpdate,
  billingModel,
  shippingModel,
  updateOrderDetails
})

</script>

<template>

  <div class="w-full h-full grid grid-rows-[1fr_max-content] overflow-hidden">

    <form
      class="w-full md:w-[46rem] md:max-w-[46rem] md:px-2 grid gap-4 overflow-hidden overflow-y-auto content-start"
      :class="{
        'pointer-events-none': updatePending,
      }"
      @submit.prevent
    >

      <!-- Shipping Details Section -->

      <section data-section="shipping" class="md:px-4 grid md:grid-cols-2 md:gap-4">

        <div class="h-12 sticky top-0 z-1 px-4 md:px-2 flex items-center md:col-span-2 bg-core-20 border-b md:border-b-0 border-core-30">
          <p class="text-xs text-core uppercase">
            Shipping Details
          </p>
        </div>

        <Input
          v-model="shippingModel.firstName.value"
          v-model:valid="shippingModel.firstName.valid"
          label="First Name"
        />

        <Input
          v-model="shippingModel.lastName.value"
          v-model:valid="shippingModel.lastName.valid"
          label="Last Name"
        />

        <Input
          v-model="shippingModel.companyName.value"
          v-model:valid="shippingModel.companyName.valid"
          label="Company Name"
          class="md:col-span-2"
          :required="false"
        />

        <Input
          v-model="shippingModel.address1.value"
          v-model:valid="shippingModel.address1.valid"
          label="Address Line 1"
        />

        <Input
          v-model="shippingModel.address2.value"
          v-model:valid="shippingModel.address2.valid"
          label="Address Line 2"
          :required="false"
        />

        <Select
          v-model="shippingModel.country.value"
          v-model:valid="shippingModel.country.valid"
          :options="countriesList"
          label="Country"
        />

        <Input
          v-model="shippingModel.state.value"
          v-model:valid="shippingModel.state.valid"
          label="State"
        />

        <Input
          v-model="shippingModel.city.value"
          v-model:valid="shippingModel.city.valid"
          label="City"
        />

        <Input
          v-model="shippingModel.zip.value"
          v-model:valid="shippingModel.zip.valid"
          label="Postal Code"
        />

        <Input
          v-model="shippingModel.email.value"
          v-model:valid="shippingModel.email.valid"
          label="Email"
          :required="false"
        />

        <Input
          v-model="shippingModel.phone.value"
          v-model:valid="shippingModel.phone.valid"
          label="Phone"
          :required="false"
        />

        <div class="h-12 md:h-auto px-4 md:px-0 flex items-center md:col-span-2">
          <Checkbox
            v-model="sameModels"
            class="md:col-span-2"
            @update:model-value="(e: boolean) => toggleSameInfo(e)"
          >
            Billing details are same as shipping
          </Checkbox>
        </div>

      </section>

      <!-- Billing Details Section -->

      <section v-show="!sameModels" data-section="billing" class="md:px-4 grid md:grid-cols-2 md:gap-4">

        <div class="h-12 sticky top-0 z-1 px-4 md:px-2 flex items-center md:col-span-2 bg-core-20 border-b md:border-b-0 border-core-30">
          <p class="text-xs text-core uppercase">
            Billing Details
          </p>
        </div>

        <Input
          v-model="billingModel.firstName.value"
          v-model:valid="billingModel.firstName.valid"
          label="First Name"
        />

        <Input
          v-model="billingModel.lastName.value"
          v-model:valid="billingModel.lastName.valid"
          label="Last Name"
        />

        <Input
          v-model="billingModel.companyName.value"
          v-model:valid="billingModel.companyName.valid"
          label="Company Name"
          class="md:col-span-2"
          :required="false"
        />

        <Input
          v-model="billingModel.address1.value"
          v-model:valid="billingModel.address1.valid"
          label="Address Line 1"
        />

        <Input
          v-model="billingModel.address2.value"
          v-model:valid="billingModel.address2.valid"
          label="Address Line 2"
          :required="false"
        />

        <Select
          v-model="billingModel.country.value"
          v-model:valid="billingModel.country.valid"
          :options="countriesList"
          label="Country"
        />

        <Input
          v-model="billingModel.state.value"
          v-model:valid="billingModel.state.valid"
          label="State"
        />

        <Input
          v-model="billingModel.city.value"
          v-model:valid="billingModel.city.valid"
          label="City"
        />

        <Input
          v-model="billingModel.zip.value"
          v-model:valid="billingModel.zip.valid"
          label="Postal Code"
        />

        <Input
          v-model="billingModel.email.value"
          v-model:valid="billingModel.email.valid"
          label="Email"
          :required="false"
        />

        <Input
          v-model="billingModel.phone.value"
          v-model:valid="billingModel.phone.valid"
          label="Phone"
          :required="false"
        />

      </section>

      <!-- Rest of the Shipping Details Section -->

      <section data-section="delivery" class="md:px-4 pb-4 grid md:grid-cols-2 md:gap-4">

        <div class="h-12 sticky top-0 z-1 px-4 md:px-2 flex items-center md:col-span-2 bg-core-20 border-b md:border-b-0 border-core-30">
          <p class="text-xs text-core uppercase">
            Delivery Details
          </p>
        </div>

        <Select
          v-model="shippingModel.shipType.value"
          v-model:valid="shippingModel.shipType.valid"
          label="Shipping Method"
          :nullable="false"
          :options="shippingMethodsList"
          class="col-span-2"
        />

      </section>

    </form>

    <div class="w-full h-12 sticky top-0 z-1 grid grid-cols-2 bg-core-20 border-t border-core-30">

      <Button
        data-button="cancel"
        size="auto"
        mode="naked"
        :disabled="updatePending"
        @click="emits('close')"
      >
        {{ $t('global.button.cancel') }}
      </Button>

      <Button
        data-button="update"
        size="auto"
        :pending="updatePending"
        :disabled="!canUpdate"
        @click="updateOrderDetails"
      >
        Save Info
      </Button>

    </div>

  </div>

</template>
