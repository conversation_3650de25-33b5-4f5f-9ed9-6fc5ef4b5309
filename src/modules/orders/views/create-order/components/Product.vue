<script setup lang="ts">

import { computed } from 'vue'
import { selectedItems } from '@/modules/orders/views/create-order/store'
import { checkValue, formatCurrency } from '@lib/scripts/utils'

import Row from '@lib/components/table/Row.vue'
import Cell from '@lib/components/table/Cell.vue'
import Input from '@lib/components/inputs/Input.vue'
import Button from '@lib/components/buttons/Button.vue'
import SimpleBadge from '@/components/SimpleBadge.vue'

import type { TableReference } from '@lib/types/tableTypes'
import type { CreateOrderLineItem } from '@/modules/orders/types'

const props = defineProps<{
  item?:           CreateOrderLineItem
  schema?:         TableSchema<CreateOrderLineItem>
  tableName?:      string
  tableReference?: TableReference
}>()

const emits = defineEmits<{
  ( event: 'update:item', item: CreateOrderLineItem ): void
  ( event: 'update:tableReference', payload: TableReference ): void
}>()

const product = computed({
  get: () => props.item,
  set: value => emits( 'update:item', value )
})

const reference = computed({
  get: () => props.tableReference,
  set: value => emits( 'update:tableReference', value )
})

function removeItem( itemId: number ) {
  selectedItems.value = selectedItems.value.filter( item => item.id !== itemId )
}

function addQuantity( item: CreateOrderLineItem ) {

  if ( !item )
    return

  item.requested++

}

function removeQuantity( item: CreateOrderLineItem ) {

  if ( !item )
    return

  if ( item.requested > 1 )
    item.requested--

  else
    removeItem( item.id )

}

function calculateAvailable( item: CreateOrderLineItem ) {

  if ( item.available > item.requested )
    return item.available - item.requested

  else return 0

}

function calculateBackordered( item: CreateOrderLineItem ) {

  if ( calculateAvailable( item ) === 0 ) {
    item.backordered = item.requested - item.available
    return item.backordered
  }

}

function calculateTotal( item: CreateOrderLineItem ) {

  if ( !item.requested )
    return 0

  if ( !item.declaredValue )
    return 0

  return item.declaredValue * item.requested

}

</script>

<template>

  <Row
    height="h-24 xl:h-[4rem]"
    width="w-full xl:w-max"
    :table-name="tableName"
    data-row="selected-product"
  >

    <div class="w-full h-24 xl:hidden grid border-b">

      <div class="row-span-2 flex justify-between">

        <div class="truncate h-full px-4 grid content-center">

          <p class="truncate" data-reference-mobile="selected-part-reference">
            {{ product?.partReference }}
          </p>

          <p class="max-w-[12.5rem] truncate text-core text-xs" data-description-mobile="selected-description">
            {{ product?.description }}
          </p>

        </div>

        <div class="h-full pr-4 self-center grid items-center justify-end">

          <div class="flex items-center w-[8.6rem]">

            <Button
              data-button-mobile="selected-removeQty"
              size="xs"
              mode="secondary"
              type="badge"
              :icon="{
                name: 'minus',
                size: 's',
              }"
              @click="removeQuantity(item)"
            />

            <Input
              v-model="product.requested"
              data-input-mobile="selected-qty"
              :min="1"
              mode="naked"
              size="xs"
              type="number"
              class="text-sm max-w-[5rem]"
              :required="false"
              placeholder="Qty"
            />

            <Button
              data-button-mobile="selected-addQty"
              size="xs"
              mode="secondary"
              type="badge"
              :icon="{
                name: 'add',
                size: 's',
              }"
              @click="addQuantity(item)"
            />

          </div>

        </div>

      </div>

      <div class="w-full grid grid-cols-[7.5rem_5.5rem_1fr]">

        <div class="h-full text-sm text-core px-4 grid items-center">

          <p v-if="!calculateBackordered(item) && !(item.kitComponents?.length > 0)" class="text-sm" data-backorder-mobile="no-backorder">
            {{ calculateAvailable(item) }} <span v-if="calculateBackordered(item)" class="text-error">| {{ calculateBackordered(item) }} BO</span>
          </p>
          <p v-else-if="!(item.kitComponents?.length > 0)" class="text-sm text-error" data-backorder-mobile="backorder">
            <span class="text-core">{{ calculateAvailable(item) }} /</span> {{ calculateBackordered(item) }} BO
          </p>
          <SimpleBadge v-else>
            KIT
          </SimpleBadge>
        </div>

        <div
          class="h-max text-sm grid items-center justify-start border-core-30"
          :class="{
            'border border-error': !checkValue(item.declaredValue),
          }"
        >

          <Input
            v-model="product.declaredValue"
            data-input-mobile="price"
            size="xs"
            type="currency"
            align="center"
            :padding="false"
            placeholder="Price"
            class="text-sm max-w-[5.5rem]"
            :required="false"
          />

        </div>

        <div class="h-full text-sm text-right font-medium grow px-4 grid items-center">
          <p data-total-mobile="total-price">
            <span class="font-normal text-core">Total: </span>{{ formatCurrency(calculateTotal(product)) }}
          </p>
        </div>

      </div>

    </div>

    <Cell
      v-model:cell="reference.cells[0]"
      :reference="reference"
      :flex="true"
      :resize="false"
      class="hidden xl:flex"
    >

      <div>

        <p class="font-medium" data-sku-desktop="selected-sku">
          {{ item.partReference }}
        </p>

        <p class="text-xs text-core" data-description-desktop="selected-description">
          {{ item.description }}
        </p>

      </div>

    </Cell>

    <Cell
      v-model:cell="reference.cells[1]"
      :reference="reference"
      :flex="true"
      class="hidden xl:flex"
    >
      <div class="h-full text-sm text-core px-4 flex items-center">
        <p v-if="!calculateBackordered(item) && !(item.kitComponents?.length > 0)" class="text-sm" data-available-desktop="selected-no-backordered">
          {{ calculateAvailable(item) }} <span v-if="calculateBackordered(item)" class="text-error" data-backordered-desktop="selected-no-backordered-backordered">| {{ calculateBackordered(item) }} BO</span>
        </p>
        <p v-else-if="!(item.kitComponents?.length > 0)" class="text-sm text-error" data-backordered-desktop="backordered-BO">
          <span class="text-core" data-available-desktop="selected-backordered">{{ calculateAvailable(item) }} /</span> {{ calculateBackordered(item) }} BO
        </p>
        <SimpleBadge v-else>
          KIT
        </SimpleBadge>
      </div>
    </Cell>

    <Cell
      v-model:cell="reference.cells[2]"
      :reference="reference"
      :flex="true"
      class="hidden xl:flex"
    >
      <Input
        v-model="product.declaredValue"
        data-input-desktop="price"
        size="s"
        type="currency"
        align="right"
        :padding="false"
        placeholder="Price"
        class="text-sm"
        :required="false"
      />
    </Cell>

    <Cell
      v-model:cell="reference.cells[3]"
      :reference="reference"
      :flex="true"
      class="hidden xl:flex"
    >
      <p class="font-medium text-right" data-total-desktop="total-price">
        {{ formatCurrency(calculateTotal(product)) }}
      </p>
    </Cell>

    <Cell
      v-model:cell="reference.cells[4]"
      :reference="reference"
      :flex="true"
      class="hidden xl:flex"
      sticky="right"
    >
      <div class="flex items-center">

        <Button
          data-button-desktop="removeQty"
          size="xs"
          mode="secondary"
          type="badge"
          :icon="{
            name: 'minus',
            size: 's',
          }"
          @click="removeQuantity(item)"
        />

        <Input
          v-model="product.requested"
          data-input-desktop="qty"
          :min="1"
          mode="naked"
          size="xs"
          type="number"
          class="text-sm max-w-[5rem]"
          :required="false"
          placeholder="Qty"
        />

        <Button
          data-button-desktop="addQty"
          size="xs"
          mode="secondary"
          type="badge"
          :icon="{
            name: 'add',
            size: 's',
          }"
          @click="addQuantity(item)"
        />

      </div>
    </Cell>

  </Row>

</template>
