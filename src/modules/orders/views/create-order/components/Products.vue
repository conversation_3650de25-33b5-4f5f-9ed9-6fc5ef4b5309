<script setup lang="ts">

import { Flow } from '@/modules/orders/views/create-order/types'
import { activeStep, productsStepValid, selectedItems } from '@/modules/orders/views/create-order/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Table from '@lib/components/table/Table.vue'
import Button from '@lib/components/buttons/Button.vue'
import Product from '@/modules/orders/views/create-order/components/Product.vue'

import type { CreateOrderLineItem } from '@/modules/orders/types'

defineEmits<{
  ( event: 'close' ): void
}>()

function schema(): TableSchema<CreateOrderLineItem> {

  return [
    {
      key:    'partReference',
      label:  'Product',
      resize: false
    },
    {
      key:      'available',
      label:    'Available',
      align:    'right',
      lockFlex: true
    },
    {
      key:       'declaredValue',
      label:     'Price',
      format:    'currency',
      lockFlex:  true,
      fixedSize: 120
    },
    {
      key:       null,
      label:     'Total',
      format:    'currency',
      lockFlex:  true,
      fixedSize: 120
    },
    {
      key:       null,
      label:     null,
      sticky:    'right',
      fixedSize: 180
    }
  ]

}

</script>

<template>

  <div class="w-full h-full lg:h-fit lg:max-h-full grid grid-rows-[1fr_max-content] overflow-hidden bg-core-20 shadow-custom">

    <Transition name="view" mode="out-in">

      <div
        v-if="selectedItems.length === 0"
        class="w-full h-full p-10 border-b border-core-30"
      >

        <div class="text-center px-4 py-10 grid place-items-center gap-y-2 bg-core-30 border border-core-40 rounded-xs">
          <Icon name="portal-inventory" class="text-main" size="l" />
          <p>Add Products from Inventory to continue.</p>
        </div>

      </div>

      <Table
        v-else
        :params="{}"
        name="Selected Products"
        :flex="true"
        :schema="schema"
        :pending="false"
        :records="selectedItems"
        hide-labels="xl"
        record-map-key="id"
        :pagination="{
          total: 0,
          maxPages: 0,
          compact: true,
        }"
      >

        <template #table-head>

          <div class="truncate h-10 px-4 hidden md:flex items-center space-x-2">

            <Icon name="product" class="text-main" size="m" />

            <p class="truncate text-sm font-medium">
              Order Products
            </p>

          </div>

        </template>

        <template #default="{ reference }">

          <Product
            v-for="item in selectedItems"
            :key="item.id"
            :item="item"
            table-name="Selected Products"
            :schema="schema()"
            :table-reference="reference"
          />

        </template>

      </Table>

    </Transition>

    <div class="w-full grid md:hidden grid-cols-1">

      <Button
        :disabled="!productsStepValid"
        @click="activeStep = Flow.SHIPPING"
      >
        Confirm Products
      </Button>

    </div>

  </div>

</template>
