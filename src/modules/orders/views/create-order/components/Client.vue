<script setup lang="ts">

import { watch } from 'vue'
import { countriesList, defaultCountry } from '@/store'
import { activeStep, billing, clientStepValid, isSameInfo, shipping } from '@/modules/orders/views/create-order/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Input from '@lib/components/inputs/Input.vue'
import Select from '@lib/components/inputs/Select.vue'
import Button from '@lib/components/buttons/Button.vue'
import Checkbox from '@lib/components/inputs/Checkbox.vue'

watch( [ shipping, isSameInfo ], ( n ) => {

  if ( n[1] )
    copyShippingToBillingInfo()

}, { deep: true, immediate: true })

watch( defaultCountry, () => {

  shipping.value.country.value = defaultCountry.value

}, { deep: true, immediate: true })

function copyShippingToBillingInfo() {

  Object.keys( billing.value ).forEach(( key ) => {

    if ( shipping.value[key]?.value !== undefined )
      billing.value[key].value = shipping.value[key].value

  })

}

</script>

<template>

  <div class="w-full h-full lg:h-fit lg:max-h-full grid grid-rows-[1fr_max-content] overflow-y-auto lg:shadow-custom border-r border-core-30 lg:border-r-0">

    <div class="w-full bg-core-20">

      <div class="w-full h-10 sticky top-0 z-1 px-4 flex items-center space-x-2 bg-core-20 border-b border-core-30">

        <Icon name="shipped" size="m" class="text-main" />

        <p class="text-sm font-medium">
          Shipping Details
        </p>

      </div>

      <form class="w-full p-0 md:px-8 md:py-9 grid xl:grid-cols-2 md:gap-4" @submit.prevent>

        <Input
          v-model="shipping.firstName.value"
          v-model:valid="shipping.firstName.valid"
          data-input="First Name"
          label="First Name"
        />

        <Input
          v-model="shipping.lastName.value"
          v-model:valid="shipping.lastName.valid"
          label="Last Name"
          data-input="Last Name"
        />

        <Input
          v-model="shipping.companyName.value"
          v-model:valid="shipping.companyName.valid"
          label="Company Name"
          data-input="Company Name"
          :required="false"
        />

        <Input
          v-model="shipping.email.value"
          v-model:valid="shipping.email.valid"
          type="email"
          label="Email"
          :required="false"
        />

        <Input
          v-model="shipping.address1.value"
          v-model:valid="shipping.address1.valid"
          label="Shipping Address Line 1"
        />

        <Input
          v-model="shipping.address2.value"
          v-model:valid="shipping.address2.valid"
          label="Shipping Address Line 2"
          :required="false"
        />

        <Select
          v-model="shipping.country.value"
          v-model:valid="shipping.country.valid"
          label="Country"
          class="xl:col-span-2"
          :options="countriesList"
          :nullable="false"
        />

        <Input
          v-model="shipping.state.value"
          v-model:valid="shipping.state.valid"
          label="State / Province"
        />

        <Input
          v-model="shipping.city.value"
          v-model:valid="shipping.city.valid"
          label="City"
        />

        <Input
          v-model="shipping.zip.value"
          v-model:valid="shipping.zip.valid"
          label="Postal Code"
        />

        <Input
          v-model="shipping.phone.value"
          v-model:valid="shipping.phone.valid"
          type="phone"
          label="Phone"
          :required="false"
        />

        <div class="p-4 md:px-0 md:py-2">

          <Checkbox v-model="isSameInfo">
            <p class="text-sm">
              Billing details are same as shipping
            </p>
          </Checkbox>

        </div>

      </form>

      <div v-if="!isSameInfo" class="w-full h-10 sticky top-0 z-1 px-4 flex items-center space-x-2 bg-core-20 border-b border-t border-core-30">

        <Icon name="bill" size="m" class="text-main" />

        <p class="text-sm font-medium">
          Billing Details
        </p>

      </div>

      <form v-show="!isSameInfo" class="w-full p-0 md:px-8 md:py-9 grid xl:grid-cols-2 md:gap-4" @submit.prevent>

        <Input
          v-model="billing.firstName.value"
          v-model:valid="billing.firstName.valid"
          label="First Name"
        />

        <Input
          v-model="billing.lastName.value"
          v-model:valid="billing.lastName.valid"
          label="Last Name"
        />

        <Input
          v-model="billing.companyName.value"
          v-model:valid="billing.companyName.valid"
          label="Company Name"
          :required="false"
        />

        <Input
          v-model="billing.email.value"
          v-model:valid="billing.email.valid"
          type="email"
          label="Email"
          :required="false"
        />

        <Input
          v-model="billing.address1.value"
          v-model:valid="billing.address1.valid"
          label="Billing Address Line 1"
        />

        <Input
          v-model="billing.address2.value"
          v-model:valid="billing.address2.valid"
          label="Billing Address Line 2"
          :required="false"
        />

        <Select
          v-model="billing.country.value"
          v-model:valid="billing.country.valid"
          label="Country"
          class="xl:col-span-2"
          :options="countriesList"
          :nullable="false"
        />

        <Input
          v-model="billing.state.value"
          v-model:valid="billing.state.valid"
          label="State / Province"
        />

        <Input
          v-model="billing.city.value"
          v-model:valid="billing.city.valid"
          label="City"
        />

        <Input
          v-model="billing.zip.value"
          v-model:valid="billing.zip.valid"
          label="Postal Code"
        />

        <Input
          v-model="billing.phone.value"
          v-model:valid="billing.phone.valid"
          label="Phone"
          :required="false"
        />

      </form>

    </div>

    <div class="w-full sticky bottom-0 z-2 grid md:hidden bg-core-20">

      <Button
        data-button-mobile="next-step"
        :disabled="!clientStepValid"
        @click="activeStep++"
      >
        Confirm Client Details
      </Button>

    </div>

  </div>

</template>
