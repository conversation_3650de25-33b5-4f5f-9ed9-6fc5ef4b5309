<script setup lang="ts">

import { computed } from 'vue'
import { selectedItems } from '@/modules/orders/views/create-order/store'
import { formatCurrency } from '@lib/scripts/utils'
import { formatRecordValue } from '@lib/store/table'
import { productDetailsOptions } from '@/store'

import Row from '@lib/components/table/Row.vue'
import Cell from '@lib/components/table/Cell.vue'
import Icon from '@lib/components/blocks/Icon.vue'
import Input from '@lib/components/inputs/Input.vue'
import Button from '@lib/components/buttons/Button.vue'
import SimpleBadge from '@/components/SimpleBadge.vue'

import type { TableReference } from '@lib/types/tableTypes'
import type { CreateOrderLineItem } from '@/modules/orders/types'
import type { InventoryPanelProduct } from '@/modules/inventory/types'

interface InventoryProductWithDeclaredValue extends InventoryPanelProduct {
  declaredValue?: number
}

const props = defineProps<{
  item?:           InventoryProductWithDeclaredValue
  schema?:         TableSchema<InventoryPanelProduct>
  mobile?:         boolean
  tableName?:      string
  tableReference?: TableReference
}>()

const emits = defineEmits<{
  ( event: 'update:item', item: InventoryProductWithDeclaredValue ): void
  ( event: 'update:tableReference', payload: TableReference ): void
}>()

const product = computed({
  get: () => props.item,
  set: value => emits( 'update:item', value )
})

const reference = computed({
  get: () => props.tableReference,
  set: value => emits( 'update:tableReference', value )
})

const selectedProduct = computed(() => selectedItems.value.find( item => item.id === product.value?.id ))

function isAddedItem( itemId: number ) {
  return selectedItems.value.some( item => item.id === itemId )
}

function remapItem( item: InventoryProductWithDeclaredValue ): CreateOrderLineItem {

  return {
    id:                  item.id,
    cost:                item.price,
    available:           item.availableQuantity,
    requested:           1,
    description:         item.title,
    backordered:         0,
    partReference:       item.sku,
    declaredValue:       item.price,
    availableByFacility: item.availableByFacility,
    kitComponents:       item.kitComponents
  }

}

function addItem( item: InventoryProductWithDeclaredValue ) {
  selectedItems.value.push( remapItem( item ))
}

function removeItem( itemId: number ) {
  selectedItems.value = selectedItems.value.filter( item => item.id !== itemId )
}

function addQuantity() {

  if ( !selectedProduct.value )
    return

  selectedProduct.value.requested++

}

function removeQuantity() {

  if ( !selectedProduct.value )
    return

  if ( selectedProduct.value.requested > 1 )
    selectedProduct.value.requested--

  else
    selectedItems.value = selectedItems.value.filter( i => i.id !== selectedProduct.value.id )

}

</script>

<template>

  <Row
    height="h-24 xl:h-[4rem]"
    width="w-full xl:w-max"
    class="cursor-pointer"
    :record="product"
    :table-name="tableName"
    @row-click="() => {
      productDetailsOptions.id = product.id
      productDetailsOptions.type = 'Live'
    }"
  >

    <!-- Mobile Row -->

    <div class="w-full h-24 grid xl:hidden">

      <div class="row-span-2 flex justify-between">

        <div class="truncate h-full px-4 grid content-center">

          <p class="truncate max-w-[12rem]" data-sku-mobile="sku">
            {{ product?.sku }}
          </p>

          <p class="max-w-[12rem] truncate text-xs text-core" data-title-mobile="title">
            {{ product?.title }}
          </p>

        </div>

        <div
          v-if="isAddedItem(product?.id)"
          class="pr-4 self-center grid items-center justify-end"
        >

          <Button
            data-button-button="delete"
            mode="naked"
            type="box"
            size="s"
            class="hidden md:grid place-content-center justify-self-end text-error hover:text-error"
            :icon="{ name: 'delete', size: 'm' }"
            @click.stop="removeItem(product?.id)"
          />

          <div class="md:hidden flex items-center">

            <Button
              data-button-button="removeQty"
              size="xs"
              mode="secondary"
              type="badge"
              :icon="{
                name: 'minus',
                size: 's',
              }"
              @click.stop="removeQuantity()"
            />

            <Input
              v-model="selectedProduct.requested"
              data-input-mobile="inputQty"
              :min="1"
              mode="naked"
              size="xs"
              type="number"
              class="text-sm max-w-[5rem]"
              :required="false"
              placeholder="Qty"
            />

            <Button
              data-button-button="addQty"
              size="xs"
              mode="secondary"
              type="badge"
              :icon="{
                name: 'add',
                size: 's',
              }"
              @click.stop="addQuantity()"
            />

          </div>

        </div>

        <div v-else class="pr-4 grid items-center justify-end">

          <div v-if="product.locked" class="px-2 text-main">
            <Icon name="locked" size="s" data-icon-mobile="locked" />
          </div>

          <Button
            v-else
            data-button-button="addItem"
            mode="secondary"
            type="pill"
            class="text-sm px-2 self-center"
            size="xs"
            @click.stop="addItem(product)"
          >
            Add
          </Button>

        </div>

      </div>

      <div class="w-full h-4 px-4 flex items-center space-x-4">

        <p class="text-sm text-core">
          {{ product?.id }}
        </p>

        <div class="text-sm grow">
          <span v-if="product.locked && !product.kitComponents" class="text-core bg-core-40 px-2 py-0.5 rounded-sm" data-availability-mobile="not-available">Not Available</span>
          <span v-else-if="!product.kitComponents" class="text-core" data-availability-mobile-text="qty">QTY: {{ product?.availableQuantity }}</span>
          <SimpleBadge v-else>
            KIT
          </SimpleBadge>
        </div>

        <p class="text-sm" data-price-mobile="price">
          {{ formatCurrency(product?.price) }}
        </p>

      </div>

    </div>

    <!-- Desktop Row -->

    <Cell
      v-model:cell="reference.cells[0]"
      :reference="reference"
      :flex="true"
      :resize="false"
      class="hidden xl:flex"
    >

      <div>
        <p class="font-medium" data-sku-desktop="sku">
          {{ item.sku }}
        </p>
        <p class="text-xs text-core" data-component-desktop-title="title">
          {{ item.title }}
        </p>
      </div>

    </Cell>

    <Cell
      v-model:cell="reference.cells[1]"
      :reference="reference"
      :flex="true"
      :value="String(item.id)"
      class="hidden xl:flex"
    />

    <Cell
      v-model:cell="reference.cells[2]"
      :reference="reference"
      :flex="true"
      align="right"
      :value="String(product.availableQuantity)"
      class="hidden  xl:flex"
    >
      <span v-if="product.locked && !product.kitComponents" class="text-core text-right bg-core-40 px-2 py-0.5 rounded-sm">Not Available</span>
      <span v-else-if="!product.kitComponents" class="text-right">{{ product?.availableQuantity }}</span>
      <div v-else class="w-full flex justify-end">
        <SimpleBadge>
          KIT
        </SimpleBadge>
      </div>
    </Cell>

    <Cell
      v-model:cell="reference.cells[3]"
      :reference="reference"
      :flex="true"
      align="right"
      :value="formatRecordValue(item, schema[3])"
      class="hidden xl:flex"
    />

    <Cell
      v-model:cell="reference.cells[4]"
      :reference="reference"
      :flex="true"
      sticky="right"
      class="hidden xl:flex"
    >

      <div v-if="product.locked" class="px-2 text-main justify-self-end">
        <Icon name="locked" size="s" data-icon-desktop="locked" />
      </div>

      <Button
        v-else-if="isAddedItem(item.id)"
        data-button-desktop="removeItem"
        mode="naked"
        type="box"
        size="s"
        class="hidden md:grid place-content-center justify-self-end text-error hover:text-error"
        :icon="{ name: 'delete', size: 'm' }"
        @click.stop="removeItem(product?.id)"
      />

      <Button
        v-else
        data-button-desktop="addItem"
        mode="secondary"
        type="pill"
        class="text-sm px-2 self-center"
        size="xs"
        @click.stop="addItem(product)"
      >
        Add
      </Button>

    </Cell>

  </Row>

</template>
