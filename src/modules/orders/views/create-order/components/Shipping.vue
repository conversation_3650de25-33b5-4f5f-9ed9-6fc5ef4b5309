<script setup lang="ts">

import { activeStep, selectedShippingGroup, shipping, shippingGroups, shippingStepValid } from '@/modules/orders/views/create-order/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Radio from '@lib/components/inputs/Radio.vue'
import Button from '@lib/components/buttons/Button.vue'

</script>

<template>

  <div class="w-full h-full lg:p-8 lg:pl-4 lg:pb-0 overflow-hidden">

    <div class="w-full h-full lg:h-fit lg:max-h-full grid grid-rows-[max-content_1fr_max-content] bg-core-20 lg:shadow-custom overflow-hidden">

      <div class="w-full h-10 p-4 flex items-center space-x-2 border-b border-core-30">

        <Icon name="shipped" size="m" class="text-main" />

        <p class="font-medium">
          Carrier Details
        </p>

      </div>

      <div class="w-full h-full py-4 xl:py-8 grid content-start xl:gap-y-8 overflow-y-auto">

        <div class="px-4 xl:px-8 pb-4 xl:pb-0 grid gap-y-4 xl:gap-y-4 border-b xl:border-b-0 border-core-30">

          <div class="w-full grid grid-cols-3 xl:grid-cols-6 border border-r-0 border-b-0 rounded-sm overflow-hidden">

            <button
              v-for="group in shippingGroups"
              :key="group.name"
              :data-shipping-method="group.name"
              class="relative text-sm h-8 border-r border-b border-core-30 clicked-active-state"
              :class="{
                'hover:bg-core-10 text-main': selectedShippingGroup !== group.name,
                'bg-main hover:bg-main text-core-10': selectedShippingGroup === group.name,
              }"
              @click="selectedShippingGroup = group.name"
            >
              {{ group.name }}
            </button>

          </div>

        </div>

        <div class="xl:px-4 py-2 xl:py-0">
          <Radio
            v-model="shipping.shipType.value"
            v-model:valid="shipping.shipType.valid"
            label="Pick Service"
            :required="true"
            :options="shippingGroups.find(group => group.name === selectedShippingGroup)?.options"
          />
        </div>

      </div>

      <div class="w-full grid md:hidden">

        <Button
          data-button-mobile="confirm-carrier"
          :disabled="!shippingStepValid"
          @click="activeStep++"
        >
          Confirm Carrier
        </Button>

      </div>

    </div>

  </div>

</template>
