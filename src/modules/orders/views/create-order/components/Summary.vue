<script setup lang="ts">

import { Flow } from '@/modules/orders/views/create-order/types'
import { computed } from 'vue'
import { formatCurrency } from '@lib/scripts/utils'
import { booleanToYesNo, facilitiesList } from '@/store'
import { internalTaxOptions, orderTypes } from '@/modules/orders/store'

import {
  activeStep,
  billing,
  ddp,
  details,
  isBusinessOrder,
  orderStatus,
  orderStatuses,
  selectedItems,
  selectedShippingGroup,
  shipping,
  shippingGroups
} from '@/modules/orders/views/create-order/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Table from '@lib/components/table/Table.vue'
import Button from '@lib/components/buttons/Button.vue'
import SimpleBadge from '@/components/SimpleBadge.vue'
import CreateOrderFooter from '@/modules/orders/views/create-order/components/CreateOrderFooter.vue'

import type { CreateOrderLineItem } from '@/modules/orders/types'

function schema( lineItem?: CreateOrderLineItem ): TableSchema<CreateOrderLineItem> {
  return [
    {
      key:   'partReference',
      label: 'Product (SKU)'
    },
    {
      key:   'description',
      label: 'Title'
    },
    {
      key:      'declaredValue',
      label:    'Each',
      format:   'currency',
      lockFlex: true
    },
    {
      key:   'backordered',
      label: 'Backorder',
      align: 'right',
      value: lineItem?.kitComponents?.length > 0
        ? {
          component: SimpleBadge,
          slots:     {
            default: () => 'KIT'
          }
        }
        : lineItem?.backordered,
      class:    Number.parseInt( String(( lineItem?.backordered ))) ? 'text-red-500!' : '',
      lockFlex: true
    },
    {
      key:      'requested',
      label:    'QTY',
      align:    'right',
      lockFlex: true
    },
    {
      key:       null,
      label:     'Total',
      class:     'font-medium',
      value:     ( Number.parseFloat( String( lineItem?.declaredValue ?? 0 ))) * ( lineItem?.requested ?? 0 ),
      format:    'currency',
      fixedSize: 120
    }
  ]
}

const selectedCarrierName = computed(() =>
  shippingGroups
    .find( group => group.name === selectedShippingGroup.value )
    ?.options
    .find( option => option.id === shipping.value.shipType.value )
    ?.name
)

const orderItemsTotal = computed(() => selectedItems.value.reduce(( total, item ) =>
  total + ( Number.parseFloat( String( item.declaredValue ?? 0 )) * ( item.requested ?? 0 )), 0 ))

</script>

<template>

  <div class="w-full h-full grid grid-rows-[1fr_max-content] bg-core-20 lg:bg-core-30 border-r overflow-hidden overflow-y-auto">

    <div class="w-full h-full lg:p-8 lg:pr-4 lg:pb-0 md:grid md:grid-rows-[max-content_max-content_1fr] lg:gap-8 md:overflow-hidden md:overflow-y-auto">

      <div class="w-full h-fit lg:max-h-full grid grid-rows-[1fr] lg:shadow-xl border-r border-core-30 lg:border-r-0">
        <div class="h-10 pl-4 flex justify-between items-center border-y border-y-core-30 md:border-t-0 bg-core-20 sticky md:static top-0 z-[1]">
          <span class="font-medium flex items-center gap-3">
            <span class="text-main-70"><Icon name="portal-orders" size="m" /></span>
            <span>Order Details</span>
          </span>
          <div class="border-l border-l-core-30">
            <Button class="w-10" mode="naked" size="m" @click="activeStep = Flow.ORDER">
              <div class="w-full flex justify-center">
                <span class="text-main-70"><Icon class="color-main-70" name="edit" size="s" /></span>
              </div>
            </Button>
          </div>
        </div>

        <div class="h-fit w-full bg-core-20 p-4 lg:p-10 flex flex-col gap-1 md:inline">

          <div class="text-core w-full flex flex-col md:flex-row md:items-center md:justify-start justify-center">
            <div class="w-[12rem] text-sm">
              Client Order Reference:
            </div><span class="text-main-70">{{ details?.clientReference.value || '/' }}</span>
          </div>

          <div class="text-core w-full flex flex-col md:flex-row md:items-center md:justify-start justify-center">
            <div class="w-[12rem] text-sm">
              Purchase Order Number:
            </div><span class="text-main-70">{{ billing?.poNumber.value || '/' }}</span>
          </div>

          <div class="text-core w-full flex flex-col md:flex-row md:items-center md:justify-start justify-center">
            <div class="w-[12rem] text-sm">
              Group Name:
            </div><span class="text-main-70">{{ details?.groupName.value || '/' }}</span>
          </div>

          <div class="text-core w-full flex md:hidden flex-col md:flex-row md:items-center md:justify-start justify-center">
            <div class="w-[12rem] text-sm">
              Order State:
            </div><span class="text-main-70">{{ orderStatuses.find(s => s.id === orderStatus.value).name }}</span>
          </div>

          <div class="text-core w-full flex flex-col md:flex-row md:items-center md:justify-start justify-center">
            <div class="w-[12rem] text-sm">
              Order Type:
            </div><span class="text-main-70">{{ orderTypes.find(f => f.id === isBusinessOrder.value).name }}</span>
          </div>

          <div class="text-core w-full flex flex-col md:flex-row md:items-center md:justify-start justify-center">
            <div class="w-[12rem] text-sm">
              International Tax And Duty:
            </div><span class="text-main-70">{{ internalTaxOptions.find(o => o.id === ddp.value).name }}</span>
          </div>

          <div class="text-core w-full flex flex-col md:flex-row md:items-center md:justify-start justify-center">
            <div class="w-[12rem] text-sm">
              Shipment Facility:
            </div><span class="text-main-70">{{ facilitiesList.find(f => f.id === details.facilityRule.value)?.name }}</span>
          </div>

          <div class="text-core w-full flex flex-col md:flex-row md:items-center md:justify-start justify-center">
            <div class="w-[12rem] text-sm">
              Use Gift Invoice:
            </div><span class="text-main-70">{{ booleanToYesNo(details.isGift.value) }}</span>
          </div>

          <div v-if="details.isGift.value" class="text-core w-full flex flex-col md:flex-row md:items-center md:justify-start justify-center">
            <div class="w-[12rem] text-sm">
              Gift Message:
            </div><span class="text-main-70">{{ details.giftMessage.value || '/' }}</span>
          </div>

        </div>

      </div>

      <div class="w-full grid xl:grid-cols-2 lg:gap-8">

        <div class="bg-core-20 border-b lg:border-none lg:shadow-custom">

          <div class="w-full h-10 sticky lg:relative top-0 z-1 pl-4 flex items-center space-x-2 bg-core-20 border-y border-core-30">

            <Icon name="bill" size="m" class="text-main" />

            <p class="font-medium grow">
              Ship To
            </p>

            <div class="w-10 h-10 grid place-content-center border-l border-core-30">
              <Button
                data-button="go-to-client"
                type="box"
                mode="naked"
                size="m"
                class="text-main hover:text-main"
                :icon="{ name: 'edit', size: 's' }"
                @click="activeStep = Flow.CLIENT"
              />
            </div>

          </div>

          <div class="p-4 lg:p-[1.625rem] text-sm text-core" data-shipping="shipping">
            <p data-shipping="name">
              {{ shipping.firstName.value }} {{ shipping.lastName.value }}
            </p>
            <p data-shipping="address">
              {{ shipping.address1.value }} {{ shipping.address2.value }}
            </p>
            <p data-shipping="city">
              {{ shipping.city.value }}, {{ shipping.zip.value }}
            </p>
            <p data-shipping="country">
              {{ shipping.country.value }}
            </p>
          </div>

        </div>

        <div class="bg-core-20 border-b lg:border-none lg:shadow-custom">

          <div class="w-full h-10 sticky lg:relative top-0 z-1 pl-4 flex items-center space-x-2 bg-core-20 border-b border-core-30">

            <Icon name="bill" size="m" class="text-main" />

            <p class="font-medium grow">
              Bill To
            </p>

            <div class="w-10 h-10 grid place-content-center border-l border-core-30">
              <Button
                data-button="go-to-client-details"
                type="box"
                mode="naked"
                size="m"
                class="text-main hover:text-main"
                :icon="{ name: 'edit', size: 's' }"
                @click="activeStep = Flow.CLIENT"
              />
            </div>

          </div>

          <div class="p-4 lg:p-[1.625rem] text-sm text-core" data-billing="billing">
            <p data-billing="name">
              {{ billing.firstName.value }} {{ billing.lastName.value }}
            </p>
            <p data-billing="address">
              {{ billing.address1.value }} {{ billing.address2.value }}
            </p>
            <p data-billing="city">
              {{ billing.city.value }}, {{ billing.zip.value }}
            </p>
            <p data-billing="country">
              {{ billing.country.value }}
            </p>
          </div>

        </div>

      </div>

      <div class="md:hidden">

        <div class="w-full h-10 sticky lg:relative top-0 z-1 pl-4 xl:hidden flex items-center space-x-2 bg-core-20 border-b border-core-30">

          <Icon name="shipped" size="m" class="text-main" />

          <div class="font-medium h-full grow flex items-center">
            Carrier Details
          </div>

          <div class="w-10 h-10 grid place-content-center border-l border-core-30">
            <Button
              data-button="go-to-shipping"
              type="box"
              mode="naked"
              size="m"
              class="text-main hover:text-main"
              :icon="{ name: 'edit', size: 's' }"
              @click="activeStep = Flow.SHIPPING"
            />
          </div>

        </div>

        <div class="p-4 xl:p-8 xl:border-r border-core-30">
          <p>{{ selectedCarrierName }}</p>
        </div>

      </div>

      <div>

        <Table
          name="Order Items"
          :flex="true"
          class="lg:shadow-custom"
          :schema="schema"
          :params="{ sortBy: null }"
          :records="selectedItems"
          record-map-key="id"
        >

          <template #table-head>

            <div class="w-full h-10 pl-4 flex items-center space-x-2">

              <Icon name="product" size="m" class="text-main" />

              <p class="font-medium grow">
                Order Items
              </p>

              <div class="w-10 h-10 border-l border-core-30">
                <Button
                  data-button="go-to-items"
                  type="box"
                  mode="naked"
                  size="m"
                  class="text-main hover:text-main"
                  :icon="{ name: 'edit', size: 's' }"
                  @click="activeStep = Flow.PRODUCTS"
                />
              </div>

            </div>

          </template>

        </Table>

      </div>

    </div>

    <div class="w-full h-12 px-4 md:hidden grid items-center justify-end border-r border-t xl:border-t-0 border-core-30 bg-core-20">
      <p class="text-core" data-total="summary-total">
        Total: <span class="text-core-120 font-medium">{{ formatCurrency(orderItemsTotal) }} USD</span>
      </p>
    </div>

    <div class="md:hidden bg-core-10 sticky bottom-0 z-20">
      <CreateOrderFooter class="md:hidden" />
    </div>

  </div>

</template>
