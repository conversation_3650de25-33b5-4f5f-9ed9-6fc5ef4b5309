<script setup lang="ts">

import { Flow } from '@/modules/orders/views/create-order/types'
import { nextTick, ref } from 'vue'
import { facilitiesList } from '@/store'
import { internalTaxOptions, orderTypes } from '@/modules/orders/store'
 
import {
  activeStep,
  billing,
  clientStepValid,
  ddp,
  details,
  hasFacility,
  isBusinessOrder,
  isDraftOrder,
  orderStatus,
  orderStatuses,
  orderStepValid
} from '@/modules/orders/views/create-order/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Input from '@lib/components/inputs/Input.vue'
import Radio from '@lib/components/inputs/Radio.vue'
import Button from '@lib/components/buttons/Button.vue'
import Textbox from '@lib/components/inputs/Textbox.vue'
import Checkbox from '@lib/components/inputs/Checkbox.vue'
import OptionsGroup from '@lib/components/blocks/OptionsGroup.vue'

import type { ComponentPublicInstance } from 'vue'

const giftMessageRef  = ref<ComponentPublicInstance>( null )

</script>

<template>

  <div class="w-full h-full lg:h-fit lg:max-h-full grid grid-rows-[1fr_max-content] overflow-y-auto bg-core-20 shadow-custom">

    <div class="w-full h-full">

      <div class="w-full h-10 sticky top-0 z-1 px-4 flex items-center space-x-2 bg-core-20 border-b border-core-30">

        <Icon name="portal-orders" size="m" class="text-main" />

        <p class="text-sm font-medium">
          Order Details
        </p>

      </div>

      <form class="w-full p-0 md:px-8 md:py-9 grid xl:grid-cols-2 md:gap-4" @submit.prevent>

        <Input
          v-model="details.clientReference.value"
          v-model:valid="details.clientReference.valid"
          label="Client Order Reference"
          :required="isDraftOrder"
        />

        <Input
          v-model="billing.poNumber.value"
          v-model:valid="billing.poNumber.valid"
          label="Purchase Order Number"
          :required="false"
        />

        <Input
          v-model="details.groupName.value"
          v-model:valid="details.groupName.valid"
          label="Group Name"
          :required="false"
        />

        <div class="xl:col-span-2 grid xl:grid-cols-2 md:gap-4">

          <Radio
            v-model="orderStatus.value"
            v-model:valid="orderStatus.valid"
            :options="orderStatuses"
            :required="true"
            label="Order State"
            @update:model-value="(value) => { isDraftOrder = !!value }"
          />

          <Radio
            v-model="isBusinessOrder.value"
            v-model:valid="isBusinessOrder.valid"
            :options="orderTypes"
            :required="true"
            label="Order Type"
            @update:model-value="(value) => details.isBusinessOrder.value = !!value"
          />

          <Radio
            v-model="ddp.value"
            v-model:valid="ddp.valid"
            :options="internalTaxOptions"
            :required="true"
            label="International Tax And Duty"
            @update:model-value="(value) => details.isDdp.value = !!value"
          />

          <Radio
            v-show="!hasFacility"
            v-model="details.facilityRule.value"
            v-model:valid="details.facilityRule.valid"
            :options="facilitiesList"
            :required="true"
            label="Shipment Facility"
            :disabled="hasFacility"
          />

          <div class="xl:col-span-2 flex flex-col gap-4">

            <OptionsGroup label="Gift Options">

              <Checkbox
                v-model="details.isGift.value"
                @update:model-value="async (value) => {
                  if (value){
                    await nextTick()
                    giftMessageRef?.$el?.scrollIntoView({ behavior: 'smooth' })
                  }
                }"
              >
                Use Gift Invoice
              </Checkbox>

            </OptionsGroup>

            <Textbox
              v-if="details.isGift.value"
              ref="giftMessageRef"
              v-model="details.giftMessage.value"
              v-model:valid="details.giftMessage.valid"
              label="Optional Gift Message (up to size of box)"
              :required="false"
            />

          </div>

        </div>

      </form>

    </div>

    <div class="w-full sticky bottom-0 grid md:hidden bg-core-10">

      <Button
        data-button-mobile="next-step"
        :disabled="!clientStepValid || !orderStepValid"
        class="lg:col-start-2 xl:col-start-3"
        @click="activeStep = Flow.PRODUCTS"
      >
        Confirm Customer Details
      </Button>

    </div>

  </div>

</template>
