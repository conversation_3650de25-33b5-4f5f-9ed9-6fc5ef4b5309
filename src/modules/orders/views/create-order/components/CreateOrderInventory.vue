<script setup lang="ts">

import { Flow } from '@/modules/orders/views/create-order/types'
import { facilitiesList } from '@/store'
import { computed, onBeforeUnmount, onMounted, ref } from 'vue'
import { activeStep, inventoryItems, productsStepValid, selectedFacility, selectedItems } from '@/modules/orders/views/create-order/store'

import Table from '@lib/components/table/Table.vue'
import Button from '@lib/components/buttons/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import Products from '@/modules/orders/views/create-order/components/Products.vue'
import CreateOrderInventoryProduct from '@/modules/orders/views/create-order/components/CreateOrderInventoryProduct.vue'

import type { InventoryPanelProduct, InventoryParams } from '@/modules/inventory/types'

const props = defineProps<{
  total?:    number
  params?:   InventoryParams
  pending?:  boolean
  maxPages?: number
}>()

const emits = defineEmits<{
  ( event: 'update:params', payload: InventoryParams ): void
}>()

const viewParams = computed({
  get: () => props.params,
  set: value => emits( 'update:params', value )
})

function schema(): TableSchema<InventoryPanelProduct> {

  return [
    {
      key:    'sku',
      label:  'Product',
      resize: false
    },
    {
      key:      'id',
      label:    'Reference',
      resize:   false,
      lockFlex: true
    },
    {
      key:      'availableQuantity',
      label:    'Available',
      align:    'right',
      resize:   false,
      sortKey:  'available',
      lockFlex: true
    },
    {
      key:      'price',
      label:    'Price',
      format:   'currency',
      resize:   false,
      lockFlex: true
    },
    {
      key:       null,
      label:     null,
      resize:    false,
      sticky:    'right',
      fixedSize: 80
    }
  ]

}

const openSelectedProducts = ref<boolean>( false )

const key = ref( false )

function checkResize( e ) {

  if ( e.target.innerWidth > 768 ) {

    if ( !key.value )
      key.value = true

    openSelectedProducts.value = false
  }

  else {

    if ( key.value )
      key.value = false

  }

}

onMounted(() => {
  window.addEventListener( 'resize', checkResize )
})

onBeforeUnmount(() => {
  window.removeEventListener( 'resize', checkResize )
})

</script>

<template>

  <div class="w-full h-full grid grid-rows-[1fr_max-content] overflow-hidden bg-core-20 shadow-custom">

    <Table
      :key="String(key)"
      v-model:params="viewParams"
      name="Products"
      :flex="true"
      :schema="schema"
      :pending="pending"
      :records="inventoryItems"
      hide-labels="xl"
      record-map-key="id"
      :search="{
        enabled: true,
        searchKey: 'sku',
      }"
      :pagination="{
        total,
        maxPages,
        compact: true,
      }"
    >
      <template #table-head>

        <div class="truncate h-10 px-4 flex items-center">
          <p class="truncate text-sm font-medium">
            Facility: <span class="text-main" data-facility="facility">[{{ facilitiesList.find(f => f.id === selectedFacility)?.name }}]</span>
          </p>
        </div>

      </template>

      <template #table-neck>

        <div class="w-full h-10 pl-4 flex md:hidden items-center border-b">

          <p class="text-main font-medium grow">
            Products <span class="text-xs text-core-10 font-normal bg-main px-1.5 py-0.5 rounded-xs">{{ selectedItems.length }}</span>
          </p>

          <Button
            data-button-mobile="open-selected"
            mode="naked"
            type="box"
            class="text-main hover:text-main"
            :icon="{ name: 'chevron-right', size: 'm' }"
            @click="openSelectedProducts = true"
          />

        </div>

      </template>

      <template #default="{ reference }">

        <CreateOrderInventoryProduct
          v-for="record in inventoryItems"
          :key="record.id"
          :item="record"
          :schema="schema()"
          table-name="Products"
          :table-reference="reference"
        />

      </template>

    </Table>

    <div class="w-full grid md:hidden grid-cols-1">

      <Button
        data-button-button="next-step"
        :disabled="!productsStepValid"
        @click="activeStep = Flow.SHIPPING"
      >
        Confirm Products
      </Button>

    </div>

    <Sidebar :open="openSelectedProducts" name="selected-items">

      <div class="w-full h-full grid grid-rows-[max-content_1fr] overflow-hidden">

        <div class="w-full h-14 grid grid-cols-[3.5rem_1fr_3.5rem] md:hidden items-center">

          <Button
            data-button-mobile="close-selected"
            :data-button-selected-count="selectedItems.length"
            mode="naked"
            type="box"
            :icon="{ name: 'chevron-left', size: 'm' }"
            @click="openSelectedProducts = false"
          />

          <p class="text-main text-center font-medium" data-text-selected="selected-products">
            Selected Products <span class="text-xs text-core-10 font-normal bg-main px-1.5 py-0.5 rounded-xs">{{ selectedItems.length }}</span>
          </p>

        </div>

        <Products @close="openSelectedProducts = false" />

      </div>

    </Sidebar>

  </div>

</template>
