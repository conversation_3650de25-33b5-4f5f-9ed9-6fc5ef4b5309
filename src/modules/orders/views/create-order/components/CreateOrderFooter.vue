<script setup lang="ts">

import { useRouter } from 'vue-router'
import { ref, watch } from 'vue'
import { cacheDetails } from '@/store'
import { convertObjectToPlain } from '@lib/scripts/utils'
import { setNotificationOptions } from '@lib/store/snackbar'
import { createDraftOrder, createDraftOrderLineItem, createLiveOrder } from '@/modules/orders/store'

import {
  billing,
  details,
  isDraftOrder,
  orderCreationPending,
  resetCreateOrderState,
  selectedFacility,
  selectedItems,
  shipping,
  shippingStepValid
} from '@/modules/orders/views/create-order/store'

import Button from '@lib/components/buttons/Button.vue'

import type { BackorderRule, CreateDraftOrderPostData, CreateLiveOrderPostData, CreateOrderLineItem } from '@/modules/orders/types'

const router              = useRouter()
const canShipEntireOrder  = ref<boolean>( false )
const canShipPartialOrder = ref<boolean>( false )

function checkOrderAvailability( items: CreateOrderLineItem[], selectedFacility: string ) {

  const flatAvailability = items.map(( item ) => {

    if ( item?.kitComponents?.length > 0 ) {
      return item.kitComponents.every(( component ) => {
        const facility = component.availableByFacility?.find( f => f.facilityCode === selectedFacility )
        return facility?.unitsAvailable >= item?.requested
      })
    }

    const facility = item?.availableByFacility?.find( f => f.facilityCode === selectedFacility )
    return facility?.unitsAvailable >= item?.requested

  })

  return flatAvailability

}

watch( selectedFacility, () => {

  const availability = checkOrderAvailability( selectedItems.value, selectedFacility.value )

  canShipPartialOrder.value = availability?.some( Boolean ) && availability.some( i => !i )

  canShipEntireOrder.value = availability?.every( Boolean )

}, { immediate: true })

const backorderRule = ref<BackorderRule>( null )

function createPartialOrder() {
  backorderRule.value = 'PARTIALSHIP'
  createNewOrder( backorderRule.value )
}

function createOnHoldOrder() {
  backorderRule.value = 'HOLDORDER'
  createNewOrder( backorderRule.value )
}

function createReleasedOrder() {
  backorderRule.value = 'NOBACKORDER'
  createNewOrder( backorderRule.value )
}

function createBackorderedOrder() {
  backorderRule.value = 'BACKORDER'
  createNewOrder( backorderRule.value )
}

async function handleDraftOrder( backorder: BackorderRule ) {

  const draftOrderDetails = convertObjectToPlain( details.value )

  const data: CreateDraftOrderPostData = {
    ...draftOrderDetails,
    backorderRule:  backorder,
    holdForRelease: backorder === 'HOLDORDER',
    billing:        { ...convertObjectToPlain ( billing.value ), paymentType: draftOrderDetails.paymentType },
    shipping:       convertObjectToPlain( shipping.value ),
    facilityCode:   draftOrderDetails.facilityRule,
  }
  const { error, payload } = await createDraftOrder( data )

  if ( !error ) {

    const requests = []

    selectedItems.value.forEach(( item ) => {

      const mappedItem = {
        sku:               item.partReference,
        cost:              item.declaredValue,
        description:       item.description,
        partReference:     item.partReference,
        requestedQuantity: item.requested,
      }

      requests.push( createDraftOrderLineItem( payload.id, mappedItem ))

    })

    await Promise.all( requests )

    setNotificationOptions({ message: 'Draft order is created successfully.' })

    await router.push({ name: 'Draft Order Details', params: { orderId: payload.id } })
    resetCreateOrderState()

  }

}

async function handleLiveOrder( backorder: BackorderRule ) {

  const data: CreateLiveOrderPostData = {
    ...convertObjectToPlain( details.value ),
    billing:       { ...convertObjectToPlain ( billing.value ), paymentType: details.value.paymentType.value },
    shipping:      convertObjectToPlain( shipping.value ),
    backorderRule: backorder,
    lineItems:     selectedItems.value.map( item => ({
      sku:               item.partReference,
      cost:              item.declaredValue,
      description:       item.description,
      partReference:     item.partReference,
      requestedQuantity: item.requested,
    })),
    holdForRelease: backorder === 'HOLDORDER',
    customValues:   null
  }

  const { error, payload } = await createLiveOrder( data )

  if ( !error ) {

    setNotificationOptions({ message: 'Live order is created successfully.' })
    cacheDetails( 'order', payload )

    await router.push({ name: 'Live Order Details', params: { orderId: payload.id } })
    resetCreateOrderState()

  }

}

async function createNewOrder( backorder: BackorderRule ) {

  orderCreationPending.value = true

  if ( isDraftOrder.value )
    await handleDraftOrder( backorder )

  else
    await handleLiveOrder( backorder )

  orderCreationPending.value = false

}

defineExpose({
  canShipEntireOrder,
  canShipPartialOrder,
  backorderRule,
  createReleasedOrder,
  createOnHoldOrder,
  createNewOrder,
  createBackorderedOrder,
  shippingStepValid,
  createPartialOrder,
})

</script>

<template>

  <div
    class="w-full h-full grid"
    :class="{
      'grid-cols-2': canShipEntireOrder || (!canShipEntireOrder && !canShipPartialOrder),
      'grid-cols-2 md:grid-cols-3': canShipPartialOrder,
    }"
  >

    <div class="w-full h-full grid">
      <Button
        mode="secondary"
        data-button="hold-order"
        size="auto"
        class="h-full min-h-12 px-4"
        :disabled="!shippingStepValid || (orderCreationPending && backorderRule !== 'HOLDORDER')"
        :pending="orderCreationPending && backorderRule === 'HOLDORDER'"
        @click="createOnHoldOrder"
      >
        <p class="text-sm xl:text-base">
          Hold Order
        </p>
      </Button>
    </div>

    <div v-if="canShipPartialOrder" class="w-full grid">

      <Button
        data-button="create-partial"
        mode="secondary"
        size="auto"
        class="h-full min-h-12 px-4"
        :disabled="!shippingStepValid || (orderCreationPending && backorderRule !== 'PARTIALSHIP')"
        :pending="orderCreationPending && backorderRule === 'PARTIALSHIP'"
        @click="createPartialOrder"
      >
        <p class="text-sm xl:text-base">
          Ship In Stock Now
        </p>
      </Button>

    </div>

    <div
      class="w-full grid"
      :class="{
        'col-span-2 md:col-span-1': canShipPartialOrder,
      }"
    >

      <Button
        v-if="canShipEntireOrder"
        data-button="release-order"
        size="auto"
        class="h-full min-h-12 px-4"
        :disabled="!shippingStepValid || (orderCreationPending && backorderRule !== 'NOBACKORDER')"
        :pending="orderCreationPending && backorderRule === 'NOBACKORDER'"
        @click="createReleasedOrder"
      >
        <p class="text-sm xl:text-base">
          Release Order <span class="hidden md:inline">for Shipping</span>
        </p>
      </Button>

      <Button
        v-if="canShipPartialOrder"
        data-button="backorder-entire"
        size="auto"
        class="h-full min-h-12 px-4"
        :disabled="!shippingStepValid || (orderCreationPending && backorderRule !== 'BACKORDER')"
        :pending="orderCreationPending && backorderRule === 'BACKORDER'"
        @click="createBackorderedOrder"
      >
        <p class="text-sm xl:text-base">
          Backorder Entire Order
        </p>
      </Button>

      <Button
        v-if="!canShipEntireOrder && !canShipPartialOrder"
        data-button="backorder"
        size="auto"
        class="h-full min-h-12 px-4"
        :disabled="!shippingStepValid || (orderCreationPending && backorderRule !== 'BACKORDER')"
        :pending="orderCreationPending && backorderRule === 'BACKORDER'"
        @click="createBackorderedOrder"
      >
        <p class="text-sm xl:text-base">
          Backorder
        </p>
      </Button>

    </div>

  </div>

</template>
