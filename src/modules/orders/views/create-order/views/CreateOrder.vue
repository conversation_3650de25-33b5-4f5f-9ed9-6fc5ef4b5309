<script setup lang="ts">

import { Flow } from '@/modules/orders/views/create-order/types'
import { confirm } from '@lib/store/confirm'
import { getProducts } from '@/modules/inventory/store'
import { inventoryParams } from '@/modules/inventory/routes'
import { useRoute, useRouter } from 'vue-router'
import { defaultFacility, shippingMethods } from '@/store'
import { computed, onMounted, reactive, ref } from 'vue'
import { blockPageUpdate, formatCurrency, sanitizeQueryParams, viewSetup } from '@lib/scripts/utils'

import Icon from '@lib/components/blocks/Icon.vue'
import Order from '@/modules/orders/views/create-order/components/Order.vue'
import Loader from '@lib/components/blocks/Loader.vue'
import Client from '@/modules/orders/views/create-order/components/Client.vue'
import Button from '@lib/components/buttons/Button.vue'
import FlowBar from '@lib/components/utils/FlowBar.vue'
import Summary from '@/modules/orders/views/create-order/components/Summary.vue'
import Products from '@/modules/orders/views/create-order/components/Products.vue'
import Shipping from '@/modules/orders/views/create-order/components/Shipping.vue'
import CreateOrderFooter from '@/modules/orders/views/create-order/components/CreateOrderFooter.vue'
import CreateOrderInventory from '@/modules/orders/views/create-order/components/CreateOrderInventory.vue'

import type { FlowStep } from '@lib/types/flowBarTypes'

import {
  activeStep,
  clientStepValid,
  details,
  hasFacility,
  inventoryItems,
  isDraftOrder,
  mapAvailableByFacility,
  orderCreationPending,
  orderStepValid,
  productsStepValid,
  resetCreateOrderState,
  selectedItems,
  shippingGroups,
  shippingStepValid,
  storedStep
} from '@/modules/orders/views/create-order/store'

import type { InventoryParams } from '@/modules/inventory/types'

const route           = useRoute()
const total           = ref<number>( 0 )
const router          = useRouter()
const params          = reactive<InventoryParams>({ ...inventoryParams, ...sanitizeQueryParams( route.query ) })
const pending         = ref<boolean>( false )
const maxPages        = ref<number>( 0 )
const initialPending  = ref<boolean>( true )

const flowSteps = computed<FlowStep<Flow>[]>(() => [
  {
    id:            1,
    isGroup:       true,
    stepName:      'Ship/Bill Details',
    groupName:     'Customer Details',
    inProgress:    activeStep.value === Flow.CLIENT,
    stepComplete:  clientStepValid.value,
    groupComplete: clientStepValid.value && orderStepValid.value,
  },
  {
    id:            2,
    disabled:      !clientStepValid.value,
    stepName:      'Order Details',
    groupName:     'Order Details',
    inProgress:    activeStep.value === Flow.ORDER,
    stepComplete:  clientStepValid.value && orderStepValid.value,
    groupComplete: orderStepValid.value,
  },
  {
    id:            3,
    isGroup:       true,
    stepName:      'Order Products',
    disabled:      !clientStepValid.value || !orderStepValid.value,
    groupName:     'Order Items',
    inProgress:    activeStep.value === Flow.PRODUCTS,
    stepComplete:  clientStepValid.value && orderStepValid.value && productsStepValid.value,
    groupComplete: clientStepValid.value && orderStepValid.value && productsStepValid.value,
  },
  {
    id:            4,
    isGroup:       true,
    stepName:      'Shipping Details',
    disabled:      !clientStepValid.value || !orderStepValid.value || !productsStepValid.value,
    groupName:     'Shipping Method',
    inProgress:    activeStep.value === Flow.SHIPPING,
    stepComplete:  clientStepValid.value && orderStepValid.value && productsStepValid.value && shippingStepValid.value,
    groupComplete: clientStepValid.value && orderStepValid.value && productsStepValid.value && shippingStepValid.value,
  },
  {
    id:            5,
    disabled:      !clientStepValid.value || !orderStepValid.value || !productsStepValid.value || !shippingStepValid.value,
    stepName:      'Summary',
    groupName:     'Summary',
    inProgress:    activeStep.value === Flow.SUMMARY,
    stepComplete:  clientStepValid.value && orderStepValid.value && productsStepValid.value && shippingStepValid.value,
    groupComplete: clientStepValid.value && orderStepValid.value && productsStepValid.value && shippingStepValid.value,
  }
] )

const activeFlow = computed(() =>
  activeStep.value === Flow.CLIENT || activeStep.value === Flow.ORDER
    ? Flow.CLIENT
    : activeStep.value === Flow.PRODUCTS
      ? Flow.PRODUCTS
      : Flow.SHIPPING
)

function sortShippingMethodsInGroups( methods: typeof shippingMethods.value ) {

  if ( !methods )
    return

  shippingGroups.forEach( group => group.options = [] )

  methods.forEach(( method ) => {

    const prefix = method.name.split( ' ' )[0]?.toLowerCase()

    if ( [ 'ups', 'usps', 'fedex', 'dhl' ].includes( prefix )) {

      shippingGroups.find( group => group.name.toLowerCase() === prefix )?.options.push({
        id:   method.value,
        name: method.name
      })

    }

    else if ( method.value.match( 'FLATRATE' )) {
      shippingGroups.find( group => group.name === 'Flat Rate' )?.options.push({
        id:   method.value,
        name: method.name
      })
    }

    else if ( method.value.match( 'PS_ALF.EHUB.USPS_GROUND_ADVANTAGE' ) || method.value.match( 'PS_ALF.EHUB.USPS_PRIORITY' )) {
      shippingGroups.find( group => group.name === 'USPS' )?.options.push({
        id:   method.value,
        name: method.name
      })
    }

    else {
      shippingGroups.find( group => group.name === 'Other' )?.options.push({
        id:   method.value,
        name: method.name
      })
    }

  })

}

async function getInventoryItems( viewParams: InventoryParams ) {

  pending.value = true

  const { payload } = await getProducts({ ...viewParams, pageSize: 50 })

  total.value = payload?.totalRows ?? 0
  maxPages.value = payload?.totalPages ?? 0
  inventoryItems.value = payload?.items ?? []

  mapAvailableByFacility()

  pending.value = false
  initialPending.value = false

}

const orderItemsTotal = computed(() => selectedItems.value.reduce(( total, item ) => total + ( Number.parseFloat( String( item.declaredValue ?? 0 )) * ( item.requested ?? 0 )), 0 ))

onMounted(() => {

  // Sort the shipping methods into groups.

  sortShippingMethodsInGroups( shippingMethods.value )

  // If the client has a default facility, use that as the facility rule value.
  // This should happened once when the create order view is mounted.

  details.value.facilityRule.value = defaultFacility.value === 'ALL'
    ? null
    : defaultFacility.value

  // If there is a default facility, set the hasFacility flag to true.

  hasFacility.value = defaultFacility.value !== 'ALL'

})

viewSetup(
  'Create Order',
  params,
  router,
  getInventoryItems
)

defineExpose({
  route,
  activeFlow,
  initialPending,
  orderStepValid,
  clientStepValid,
  productsStepValid
})

</script>

<template>

  <div
    class="w-full h-full grid grid-rows-[max-content_1fr] overflow-hidden"
    :class="{ 'pointer-events-none': orderCreationPending }"
  >

    <div class="w-full h-12 flex items-center justify-between bg-core-120">

      <div class="w-[3rem] md:w-[13.2rem] h-full pl-4 md:px-4 flex items-center">

        <p class="text-xs md:text-base font-medium hidden md:flex">

          <span

            class="rounded-[0.063rem] flex items-center gap-2 px-3 py-px" :class="{
              'text-warning bg-warning/30': isDraftOrder,
              'text-core-10': !isDraftOrder,
            }"
          >

            <Icon
              v-if="isDraftOrder"
              data-title-icon="create-order-icon"
              name="draft-order"
              size="s"
            />
            <span data-title="create-order-title" class="flex">
              {{ isDraftOrder ? 'Create Draft Order' : 'Create Live Order' }}
            </span>

          </span>

        </p>

      </div>

      <FlowBar
        :steps="flowSteps"
        @set-active-step="(step) => {
          activeStep = step.id
        }"
      />

      <div class="w-[3rem] md:max-w-[13.188rem] lg:w-full h-full flex justify-end">

        <Button
          type="box"
          mode="naked"
          size="auto"
          class="w-12 h-full text-core-20 hover:text-core-20"
          :icon="{ name: 'close', size: 'm' }"
          @click="() => confirm({
            header: 'Are you sure you want to exit order creation?',
            description: 'Exiting now will result in the loss of all entered data.',
            action: () => {
              blockPageUpdate()
              $router.push('/orders/live')
              resetCreateOrderState()
            },
          })"
        />

      </div>

    </div>

    <Transition name="view" mode="out-in">

      <div v-if="initialPending" class="w-full h-full flex items-center justify-center space-x-4">

        <Loader name="" />

      </div>

      <div v-else class="w-full h-full overflow-hidden">

        <!-- Flow :: Desktop -->

        <div class="w-full h-full hidden md:block overflow-hidden bg-core-30">

          <Transition name="route" mode="out-in">

            <div
              v-if="activeFlow === Flow.CLIENT"
              class="w-full h-full grid grid-rows-[1fr_max-content] overflow-hidden"
            >

              <div class="w-full h-full lg:p-8 lg:pb-0 grid grid-cols-2 lg:gap-8 overflow-hidden">

                <Client data-component-desktop="client" />
                <Order data-component-desktop="order" />

              </div>

              <div class="w-full h-12 hidden md:flex items-center justify-end bg-core-20 border-t border-core-30">

                <Button
                  :disabled="!clientStepValid || !orderStepValid"
                  class="h-full px-6"
                  size="auto"
                  data-button-desktop="confirm-customer-details"
                  @click="activeStep = Flow.PRODUCTS"
                >
                  Confirm Customer Details
                </Button>

              </div>

            </div>

            <div
              v-else-if="activeFlow === Flow.PRODUCTS"
              class="w-full h-full grid grid-rows-[1fr_max-content] overflow-hidden"
            >

              <div class="w-full lg:p-8 lg:pb-0 grid grid-cols-2 lg:gap-8 overflow-hidden">

                <CreateOrderInventory
                  v-model:params="params"
                  data-component-desktop="inventory"
                  :total="total"
                  :pending="pending"
                  :max-pages="maxPages"
                />

                <Products data-component-desktop="products" />

              </div>

              <div class="w-full h-12 hidden md:flex items-center justify-end bg-core-20 border-t border-core-30">

                <Button
                  data-button-desktop="confirm-products"
                  :disabled="!productsStepValid"
                  class="h-full px-6"
                  size="auto"
                  @click="activeStep = Flow.SHIPPING"
                >
                  Confirm Products
                </Button>

              </div>

            </div>

            <div
              v-else-if="activeFlow === Flow.SHIPPING"
              class="w-full h-full grid grid-rows-[1fr_max-content] overflow-hidden"
            >

              <div class="w-full lg:pb-0 grid grid-cols-2 overflow-hidden">

                <Summary data-component-desktop="summary" />
                <Shipping data-component-desktop="shipping" />

              </div>

              <div class="w-full h-12 hidden md:grid lg:grid-cols-2 grid-cols-[1fr_max-content] bg-core-20">

                <div
                  class="w-full h-12 px-4 grid items-center justify-end sticky bottom-0 z-30 border-r border-t xl:border-t-0 border-core-30 bg-core-20"
                >
                  <p class="text-core" data-total-desktop="total">
                    Total: <span class="text-core-120 font-medium">{{ formatCurrency(orderItemsTotal) }} USD</span>
                  </p>
                </div>

                <CreateOrderFooter data-component-desktop="footer" class="hidden md:grid" />

              </div>

            </div>

          </Transition>

        </div>

        <!-- Micro Flow :: Small Screens -->

        <div class="w-full h-full relative md:hidden overflow-hidden">

          <Transition
            :name="activeStep > storedStep ? 'mobile-view-right' : 'mobile-view-left'"
            @enter="() => storedStep = activeStep"
          >

            <Client v-if="activeStep === Flow.CLIENT" data-component-mobile="client" />

            <Order v-else-if="activeStep === Flow.ORDER" data-component-mobile="order" />

            <CreateOrderInventory
              v-else-if="activeStep === Flow.PRODUCTS"
              v-model:params="params"
              :total="total"
              :pending="pending"
              :max-pages="maxPages"
              data-component-mobile="inventory"
            />

            <Shipping v-else-if="activeStep === Flow.SHIPPING" data-component-mobile="shipping" />

            <Summary v-else-if="activeStep === Flow.SUMMARY" data-component-mobile="summary" />

          </Transition>

        </div>

      </div>

    </Transition>

  </div>

</template>
