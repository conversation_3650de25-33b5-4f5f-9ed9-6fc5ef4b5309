import { reactive, ref, watch } from 'vue'

import type { ShipmentParams } from '@/modules/orders/types'

/**
 * isOrderRpc
 *
 * This flag is used for the order details request.
 * It is set to true when the line items are updated.
 * If this is true, the order details request is sent with an isRpc flag.
 */

export const isOrderRpc = ref<boolean>( false )

/**
 * This timeout is used to reset the isOrderRpc flag after 30 seconds.
 */

const orderRpcTimeout = ref<NodeJS.Timeout>( null )

watch( isOrderRpc, ( n ) => {

  if ( n ) {
    clearTimeout( orderRpcTimeout.value )
    orderRpcTimeout.value = setTimeout(() => isOrderRpc.value = false, 30000 )
  }

})

/**
 * areItemsRpc
 *
 * This flag is used for the line items request.
 * It is set to true when some of the order actions are triggered ex: Void Order or Release for shipping.
 * If this is true, the line items request is sent with an isRpc flag.
 */

export const areItemsRpc = ref<boolean>( false )

/**
 * This timeout is used to reset the areItemsRpc flag after 30 seconds.
 */

const itemsRpcTimeout = ref<NodeJS.Timeout>( null )

watch( areItemsRpc, ( n ) => {

  if ( n ) {
    clearTimeout( itemsRpcTimeout.value )
    itemsRpcTimeout.value = setTimeout(() => areItemsRpc.value = false, 30000 )
  }

})

/**
 * isCached
 *
 * This flag is used for the line items request.
 * It is set to true when the order details are cached.
 * If this is true, the line items request is sent with an isRpc flag.
 */

export const isCached = ref<boolean>( false )

/**
 * hasPackageShipments
 *
 * This flag is used for the packaged shipments.
 * It is set to true if the order has package shipments.
 * If this is true, each product row will have an option to filter the package shipments by sku.
 */

export const hasPackageShipments = ref<boolean>( false )

/**
 * packageShipmentParams
 *
 * This are the params for the package shipments table.
 * They are set from both live order Products and Shipments component.
 * The sku is set when the user filters the package shipments by sku.
 */
export const packageShipmentParams = reactive<ShipmentParams>({ page: 1, pageSize: 10, sku: null })
