<script setup lang="ts">

import { confirm } from '@lib/store/confirm'
import { Guard, guard } from '@/plugins/guard'
import { computed, ref } from 'vue'
import { onClickOutside } from '@vueuse/core'
import { useRoute, useRouter } from 'vue-router'
import { handleCachedDetails } from '@/store'
import { setNotificationOptions } from '@lib/store/snackbar'
import { formatCurrency, saveFile, useBreakpoint, viewSetup } from '@lib/scripts/utils'
import { areItemsRpc, isCached, isOrderRpc, packageShipmentParams } from '@/modules/orders/views/live-details/store'
import { exportLiveOrder, exportPackingSlip, getOrder, holdOrder, releaseOrder, unholdOrder, unpostOrder, voidOrder } from '@/modules/orders/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Loader from '@lib/components/blocks/Loader.vue'
import Events from '@/modules/orders/views/live-details/components/Events.vue'
import Button from '@lib/components/buttons/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import Details from '@/modules/orders/views/live-details/components/Details.vue'
import Products from '@/modules/orders/views/live-details/components/Products.vue'
import Shipments from '@/modules/orders/views/live-details/components/Shipments.vue'

import type { IconName } from '@lib/store/icon'
import type { OrderDetails } from '@/modules/orders/types'

interface orderView {
  id:      number
  name:    string
  icon:    IconName
  disable: boolean
}

const order           = ref<OrderDetails>( null )
const route           = useRoute()
const router          = useRouter()
const orderId         = computed<number>(() => Number.parseInt( route.params.orderId as string ))
const hasOptions      = computed(() => globalActionsOptions( order.value ).filter( option => !option.hidden ).length > 0 )
const activeView      = ref<number>( 1 )
const storedView      = ref<number>( 1 )
const openActions     = ref<boolean>( false )
const openInvoice     = ref<boolean>( false )
const canEditOrder    = computed(() => order.value?.status === 'On Hold' || order.value?.status?.startsWith( 'Backorder' ))
const canBePartial    = ref<boolean>( false )
const exportPending   = ref<boolean>( false )
const canBeReleased   = ref<boolean>( false )
const eventsElement   = ref<HTMLElement>( null )
const packSlipPending = ref<boolean>( false )
const openOrderEvents = ref<boolean>( false )

const viewOrderViews = computed<orderView[]>(() => [
  {
    id:      1,
    name:    'Details',
    icon:    'details',
    disable: false
  },
  {
    id:      2,
    name:    'Products',
    icon:    'product',
    disable: false
  },
  {
    id:      3,
    name:    'Shipments',
    icon:    'portal-inventory',
    disable: false
  }
] )

async function getOrderDetails( orderId: number ) {

  openActions.value = false

  const { error, payload, status } = await getOrder( orderId, isOrderRpc.value, false )

  const cachedOrder = handleCachedDetails( status, 'order', orderId )

  order.value = cachedOrder.data ?? payload ?? null
  isCached.value = !!cachedOrder?.data

  return {
    error,
    status: cachedOrder.status,
    payload
  }

}

async function exportOrder( orderId: number ) {

  exportPending.value = true

  const { error, payload } = await exportLiveOrder( orderId )

  if ( !error )
    saveFile( payload, `Order_${orderId}.xlsx` )

  exportPending.value = false

}

async function exportPackSlip( orderId: number ) {

  packSlipPending.value = true

  const { error, payload } = await exportPackingSlip( orderId )

  if ( !error )
    window.open( payload.url, '_blank' )

  packSlipPending.value = false

}

function exportInvoice() {
  openInvoice.value = true
}

const exportOptions = computed<DropListOption[]>(() => [
  {
    id:     1,
    name:   'Excel',
    action: () => exportOrder( orderId.value )
  },
  {
    id:          2,
    name:        'Commercial Invoice',
    action:      () => exportInvoice(),
    disabled:    !order.value?.isInternational || order.value?.status !== 'Shipped',
    description: !order.value?.isInternational || order.value?.status !== 'Shipped' ? 'Available only for international shipped orders.' : null
  },
  {
    id:          3,
    name:        'Packing Slip',
    action:      () => exportPackSlip( orderId.value ),
    disabled:    !order.value?.packSlip,
    description: !order.value?.packSlip ? 'Available once a packing slip is generated.' : null
  }
] )

function globalActionsOptions( currentOrder: OrderDetails ): DropListOption[] {

  const hasWriteAccess = guard( 'Order.Write' )

  if ( hasWriteAccess ) {

    return [
      {
        id:     1,
        name:   'Void Order',
        color:  'error',
        hidden: [ 'Void', 'At Warehouse', 'Shipped' ].some( status => currentOrder?.status ? currentOrder.status.startsWith( status ) : false ),
        action: () => confirm({
          header:      'Void Order',
          description: 'Are you sure you want to void this order?',
          action:      () => voidOrder( currentOrder.id ),
          onSuccess:   () => {
            isOrderRpc.value = true
            areItemsRpc.value = true
            updateView( 'silent' )
            setNotificationOptions({ message: 'Order voided successfully.' })
          }
        }),
      },
      {
        id:     2,
        name:   'Set On Hold',
        hidden: [ 'Void', 'On Hold', 'Backorder (On Hold)', 'At Warehouse', 'Shipped' ].some( status => currentOrder?.status ? currentOrder.status.startsWith( status ) : false ),
        action: () => confirm({
          header:      'Hold Order',
          description: 'Are you sure you want to hold this order?',
          action:      () => holdOrder( currentOrder.id ),
          onSuccess:   () => {
            isOrderRpc.value = true
            areItemsRpc.value = true
            updateView( 'silent' )
            setNotificationOptions({ message: 'Order set on hold successfully.' })
          }
        }),
      },
      {
        id:     3,
        name:   'Unpost Order',
        color:  'warning',
        hidden: [ 'Void', 'On Hold', 'Backorder', 'Shipped' ].some( status => currentOrder?.status ? currentOrder.status.startsWith( status ) : false ),
        action: () => confirm({
          header:      'Unpost Order',
          description: `${order.value.pickStatus !== 0 ? 'Unposting will incur an additional change order fee.' : ''} Are you sure you want to unpost this order?`,
          action:      () => unpostOrder( currentOrder.id ),
          onSuccess:   () => {
            isOrderRpc.value = true
            areItemsRpc.value = true
            updateView( 'silent' )
            setNotificationOptions({ message: 'Order unposted successfully.' })
          }
        }),
      },
      {
        id:     4,
        name:   'Remove Hold - Backorder All',
        hidden: [ 'Void', 'Backorder (Active)', 'At Warehouse', 'Shipped' ].some( status => currentOrder?.status ? currentOrder.status.startsWith( status ) : false ),
        action: () => confirm({
          header:      'Unhold Order',
          description: 'Are you sure you want to unhold this order?',
          action:      () => unholdOrder( currentOrder.id ),
          onSuccess:   () => {
            isOrderRpc.value = true
            areItemsRpc.value = true
            updateView( 'silent' )
            setNotificationOptions({ message: 'Order unheld successfully.' })
          }
        }),
      },
      {
        id:     5,
        name:   'Release Order',
        hidden: [ 'Void', 'At Warehouse', 'Shipped' ].some( status => currentOrder?.status ? currentOrder.status.startsWith( status ) || !canBeReleased.value : false ),
        action: () => confirm({
          header:      'Release Order',
          description: 'Are you sure you want to release this order?',
          action:      () => releaseOrder( currentOrder.id, false ),
          onSuccess:   () => {
            isOrderRpc.value = true
            areItemsRpc.value = true
            updateView( 'silent' )
            setNotificationOptions({ message: 'Order released successfully.' })
          }
        }),
      },
      {
        id:     6,
        name:   'Release Order - Ship Available Items',
        hidden: [ 'Void', 'At Warehouse', 'Shipped' ].some( status => currentOrder?.status ? currentOrder.status.startsWith( status ) || !canBePartial.value : false ),
        action: () => confirm({
          header:      'Release Order',
          description: 'Are you sure you want to release this order?',
          action:      () => releaseOrder( currentOrder.id, true ),
          onSuccess:   () => {
            isOrderRpc.value = true
            areItemsRpc.value = true
            updateView( 'silent' )
            setNotificationOptions({ message: 'Order released successfully.' })
          }
        }),
      }
    ]

  }

  else { return [] }

}

function closeLiveOrderDetails() {
  router.push({ name: 'Live Orders' })
  packageShipmentParams.sku = null
}

function closeSidebarOnClickOutside() {
  openOrderEvents.value = false
}

onClickOutside( eventsElement, closeSidebarOnClickOutside, { ignore: [ '.ignore-outside-events' ] })

const { silent, pending, updateView } = viewSetup(
  null,
  null,
  router,
  () => getOrderDetails( orderId.value ),
  orderId
)

defineExpose({
  order,
  route,
  pending,
  getOrder,
  activeView,
  openActions,
  canBePartial,
  getOrderDetails,
  openOrderEvents,
  globalActionsOptions
})

</script>

<template>

  <div class="w-full h-full relative grid grid-rows-[max-content_1fr_max-content] md:grid-rows-[max-content_1fr] bg-core-30 overflow-hidden">

    <!-- Order Details Header -->

    <div class="w-full h-[5.5rem] md:h-12 grid grid-rows-[max-content_max-content] md:grid-rows-1 grid-cols-[1fr_max-content] md:grid-cols-[max-content_1fr_max-content] bg-core-120">

      <!-- Order Id -->

      <div class="h-12 px-4 flex items-center border-b md:border-r border-core-100">

        <p v-if="pending" class="text-core-10">
          Loading Order Details ...
        </p>

        <p v-else class="text-core-10 font-medium">
          Live Order: <span class="text-main-50">[{{ order?.clientReference }}]</span>
        </p>

      </div>

      <!-- Other Header Options -->

      <div class="h-10 md:h-12 col-span-2 md:col-span-1 row-start-2 md:row-start-auto flex justify-end">

        <div
          v-if="!pending"
          class="truncate text-core-10 font-medium h-full px-4 flex items-center space-x-2 md:border-r border-core-100"
        >
          <p class="truncate">
            <span class="hidden lg:inline-block">Status: </span>
            <span
              class="truncate"
              :class="{
                'text-error': order?.status.startsWith('Void'),
                'text-success': order?.status.startsWith('Shipped'),
                'text-data1-120': order?.status.startsWith('Backorder (Active)'),
                'text-main-50': order?.status.startsWith('At Warehouse'),
                'text-warning': order?.status?.toLowerCase().includes('on hold'),
              }"
            > [{{ order?.status }}]</span>
          </p>

        </div>

        <div class="grow" />

        <div class="h-full hidden md:block md:border-l border-core-100">

          <Button
            mode="naked"
            size="auto"
            class="h-full px-4 flex items-center space-x-4 text-core-10"
            :pending="exportPending || packSlipPending"
            :options="exportOptions"
          >

            <template #default="{ active }">

              <Icon name="export" size="m" />
              <p>Export</p>
              <Icon name="chevron-down" size="m" :class="{ 'rotate-180': active }" />

            </template>

          </Button>

        </div>

        <div class="w-10 md:w-12 h-full md:border-l border-core-100">

          <Button
            v-tooltip="{ content: 'Events and Comments' }"
            data-button="open-order-events"
            mode="naked"
            type="box"
            size="auto"
            :is-active="openOrderEvents"
            class="w-full h-full text-core-10 ignore-outside-events"
            :class="{
              'bg-core-100': openOrderEvents,
            }"
            :icon="{
              name: 'portal-orders',
              size: 'm',
            }"
            @click="openOrderEvents = !openOrderEvents"
          />

        </div>

        <div class="w-10 h-full md:hidden border-core-100">

          <Button
            mode="naked"
            type="box"
            size="auto"
            class="w-full h-full text-core-10"
            :icon="{
              name: 'dots-vertical',
              size: 'm',
            }"
            @click="openActions = true"
          />

        </div>

      </div>

      <!-- Close Button -->

      <div class="h-12 border-b md:border-l border-core-100">

        <Button
          mode="naked"
          type="box"
          size="auto"
          class="w-12 h-full text-core-10"
          :icon="{
            name: 'close',
            size: 'm',
          }"
          @click="closeLiveOrderDetails"
        />

      </div>

    </div>

    <!-- Order Details :: Desktop -->

    <div v-if="pending" class="w-full h-full hidden md:flex justify-center space-x-4 p-10">
      <Loader />
    </div>

    <div v-else-if="useBreakpoint('md')" class="w-full h-full grid grid-cols-[1fr_max-content] overflow-hidden">

      <div class="w-full h-full grid grid-rows-[1fr_max-content] overflow-hidden">

        <div class="w-full h-full grid grid-rows-[minmax(max-content_1fr)_minmax(max-content_1fr)] content-start gap-6 overflow-hidden md:px-4 md:pt-6 md:pb-6">

          <Products
            v-model:active-view="activeView"
            v-model:open-invoice="openInvoice"
            :order
            :pending
            :editable="canEditOrder"
            @global-actions-update="(actions) => {
              canBePartial = actions.partial
              canBeReleased = actions.released
            }"
            @update-order-details="() => {
              isOrderRpc = true
              updateView('silent')
            }"
          />

          <Shipments
            :order-id="order.id"
          />

        </div>

        <div class="w-full h-10 px-4 flex items-center gap-x-4 justify-end bg-core-10 border-t border-r border-core-30">

          <p class="font-medium flex items-center space-x-2">
            <span class="text-core">Ship Fee: </span>
            <Icon v-if="silent" name="loading" size="m" class="text-main" />
            <span v-else>{{ formatCurrency(order?.shipHandlingFee) }}</span>
          </p>

          <p class="font-medium flex items-center space-x-2">
            <span class="text-core">Total Cost: </span>
            <Icon v-if="silent" name="loading" size="m" class="text-main" />
            <span v-else>{{ formatCurrency(order?.total) }}</span>
          </p>

        </div>

      </div>

      <div
        class="h-full grid grid-rows-[1fr_max-content] overflow-hidden transition-[width]"
        :class="{
          'w-0': pending,
          'w-[26rem]': !pending,
        }"
      >

        <Details
          :order
          :update-pending="silent"
          @open-actions="openActions = true"
          @update-order="(payload) => {
            order = payload
          }"
        />

      </div>

    </div>

    <!-- Order Details :: Mobile -->

    <div v-if="pending" class="w-full h-full flex md:hidden justify-center space-x-4 p-10">
      <Loader />
    </div>

    <div v-else-if="!useBreakpoint('md')" class="w-full h-full overflow-hidden">

      <Transition :name="activeView > storedView ? 'mobile-view-right' : 'mobile-view-left'" @enter="() => storedView = activeView">

        <Details
          v-if="activeView === 1"
          :order
          :update-pending="silent"
          @update-order="(payload) => {
            order = payload
          }"
        />

        <Products
          v-else-if="activeView === 2"
          v-model:active-view="activeView"
          v-model:open-invoice="openInvoice"
          :order
          :pending
          :editable="canEditOrder"
          @global-actions-update="(actions) => {
            canBePartial = actions.partial
            canBeReleased = actions.released
          }"
          @update-order-details="() => {
            isOrderRpc = true
            updateView('silent')
          }"
        />

        <Shipments
          v-else-if="activeView === 3"
          :order-id="order.id"
        />

      </Transition>

    </div>

    <!-- Order Mobile Actions -->

    <Sidebar
      :dim="true"
      :strict="true"
      :open="openActions"
      position="bottom"
      custom-class="md:hidden"
      @close="openActions = false"
    >

      <div class="w-full bg-core-10">

        <div class="w-full h-12 pl-4 sticky top-0 z-1 flex items-center bg-core-20 border-b border-core-30">

          <p class="text-sm font-medium grow">
            Available Options
          </p>

          <Button
            mode="naked"
            type="box"
            size="auto"
            class="w-12 h-full min-w-[3rem]"
            :icon="{
              name: 'close',
              size: 'm',
            }"
            @click="openActions = false"
          />

        </div>

        <div class="p-4 grid gap-2">

          <Guard scope="Order.Write">

            <Button
              v-for="option in globalActionsOptions(order).filter(option => !option.hidden)"
              :key="option.id"
              type="pill"
              :mode="option?.color ?? 'primary'"
              class="px-4 flex items-center justify-between"
              @click="option.action"
            >

              <p>{{ option.name }}</p>
              <Icon name="chevron-right" />

            </Button>

            <div v-if="hasOptions" class="py-2">
              <hr class="border-core-30">
            </div>

          </Guard>

          <Button
            type="pill"
            mode="secondary"
            class="px-4 flex items-center justify-between"
            :pending="exportPending"
            @click="exportOrder(orderId)"
          >

            <p>Export Excel</p>
            <Icon name="export" size="m" />

          </Button>

          <Button
            type="pill"
            mode="secondary"
            class="px-4 flex items-center justify-between"
            :pending="packSlipPending"
            :disabled="order?.status !== 'Shipped'"
            @click="() => exportPackSlip(orderId)"
          >

            <p>Export Packing Slip</p>
            <Icon name="export" size="m" />

          </Button>

          <Button
            type="pill"
            mode="secondary"
            class="px-4 flex items-center justify-between"
            :disabled="!order?.isInternational || order?.status !== 'Shipped'"
            @click="exportInvoice"
          >

            <p>Export Commercial Invoice</p>
            <Icon name="export" size="m" />

          </Button>

        </div>

      </div>

    </Sidebar>

    <!-- Order Desktop Actions -->

    <Sidebar
      :dim="true"
      :strict="true"
      :open="openActions"
      custom-class="hidden md:block"
      @close="openActions = false"
    >

      <div class="w-full min-w-[26rem] h-full bg-core-10">

        <div class="w-full h-12 pl-4 sticky top-0 z-1 flex items-center bg-core-20 border-b border-core-30">

          <p class="text-sm font-medium grow">
            Available Options
          </p>

          <Button
            mode="naked"
            type="box"
            size="auto"
            class="w-12 h-full min-w-[3rem]"
            :icon="{
              name: 'close',
              size: 'm',
            }"
            @click="openActions = false"
          />

        </div>

        <div class="p-4 grid gap-2">

          <Button
            v-for="option in globalActionsOptions(order).filter(option => !option.hidden)"
            :key="option.id"
            type="pill"
            data-button-desktop="actions"
            :mode="option?.color ?? 'primary'"
            class="px-4 flex items-center justify-between"
            @click="option.action"
          >

            <p>{{ option.name }}</p>
            <Icon name="chevron-right" />

          </Button>

        </div>

      </div>

    </Sidebar>

    <!-- Order Details Footer :: Mobile -->

    <div class="w-full h-12 z-1 md:hidden grid grid-cols-3 bg-core-10 border-t border-core-30">

      <Button
        v-for="view in viewOrderViews"
        :key="view.id"
        :icon="{
          name: view.icon,
          size: 'm',
        }"
        mode="naked"
        type="box"
        size="auto"
        class="w-full h-full"
        :class="{ 'text-main bg-core-20': activeView === view.id }"
        :disabled="view.disable"
        @click="activeView = view.id"
      />

    </div>

    <!-- Events -->

    <div
      ref="eventsElement"
      class="absolute top-[5.5rem] md:top-12 z-10 right-0 md:right-[26rem] overflow-hidden h-[calc(100%-5.5rem)] md:h-[calc(100%-3rem)] transition-[width] bg-core-10"
      :class="{
        'w-0': !openOrderEvents,
        'w-full md:w-[21rem]': openOrderEvents,
      }"
    >

      <Transition name="view" appear>

        <div v-if="openOrderEvents" class="w-full h-full">
          <Events :order-id="order?.id" />
        </div>

      </Transition>

    </div>

  </div>

</template>
