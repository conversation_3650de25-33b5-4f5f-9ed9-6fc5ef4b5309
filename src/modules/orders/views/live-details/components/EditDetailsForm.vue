<script setup lang="ts">

import { validateModel } from '@lib/scripts/inputValidation'
import { setNotificationOptions } from '@lib/store/snackbar'
import { internalTaxOptions, updateOrder } from '@/modules/orders/store'
import { countriesList, shippingMethodsList } from '@/store'
import { computed, nextTick, onMounted, reactive, ref } from 'vue'
import { convertObjectToPlain, convertObjectToValidatable } from '@lib/scripts/utils'

import Icon from '@lib/components/blocks/Icon.vue'
import Input from '@lib/components/inputs/Input.vue'
import Select from '@lib/components/inputs/Select.vue'
import Button from '@lib/components/buttons/Button.vue'
import Textbox from '@lib/components/inputs/Textbox.vue'
import Checkbox from '@lib/components/inputs/Checkbox.vue'
import OptionsGroup from '@lib/components/blocks/OptionsGroup.vue'

import type { ComponentPublicInstance } from 'vue'
import type { OrderBilling, OrderDetails, OrderShipping, UpdateOrderDetailsPostData } from '@/modules/orders/types'

const props = defineProps<{
  order:   OrderDetails
  pending: boolean
}>()

const emits = defineEmits<{
  ( event: 'close' ): void
  ( event: 'update', payload: OrderDetails ): void
}>()

type DetailsModel = Omit<UpdateOrderDetailsPostData, 'shipping' | 'billing'>

const details: DetailsModel = {
  aesItn:                       props.order.aesItn,
  isGift:                       props.order.isGift,
  carrier:                      props.order.shipping.method,
  avsOverride:                  props.order.avsOverride,
  shipAccount:                  props.order.shipAccount,
  giftMessage:                  props.order.giftMessage,
  dutyAndTaxes:                 props.order.dutyAndTaxes,
  declaredValue:                props.order.insuranceAmount,
  shipPaymentType:              props.order.shipPaymentType,
  carrierReferenceNumber:       props.order.shipping.method,
  requestSaturdayDelivery:      props.order.requestSaturdayDelivery,
  requestSignatureConfirmation: props.order.requestSignatureConfirmation
}

const billing: OrderBilling   = { ...props.order.billing }
const shipping: OrderShipping = { ...props.order.shipping }

const sameModels      = ref<boolean>( true )
const dutyAndTaxes    = ref<number>( props.order.dutyAndTaxes ? 1 : 0 )
const giftMessageRef  = ref<ComponentPublicInstance>( null )

function mapShippingToBilling() {
  Object.keys( billingModel ).forEach(( key ) => {
    if ( key in shippingModel )
      billingModel[key].value = shippingModel[key]?.value
  })
}

function toggleSameInfo( isSame: boolean ) {

  if ( isSame )
    mapShippingToBilling()

}

function areBillingAndShippingSame() {

  if (

    billingModel.zip.value === shippingModel.zip.value
    && billingModel.city.value === shippingModel.city.value
    && billingModel.state.value === shippingModel.state.value
    && billingModel.email.value === shippingModel.email.value
    && billingModel.phone.value === shippingModel.phone.value
    && billingModel.country.value === shippingModel.country.value
    && billingModel.address1.value === shippingModel.address1.value
    && billingModel.address2.value === shippingModel.address2.value
    && billingModel.lastName.value === shippingModel.lastName.value
    && billingModel.firstName.value === shippingModel.firstName.value
    && billingModel.companyName.value === shippingModel.companyName.value

  ) {
    return true
  }

  return false

}

const detailsModel  = reactive<Validatable<DetailsModel>>( convertObjectToValidatable( details, null, [ 'carrier', 'declaredValue', 'aesItn', 'requestSaturdayDelivery', 'requestSignatureConfirmation', 'avsOverride', 'shipAccount', 'dutyAndTaxes', 'isGift', 'giftMessage' ] ))
const billingModel  = reactive<Validatable<OrderBilling>>( convertObjectToValidatable( billing, null, [ 'companyName', 'address2', 'email', 'phone', 'fax', 'paymentType' ] ))
const shippingModel = reactive<Validatable<OrderShipping>>( convertObjectToValidatable( shipping, null, [ 'companyName', 'address2', 'email', 'phone', 'fax', 'method', 'methodReference' ] ))

const canUpdate = computed(() => validateModel( detailsModel ) && validateModel( billingModel ) && validateModel( shippingModel ))

const carrierBillingOptions: DropListOption[] = [
  {
    id:   'Delivered Duty/Tax Unpaid',
    name: 'Delivered Duty/Tax Unpaid'
  },
  {
    id:   'Freight Collect',
    name: 'Freight Collect'
  },
  {
    id:   'Prepaid',
    name: 'Prepaid'
  },
  {
    id:   'Third Party Billing',
    name: 'Third Party Billing'
  }
]

const updatePending = defineModel<boolean>( 'pending' )

async function updateOrderDetails() {

  updatePending.value = true

  if ( sameModels.value )
    mapShippingToBilling()

  const orderDetails = convertObjectToPlain<UpdateOrderDetailsPostData>( detailsModel as Validatable<UpdateOrderDetailsPostData> )
  const orderBilling = convertObjectToPlain( billingModel )
  const orderShipping = convertObjectToPlain( shippingModel )

  orderDetails.carrier = shippingMethodsList.value.find( method => method.id === orderDetails.carrierReferenceNumber )?.name ?? null
  orderDetails.billing = orderBilling
  orderDetails.shipping = orderShipping
  orderDetails.dutyAndTaxes = dutyAndTaxes.value === 1

  const { error, payload } = await updateOrder( props.order.id, orderDetails )

  if ( !error ) {
    setNotificationOptions({ message: 'Order details are updated successfully.' })
    emits( 'update', payload )
  }

  updatePending.value = false

}

onMounted(() => {

  // Set the carrier reference number to the id of the carrier

  detailsModel.carrierReferenceNumber.value = shippingMethodsList.value.find( method => method.name === detailsModel.carrierReferenceNumber.value )?.id ?? null

  // Check if billing and shipping are the same

  sameModels.value = areBillingAndShippingSame()

})

const carrierBillingTooltip = 'Normally is "Prepaid". Select "Third Party Billing" and provide your account information below to bill a third party. If you have made arrangements with One World to have your packages billed to your own account by default, you do not need to fill out this section. Only select third party billing when the package should be billed to someone other than you or OWD. Note for International Orders: You must use a USA account is number if you wish to bill Third Party and ship to another country. You can bill Freight Collect if the account number that of the receiving party.'
const shipperAccountTooltip = 'Required if "Third Party Billing" is chosen above.'

defineExpose({
  detailsModel,
  billingModel,
  shippingModel,
  canUpdate,
  updateOrderDetails,
  updateOrder
})

</script>

<template>

  <div class="w-full h-full grid grid-rows-[1fr_max-content] overflow-hidden">

    <form
      class="w-full md:w-[46rem] md:max-w-[46rem] md:px-2 grid gap-4 content-start overflow-hidden overflow-y-auto"
      :class="{
        'pointer-events-none': updatePending,
      }"
      @submit.prevent
    >

      <!-- Shipping Details Section -->

      <section class="md:px-4 grid md:grid-cols-2 md:gap-4" data-section="shipping">

        <div class="h-12 sticky top-0 z-1 px-4 md:px-2 flex items-center md:col-span-2 bg-core-20 border-b md:border-b-0 border-core-30">
          <p class="text-xs text-core uppercase">
            Shipping Details
          </p>
        </div>

        <Input
          v-model="shippingModel.firstName.value"
          v-model:valid="shippingModel.firstName.valid"
          label="First Name"
        />

        <Input
          v-model="shippingModel.lastName.value"
          v-model:valid="shippingModel.lastName.valid"
          label="Last Name"
        />

        <Input
          v-model="shippingModel.companyName.value"
          v-model:valid="shippingModel.companyName.valid"
          label="Company Name"
          class="md:col-span-2"
          :required="false"
        />

        <Input
          v-model="shippingModel.address1.value"
          v-model:valid="shippingModel.address1.valid"
          label="Address Line 1"
        />

        <Input
          v-model="shippingModel.address2.value"
          v-model:valid="shippingModel.address2.valid"
          label="Address Line 2"
          :required="false"
        />

        <Select
          v-model="shippingModel.country.value"
          v-model:valid="shippingModel.country.valid"
          :options="countriesList"
          label="Country"
        />

        <Input
          v-model="shippingModel.state.value"
          v-model:valid="shippingModel.state.valid"
          label="State"
        />

        <Input
          v-model="shippingModel.city.value"
          v-model:valid="shippingModel.city.valid"
          label="City"
        />

        <Input
          v-model="shippingModel.zip.value"
          v-model:valid="shippingModel.zip.valid"
          label="Postal Code"
        />

        <Input
          v-model="shippingModel.email.value"
          v-model:valid="shippingModel.email.valid"
          label="Email"
          :required="false"
        />

        <Input
          v-model="shippingModel.phone.value"
          v-model:valid="shippingModel.phone.valid"
          label="Phone"
          :required="false"
        />

        <div class="h-12 md:h-auto px-4 md:px-0 flex items-center md:col-span-2">
          <Checkbox
            v-model="sameModels"
            class="md:col-span-2"
            @update:model-value="(e: boolean) => toggleSameInfo(e)"
          >
            Billing details are same as shipping
          </Checkbox>
        </div>

      </section>

      <!-- Billing Details Section -->

      <section v-show="!sameModels" class="md:px-4 grid md:grid-cols-2 md:gap-4" data-section="billing">

        <div class="h-12 sticky top-0 z-1 px-4 md:px-2 flex items-center md:col-span-2 bg-core-20 border-b md:border-b-0 border-core-30">
          <p class="text-xs text-core uppercase">
            Billing Details
          </p>
        </div>

        <Input
          v-model="billingModel.firstName.value"
          v-model:valid="billingModel.firstName.valid"
          label="First Name"
        />

        <Input
          v-model="billingModel.lastName.value"
          v-model:valid="billingModel.lastName.valid"
          label="Last Name"
        />

        <Input
          v-model="billingModel.companyName.value"
          v-model:valid="billingModel.companyName.valid"
          label="Company Name"
          class="md:col-span-2"
          :required="false"
        />

        <Input
          v-model="billingModel.address1.value"
          v-model:valid="billingModel.address1.valid"
          label="Address Line 1"
        />

        <Input
          v-model="billingModel.address2.value"
          v-model:valid="billingModel.address2.valid"
          label="Address Line 2"
          :required="false"
        />

        <Select
          v-model="billingModel.country.value"
          v-model:valid="billingModel.country.valid"
          :options="countriesList"
          label="Country"
        />

        <Input
          v-model="billingModel.state.value"
          v-model:valid="billingModel.state.valid"
          label="State"
        />

        <Input
          v-model="billingModel.city.value"
          v-model:valid="billingModel.city.valid"
          label="City"
        />

        <Input
          v-model="billingModel.zip.value"
          v-model:valid="billingModel.zip.valid"
          label="Postal Code"
        />

        <Input
          v-model="billingModel.email.value"
          v-model:valid="billingModel.email.valid"
          label="Email"
          :required="false"
        />

        <Input
          v-model="billingModel.phone.value"
          v-model:valid="billingModel.phone.valid"
          label="Phone"
          :required="false"
        />

      </section>

      <!-- Rest of the Shipping Details Section -->

      <section class="md:px-4 pb-4 grid md:grid-cols-2 md:gap-4" data-section="delivery">

        <div class="h-12 sticky top-0 z-1 px-4 md:px-2 flex items-center md:col-span-2 bg-core-20 border-b md:border-b-0 border-core-30">
          <p class="text-xs text-core uppercase">
            Delivery Details
          </p>
        </div>

        <Select
          v-model="detailsModel.carrierReferenceNumber.value"
          v-model:valid="detailsModel.carrierReferenceNumber.valid"
          label="Shipping Method"
          :nullable="false"
          :options="shippingMethodsList"
        />

        <Select
          v-model="detailsModel.shipPaymentType.value"
          v-model:valid="detailsModel.shipPaymentType.valid"
          label="Carrier Billing"
          :required="false"
          :options="carrierBillingOptions"
        >

          <template #suffix>

            <div class="h-full w-10 grid place-content-center">
              <Icon
                v-tooltip.left="{ content: carrierBillingTooltip }"
                name="info"
                size="m"
                class="text-main"
              />
            </div>

          </template>

        </Select>

        <Input
          v-show="detailsModel.shipPaymentType.value === 'Third Party Billing'"
          v-model="detailsModel.shipAccount.value"
          v-model:valid="detailsModel.shipAccount.valid"
          label="Shipper Account ID"
          :required="detailsModel.shipPaymentType.value === 'Third Party Billing'"
          class="md:col-span-2"
        >

          <template #suffix>

            <div class="h-full w-10 grid place-content-center">
              <Icon
                v-tooltip.left="{ content: shipperAccountTooltip }"
                name="info"
                size="m"
                class="text-main"
              />
            </div>

          </template>

        </Input>

        <Input
          v-model="detailsModel.declaredValue.value"
          v-model:valid="detailsModel.declaredValue.valid"
          type="currency"
          label="Declared Value"
          :required="false"
        />

        <Input
          v-model="detailsModel.aesItn.value"
          v-model:valid="detailsModel.aesItn.valid"
          label="AES ITN"
          :required="false"
        />

        <Select
          v-model="dutyAndTaxes"
          class="md:col-span-2"
          label="International Tax And Duty"
          :nullable="false"
          :required="true"
          :options="internalTaxOptions"
        />

        <div class="h-12 md:h-auto px-4 md:px-0 flex items-center md:col-span-2">
          <Checkbox
            v-model="detailsModel.requestSaturdayDelivery.value"
            label="Request Saturday Delivery"
            class="md:col-span-2"
          >

            <span> Request Saturday Delivery</span>

            <template #suffix>
              <Icon
                v-tooltip="{ content: 'Check this box to force Saturday shipping. This option only applies to services that deliver on specific days (overnight, 2-day, 3-day, etc.) where the normal delivery date is a Saturday. Carriers will assign an additional cost for this option. If the normal delivery date is a Saturday and this option is not checked, delivery will occur the following Monday.' }"
                name="info"
                size="m"
                class="text-main ml-2"
              />
            </template>

          </Checkbox>
        </div>

        <div class="h-12 md:h-auto px-4 md:px-0 flex items-center md:col-span-2">
          <Checkbox
            v-model="detailsModel.requestSignatureConfirmation.value"
            label="Request Signature Confirmation"
            class="md:col-span-2"
          >

            <span>Request Signature Confirmation</span>

            <template #suffix>
              <Icon
                v-tooltip="{ content: 'Check this box to force Signature Confirmation for delivery. This option only applies to services that offer signature services - this request will be ignored for other services. Carriers will assign an additional cost for this option.' }"
                name="info"
                size="m"
                class="text-main ml-2"
              />
            </template>

          </Checkbox>
        </div>

        <div class="h-12 md:h-auto px-4 md:px-0 flex items-center md:col-span-2">
          <Checkbox
            v-model="detailsModel.avsOverride.value"
            label="Bypass AVS"
            class="md:col-span-2"
          >

            <span> {{ $t('orders.details.bypassAvsCheckboxText') }}</span>

            <template #suffix>
              <Icon
                v-tooltip="{ content: 'We will ship using address above with no changes.' }"
                name="info"
                size="m"
                class="text-main ml-2"
              />
            </template>

          </Checkbox>
        </div>

        <div class="md:col-span-2 flex flex-col gap-4">

          <OptionsGroup label="Gift Options">

            <Checkbox
              v-model="detailsModel.isGift.value"
              label="Use Gift Invoice"
              @update:model-value="async (value) => {
                if (value){
                  await nextTick()
                  giftMessageRef?.$el?.scrollIntoView({ behavior: 'smooth' })
                }
              }"
            >
              Use Gift Invoice
            </Checkbox>

          </OptionsGroup>

          <Textbox
            v-if="detailsModel.isGift.value"
            ref="giftMessageRef"
            v-model="detailsModel.giftMessage.value"
            v-model:valid="detailsModel.giftMessage.valid"
            label="Optional Gift Message (up to size of box)"
            :required="false"
          />

        </div>

      </section>

    </form>

    <div class="w-full h-12 sticky top-0 z-1 grid grid-cols-2 bg-core-20 border-t border-core-30">

      <Button
        data-button="cancel"
        size="auto"
        mode="naked"
        :disabled="updatePending"
        @click="emits('close')"
      >
        {{ $t('global.button.cancel') }}
      </Button>

      <Button
        v-model:pending="updatePending"
        data-button="update"
        size="auto"
        :disabled="!canUpdate"
        @click="updateOrderDetails"
      >
        Save Changes
      </Button>

    </div>

  </div>

</template>
