<script setup lang="ts">

import { formatDate } from '@lib/scripts/utils'
import { Guard, guard } from '@/plugins/guard'
import { computed, ref } from 'vue'
import { appMode, facilitiesList } from '@/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import RadioButton from '@lib/components/buttons/RadioButton.vue'
import DetailsCard from '@/components/DetailsCard.vue'
import BillingDetails from '@/modules/orders/views/live-details/components/BillingDetails.vue'
import ShippingDetails from '@/modules/orders/views/live-details/components/ShippingDetails.vue'
import EditDetailsForm from '@/modules/orders/views/live-details/components/EditDetailsForm.vue'

import type { OrderDetails } from '@/modules/orders/types'

const props = defineProps<{
  order?:         OrderDetails
  updatePending?: boolean
}>()

const emits = defineEmits<{
  ( eventName: 'openActions' ): void
  ( eventName: 'updateOrder', payload: OrderDetails ): void
}>()

const noOptions            = computed(() => props.order?.status.startsWith( 'Void' ) || props.order?.status.startsWith( 'Shipped' ))
const toggleInfo           = ref( 1 )
const editDetails          = ref<boolean>( false )
const canEditDetails       = computed(() => props.order?.status === 'On Hold' || props.order?.status?.startsWith( 'Backorder' ))
const updateDetailsPending = ref( false )

function getDate() {
  const dateTimeCreated = formatDate( props.order.created, 'MMM DD, YYYY [at] HH:mm', false )
  const orderType = props?.order?.type ? ` via ${props.order.type}` : ''
  return dateTimeCreated + orderType
}

defineExpose({
  noOptions
})

</script>

<template>

  <div class="w-full h-full grid grid-rows-1 md:grid-rows-[1fr_max-content] bg-core-10 overflow-hidden">

    <div class="w-full h-full overflow-hidden overflow-y-auto">

      <div class="w-full h-10 sticky top-0 z-1 pl-4 flex items-center space-x-3 bg-core-20 border-b border-core-30">

        <Icon name="details" size="m" class="text-main" />

        <p class="text-sm font-medium grow">
          Details
        </p>

      </div>

      <div v-if="order" class="text-sm grid grid-cols-2 gap-px bg-core-30 border-b border-core-30">

        <DetailsCard
          v-if="appMode === 'ADMIN'"
          label="Order ID"
          class="col-span-2"
          :content="order.id ? order.id : '/'"
          :pending="updatePending"
        />

        <DetailsCard
          label="Client Reference Number"
          class="col-span-2"
          :content="order.clientReference ? order.clientReference : '/'"
          :pending="updatePending"
        />

        <DetailsCard
          label="OWD Reference Number"
          class="col-span-2"
          :content="order.owdReference ? order.owdReference : '/'"
          :pending="updatePending"
        />

        <DetailsCard
          label="Group Name"
          class="col-span-2"
          :content="order.groupName ? order.groupName : '/'"
          :pending="updatePending"
        />

        <DetailsCard
          label="Date Created"
          class="col-span-2"
          :content="order.created ? getDate() : new Date().toString()"
          :pending="updatePending"
        />

        <DetailsCard
          :to="order.backOrderId ? `/orders/live/details/${order?.backOrderId}` : null"
          label="Back Order Reference"
          class="col-span-2"
          :content="order.backOrderId ? order.backOrderReference : '/'"
          :pending="updatePending"
        />

        <DetailsCard
          label="Facility"
          :content="order.facilityCode ? facilitiesList?.find(f => f.id === order.facilityCode)?.name : '/'"
          :pending="updatePending"
        />

        <DetailsCard
          label="Type"
          :content="order.isBusinessOrder ? 'Business' : 'Consumer'"
          :pending="updatePending"
        />

        <DetailsCard
          label="PO Number"
          :content="order.poNumber || '/'"
          :pending="updatePending"
        />

        <DetailsCard
          label="Use Gift Invoice"
          :content="order.isGift ? 'Yes' : 'No'"
          :pending="updatePending"
        />

        <DetailsCard
          v-if="order.isGift"
          label="Gift Message"
          class="col-span-2"
          :content="order.giftMessage || '/'"
          :pending="updatePending"
        />

        <DetailsCard
          :label="$t('orders.details.notes')"
          class="col-span-2"
          :content="order.notes ? order.notes : '/'"
          :pending="updatePending"
        />

      </div>

      <div class="w-full h-10 sticky top-0 z-1 pl-4 flex items-center space-x-3 bg-core-20 border-b border-core-30">

        <div class="h-full flex items-center grow">

          <RadioButton
            v-model="toggleInfo"
            :options="[
              {
                id: 1,
                label: 'Ship To',
              },
              {
                id: 2,
                label: 'Bill To',
              },
            ]"
          />

        </div>

        <Guard scope="Order.Write">

          <Button
            v-if="canEditDetails"
            mode="naked"
            size="auto"
            class="h-full px-4 flex items-center space-x-3"
            @click="editDetails = true"
          >

            <p class="text-sm">
              Edit
            </p>

            <Icon name="edit" size="s" class="text-main" />

          </Button>

        </Guard>

      </div>

      <Transition v-if="order" name="source-form" mode="out-in">

        <ShippingDetails
          v-if="toggleInfo === 1"
          :shipping="order?.shipping"
          :update-pending="updatePending"
        />

        <BillingDetails
          v-else
          :billing="order?.billing"
          :update-pending="updatePending"
        />

      </Transition>

      <!-- Edit Details Form -->

      <Sidebar
        :dim="true"
        :open="editDetails"
        :strict="true"
        @close="editDetails = false"
      >

        <div class="w-full h-full grid grid-rows-[max-content_1fr]">

          <div class="w-full h-12 sticky top-0 z-1 flex items-center bg-core-20 border-b border-core-30">

            <div class="h-full px-4 flex items-center space-x-3 grow border-r border-core-30">

              <Icon name="edit" size="m" class="text-main" />

              <p class="text-sm font-medium">
                Edit Details <span class="text-main">[{{ order?.clientReference }}]</span>
              </p>

            </div>

            <Button
              type="box"
              size="auto"
              mode="naked"
              class="w-12 h-full min-w-[3rem]"
              :icon="{
                name: 'close',
                size: 'm',
              }"
              :disabled="updateDetailsPending"
              @click="editDetails = false"
            />

          </div>

          <EditDetailsForm
            v-model:pending="updateDetailsPending"
            :order="order"
            @close="editDetails = false"
            @update="(payload) => {
              editDetails = false
              emits('updateOrder', payload)
            }"
          />

        </div>

      </Sidebar>

    </div>

    <div v-if="!noOptions && guard('Order.Write')" data-div="actions" class="w-full h-10 hidden md:grid">

      <Button
        class="px-4 flex items-center justify-between"
        size="auto"
        data-button="actions"
        @click="$emit('openActions')"
      >

        <p>
          Actions
        </p>

        <Icon name="chevron-right" size="m" />

      </Button>

    </div>

  </div>

</template>
