<script setup lang="ts">

import { Guard } from '@/plugins/guard'
import { userName } from '@/modules/auth/store'
import { formatDate } from '@lib/scripts/utils'
import { onMounted, ref } from 'vue'
import { addEvent, getEvents } from '@/modules/orders/store'

import Tab from '@lib/components/buttons/Tab.vue'
import Loader from '@lib/components/blocks/Loader.vue'
import Button from '@lib/components/buttons/Button.vue'
import Textbox from '@lib/components/inputs/Textbox.vue'

import type { OrderEvent } from '@/modules/orders/types'

const props = defineProps<{
  orderId: number
}>()

const note          = ref<string>( null )
const events        = ref<OrderEvent[]>( [] )
const eventsMode    = ref<'events' | 'notes'>( 'events' )
const pendingGet    = ref<boolean>( true )
const pendingUpdate = ref<boolean>( false )

async function saveNote() {

  pendingUpdate.value = true

  const { error, payload } = await addEvent( props.orderId, {
    type:    1,
    message: note.value,
    creator: userName.value
  })

  if ( !error ) {
    note.value = null
    events.value = payload?.events ?? []
  }

  pendingUpdate.value = false

}

async function getOrderEvents() {

  const { payload, error } = await getEvents( props.orderId )

  if ( !error )
    events.value = payload?.events ?? []

  pendingGet.value = false

}

onMounted(() => {
  getOrderEvents()
})

defineExpose({
  pendingGet,
  eventsMode,
  pendingUpdate
})

</script>

<template>

  <div class="w-full md:w-[21rem] h-full grid grid-rows-[max-content_1fr] bg-core-10 border-r border-l border-core-30 overflow-hidden">

    <div class="w-full h-[2.55rem] grid grid-cols-2">

      <Tab
        data-tab="events"
        size="auto"
        class="text-sm h-full px-4 border-r border-r-core-40"
        :is-active="eventsMode === 'events'"
        @click="eventsMode = 'events'"
      >
        <template #default="{ active }">
          <p :class="{ 'font-medium': active }">
            Events
          </p>
        </template>
      </Tab>

      <Tab
        data-tab="comments"
        size="auto"
        class="text-sm h-full px-4"
        :is-active="eventsMode === 'notes'"
        @click="eventsMode = 'notes'"
      >
        <template #default="{ active }">
          <p :class="{ 'font-medium': active }">
            {{ $t('orders.details.comments') }}
          </p>
        </template>
      </Tab>

    </div>

    <Transition name="source-form" mode="out-in">

      <div
        v-if="!pendingGet"
        class="grid w-full"
        :class="{
          'overflow-y-auto grid-rows-[max-content_1fr]': eventsMode === 'events',
          'overflow-y-hidden grid-rows-[1fr_max-content]': eventsMode !== 'events',
        }"
      >

        <Transition name="source-form" mode="out-in">

          <div v-if="eventsMode === 'events'">

            <div
              v-for="event in events.filter(e => e.type !== 1)"
              :key="event.id"
              data-element="single-event"
              class="w-full p-4 grid grid-rows-[max-content_max-content] items-start border-b border-core-30"
            >

              <p class="text-sm">
                {{ event.message }}
              </p>

              <div>
                <p class="text-xs text-core-60">
                  {{ event.creator }} - {{ formatDate(event.createdAt, 'MMM DD, YYYY [at] HH:mm') }}
                </p>
              </div>

            </div>

          </div>

          <div v-else class="h-full overflow-y-auto">

            <div
              v-for="event in events.filter(e => e.type === 1)"
              :key="event.id"
              data-element="single-comment"
              class="w-full p-4 grid grid-rows-[max-content_max-content] items-start border-b border-core-30"
            >

              <p class="text-sm ">
                {{ event.message }}
              </p>

              <div>
                <p class="text-xs text-core-60">
                  {{ event.creator }} - {{ formatDate(event.createdAt, 'MMM DD, YYYY [at] HH:mm') }}
                </p>
              </div>

            </div>

          </div>

        </Transition>

        <Guard scope="Order.Write">

          <form
            v-if="eventsMode === 'notes'"
            class="p-4 grid grid-cols-[1fr_max-content] gap-x-2 items-center bg-core-20 border-t border-core-30"
            @submit.prevent="saveNote"
          >

            <Textbox
              v-model="note"
              mode="ghost"
              :required="false"
              class="text-sm bg-core-30"
              :placeholder="$t('orders.details.commentsTextBoxLabel')"
            />

            <Button
              data-button="submit-note"
              mode="naked"
              size="s"
              type="box"
              :icon="{ name: 'send', size: 'm' }"
              :pending="pendingUpdate"
              :disabled="!note"
              class="text-main hover:text-main-80"
            />

          </form>

        </Guard>

      </div>

      <div v-else class="bg-core-10 h-full w-full flex justify-center items-center gap-x-2">
        <Loader name="Events and Comments" />
      </div>

    </Transition>

  </div>

</template>
