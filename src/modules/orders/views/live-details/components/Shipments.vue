<script setup lang="ts">

import { ref } from 'vue'
import { viewSetup } from '@lib/scripts/viewSetup'
import { useRouter } from 'vue-router'
import { checkValue } from '@lib/scripts/utils'
import { getOrderPackageShipments } from '@/modules/orders/store'
import { hasPackageShipments, packageShipmentParams } from '@/modules/orders/views/live-details/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Table from '@lib/components/table/Table.vue'
import Button from '@lib/components/buttons/Button.vue'

import type { PackageShipment, PackageShipmentLot, ShipmentParams } from '@/modules/orders/types'

const props = defineProps<{
  orderId: number
}>()

const total     = ref<number>( 0 )
const router    = useRouter()
const maxPages  = ref<number>( 0 )
const shipments = ref<PackageShipment[]>( [] )

function schema(): TableSchema<PackageShipment> {
  return [
    {
      key:    'shippedDate',
      label:  'Shipped Date',
      format: 'date'
    },
    {
      key:   'carrier',
      label: 'Carrier'
    },
    {
      key:   'serviceLevel',
      label: 'Carrier Service Level'
    },
    {
      key:   'trackingNumber',
      label: 'Tracking No'
    },
    {
      key:       'weight',
      label:     'Weight',
      format:    'custom',
      transform: ( value ) => {
        return `${value} lbs`
      }
    },
    {
      key:       'dimWeight',
      label:     'Dim Weight',
      format:    'custom',
      transform: ( value ) => {
        return `${value} lbs`
      }
    },
    {
      key:    'rate',
      label:  'Rate',
      format: 'currency'
    }
  ]
}

function shipmentLotSchema(): TableSchema<PackageShipmentLot> {
  return [
    {
      key:   'sku',
      label: 'SKU',
    },
    {
      key:   'lotValue',
      label: 'Lot',
    },
    {
      key:   'quantity',
      label: 'Quantity',
    }
  ]
}

/**
 * Maps the children of a product to a nested schema
 * @param records - The records to map
 * @returns The mapped records
 */
function mapChildrenToProducts( records: Tablify<PackageShipment, PackageShipmentLot>[] ) {

  if ( !records )
    return null

  records.forEach(( record ) => {

    if ( record.lots ) {

      record.nested = {
        name:         'Lots',
        type:         'nested',
        schema:       shipmentLotSchema,
        records:      record.lots,
        recordMapKey: 'sku'
      }

    }

  })

  return records

}

async function getPackageShipments( params: ShipmentParams ) {

  const { payload, error } = await getOrderPackageShipments( props.orderId, params )

  if ( !error ) {
    shipments.value = mapChildrenToProducts( payload?.packageShipments )
    hasPackageShipments.value = !!shipments.value?.length
    total.value = payload?.totalRows ?? 0
    maxPages.value = payload?.totalPages ?? 0
  }

}

const { pending } = viewSetup(
  null,
  packageShipmentParams,
  router,
  getPackageShipments,
  null,
  null,
  true
)

</script>

<template>

  <div class="w-full grid overflow-hidden shadow-custom">

    <Table
      name="Package Shipments"
      :flex="true"
      :params="packageShipmentParams"
      :schema="schema"
      :pending="pending"
      :records="shipments || []"
      :pagination="{
        total,
        maxPages,
        statePagination: true,
      }"
      record-map-key="trackingNumber"
      custom-empty-message="There are no package shipments for this order."
    >

      <template #table-head>

        <div class="h-10 pl-4 flex items-center space-x-3">

          <Icon name="portal-inventory" size="m" class="text-main" />

          <p class="text-sm font-medium">
            Package Shipments
          </p>

        </div>

      </template>

      <template #table-neck>

        <div v-if="checkValue(packageShipmentParams.sku)" class="py-1 px-2 flex flex-wrap items-center border-b border-core-30">

          <div
            class="w-max h-8 pl-2 flex items-center space-x-1 bg-main-20 border border-main-30 rounded-xs mr-2 my-1"
          >

            <p class="text-xs md:text-sm font-light">
              SKU: <span class="font-medium">[{{ packageShipmentParams.sku }}]</span>
            </p>

            <Button
              size="auto"
              type="box"
              mode="naked"
              :icon="{
                name: 'close',
                size: 's',
              }"
              class="h-full w-8"
              @click="() => packageShipmentParams.sku = null"
            />

          </div>

        </div>

      </template>

    </Table>

  </div>

</template>
