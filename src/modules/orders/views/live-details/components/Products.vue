<script setup lang="ts">

import { confirm } from '@lib/store/confirm'
import { pageSize } from '@lib/store/table'
import { Guard, guard } from '@/plugins/guard'
import { validateModel } from '@lib/scripts/inputValidation'
import { useRoute, useRouter } from 'vue-router'
import { productDetailsOptions } from '@/store'
import { computed, reactive, ref } from 'vue'
import { setAlertOptions, setNotificationOptions } from '@lib/store/snackbar'
import { areItemsRpc, hasPackageShipments, isCached, isOrderRpc, packageShipmentParams } from '@/modules/orders/views/live-details/store'
import { checkValue, convertObjectToValidatable, formatCurrency, sanitizeQueryParams, viewSetup } from '@lib/scripts/utils'
import { batchDeleteLineItems, deleteOrderLineItem, getOrderLineItem, getOrderLineItems, updateLineItems, updateOrderLineItem } from '@/modules/orders/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Table from '@lib/components/table/Table.vue'
import Input from '@lib/components/inputs/Input.vue'
import Button from '@lib/components/buttons/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import Textbox from '@lib/components/inputs/Textbox.vue'
import Invoice from '@/modules/orders/views/live-details/components/Invoice.vue'
import SimpleBadge from '@/components/SimpleBadge.vue'

import type { BatchOption } from '@lib/types/tableTypes'
import type { InventoryPanelProduct } from '@/modules/inventory/types'
import type { EditableLineItem, EditableOrderLineItem, OrderDetails, OrderLineItem } from '@/modules/orders/types'

const props = defineProps<{
  order:        OrderDetails | null
  editable?:    boolean
  activeView?:  number
  openInvoice?: boolean
}>()

const emits = defineEmits<{
  updateOrderDetails:  []
  globalActionsUpdate: [ { partial: boolean, released: boolean } ]
}>()

function schema( lineItem: EditableOrderLineItem ): TableSchema<EditableOrderLineItem> {

  const availableInSelectedFacility = lineItem?.availableStockLevels?.find( f => f.facilityCode === props.order?.facilityCode )?.unitsAvailable

  return [
    {
      key:   'sku',
      label: 'SKU'
    },
    {
      key:   'description',
      label: 'Description'
    },
    {
      key:   'color',
      label: 'Color'
    },
    {
      key:   'size',
      label: 'Size'
    },
    {
      key:   'onHandQuantity',
      label: 'Available',
      value: lineItem?.isKitItem
        ? {
          component: SimpleBadge,
          slots:     {
            default: () => 'KIT'
          }
        }
        : availableInSelectedFacility,
    },
    {
      key:   'requestedQuantity',
      label: 'Count'
    },
    {
      key:   'backorderedQuantity',
      label: 'BO'
    },
    {
      key:    'cost',
      label:  'Cost',
      format: 'currency'
    },
    {
      key:       null,
      label:     'Total',
      value:     lineItem?.cost * ( lineItem?.backorderedQuantity + lineItem?.requestedQuantity ) || 0,
      format:    'custom',
      align:     'right',
      transform: ( value ) => {
        return formatCurrency( value )
      }
    }
  ]
}

const items             = ref<Tablify<EditableOrderLineItem>[]>( [] )
const total             = ref<number>( 0 )
const route             = useRoute()
const router            = useRouter()
const params            = reactive<BaseParams>({ page: 1, pageSize: pageSize.value, ...sanitizeQueryParams( route.query ) })
const maxPages          = ref<number>( 0 )
const activeView        = defineModel( 'activeView' )
const editedItem        = ref<Validatable<EditableLineItem>>( null )
const openEditForm      = ref( false )
const shouldUpdate      = ref( false )
const updatePending     = ref( false )
const exportInvoice     = defineModel( 'openInvoice', { type: Boolean })
const canConfirmItems   = computed(() => shouldUpdate.value && selectedProducts.value.every( item => checkValue( item.quantity ) && checkValue( item.price )))
const selectedProducts  = ref<InventoryPanelProduct[]>( [] )
const canSaveEditedItem = computed(() => validateModel( editedItem.value ))

/**
 * Maps line items to an availability boolean values array.
 * @param lineItems - The list of order line items.
 * @returns - Boolean array.
 */

function checkOrderAvailability( lineItems: Tablify<EditableOrderLineItem>[], selectedFacility: string ) {

  const flatAvailability = lineItems.map(( item ) => {

    if ( item.kitComponents?.length > 0 ) {

      return item.kitComponents.every(( component ) => {
        const facility = component.availableStockLevels?.find( f => f.facilityCode === selectedFacility )

        return facility?.unitsAvailable >= item?.requestedQuantity && item?.requestedQuantity > 0
      })

    }

    const facility = item?.availableStockLevels?.find( f => f.facilityCode === selectedFacility )
    return facility?.unitsAvailable >= item?.requestedQuantity && item?.requestedQuantity > 0

  })

  return flatAvailability

}

function canBePartialOrder() {

  const availability = checkOrderAvailability( items.value, props.order.facilityCode )
  return availability?.some( Boolean ) && availability.some( i => !i )

}

function canBeReleased() {

  const availability = checkOrderAvailability( items.value, props.order.facilityCode )
  return availability?.every( Boolean )

}

/**
 * Maps children items to their parent items in the list of order line items.
 * @param lineItems - The list of order line items.
 * @returns - The modified list of items with children items grouped under their parent items.
 */

function mapKitComponentsAsChildren( lineItems: Tablify<EditableOrderLineItem>[] ) {

  lineItems.forEach(( item ) => {

    if ( item?.kitComponents ) {
      item.children = item.kitComponents
      item.nested = {
        name:         'Kit Components',
        type:         'nested',
        schema:       () => schema( null ),
        records:      item.kitComponents,
        recordMapKey: 'sku'
      }
    }

  })

  return lineItems

}

/**
 * Opens the edit item sidebar with the item's details.
 * @param item - The item to edit.
 */

function openEditItemSidebar( item: EditableOrderLineItem ) {

  openEditForm.value = true

  editedItem.value = convertObjectToValidatable({
    sku:         item.sku,
    price:       item.cost,
    quantity:    item.requestedQuantity,
    description: item.description
  }, null, [ 'sku' ] )

}

/**
 * Closes the edit item sidebar.
 * resets the edited item.
 */

function closeEditItemSidebar() {
  openEditForm.value = false
  editedItem.value = null
}

/**
 * Opens the inventory sidebar.
 */

function openInventorySidebar() {
  router.push({ name: 'Live Order Products' })
}

/**
 * Updates the order line items with the selected products and navigates back to the order details page.
 */

async function updateInventorySidebar() {

  const { error, payload } = await updateOrderLineItems()

  if ( !error ) {

    items.value = mapKitComponentsAsChildren( payload?.lineItems ?? [] ) // ---- Update the line items from the response.
    isOrderRpc.value = true // ------------------------------------------------- Set the order to be an RPC request.
    shouldUpdate.value = false // ---------------------------------------------- Reset the update flag.
    selectedProducts.value = [] // --------------------------------------------- Reset the selected products.

    // Update the global actions after updating the line items.

    emits(
      'globalActionsUpdate',
      {
        partial:  canBePartialOrder(),
        released: canBeReleased()
      }
    )

    blockPageUpdate() // ------------------------------------------------------- Block the line items get request since we are updating them from the response.

    await router.push({
      name:   'Live Order Details',
      params: { id: router.currentRoute.value.params.id }
    })

    // Update the pagination details after
    // navigating back to the order details page.

    total.value = payload?.totalRows ?? 0
    maxPages.value = payload?.totalPages ?? 0
    pageSize.value = payload?.pageSize ?? pageSize.value
    params.pageSize = pageSize.value
    params.page = payload.currentPage

  }

}

/**
 * Cancels the inventory sidebar and navigates back to the order details page.
 * Resets the selected products.
 */

function cancelInventorySidebar() {

  blockPageUpdate()

  shouldUpdate.value = false
  selectedProducts.value = []

  router.push({ name: 'Live Order Details', params: { id: router.currentRoute.value.params.id } })

}

/**
 * Retrieves the order line items.
 * @param {BaseParams} viewParams - The view parameters.
 */

async function getOrderProducts( viewParams: BaseParams ) {

  if ( !props.order?.id )
    return

  const isRpc     = isCached.value || areItemsRpc.value
  const rpcParams = isRpc ? { isRpc } : {}

  const { error, payload } = await getOrderLineItems( props.order.id, { ...viewParams, ...rpcParams })

  if ( !error ) {

    items.value = mapKitComponentsAsChildren( payload?.lineItems ?? [] )
    total.value = payload?.totalRows ?? 0
    maxPages.value = payload?.totalPages ?? 0

    emits( 'globalActionsUpdate', { partial: canBePartialOrder(), released: canBeReleased() })

  }

}

/**
 * Updates the order line items with the selected products.
 */

async function updateOrderLineItems() {

  updatePending.value = true

  const mappedItems: Partial<OrderLineItem>[] = selectedProducts.value.map( product => ({
    sku:               product.sku,
    cost:              product.price,
    description:       product.title,
    declaredValue:     product.price,
    onHandQuantity:    product.availableByFacility?.find( f => f.facilityCode === props.order.facilityCode )?.unitsAvailable || 0,
    requestedQuantity: product.quantity
  }))

  const { error, payload } = await updateLineItems( props.order.id, mappedItems )

  if ( !error )
    setNotificationOptions({ message: 'All selected products have been successfully added or updated in the order.' })

  updatePending.value = false

  emits( 'updateOrderDetails' )
  emits( 'globalActionsUpdate', { partial: canBePartialOrder(), released: canBeReleased() })

  return { error, payload }

}

/**
 * Updates the line item with the edited details.
 */

async function updateLineItem() {

  updatePending.value = true

  const data: Partial<OrderLineItem> = {
    sku:               editedItem.value.sku.value,
    cost:              editedItem.value.price.value,
    description:       editedItem.value.description.value,
    requestedQuantity: editedItem.value.quantity.value
  }

  const { error, payload } = await updateOrderLineItem( props.order.id, data.sku, data )

  if ( !error ) {

    items.value = payload?.lineItems ?? []
    total.value = payload?.totalRows ?? 0
    maxPages.value = payload?.totalPages ?? 0
    pageSize.value = payload?.pageSize ?? pageSize.value
    params.pageSize = pageSize.value
    isOrderRpc.value = true

    setNotificationOptions({ message: 'Order product is updated successfully.' })

    emits( 'updateOrderDetails' )
    emits( 'globalActionsUpdate', { partial: canBePartialOrder(), released: canBeReleased() })

    closeEditItemSidebar()

  }

  updatePending.value = false

}

/**
 * Deletes a line item from the order.
 * @param {EditableOrderLineItem} lineItem - The line item to delete.
 */

async function deleteItem( lineItem: EditableOrderLineItem ) {

  if ( maxPages.value === 1 && items.value.length === 1 ) {

    setAlertOptions({
      message:  'Your order must contain at least one item.',
      details:  'Ensure your order isn\'t empty by adding a new product before removing this one.',
      severity: 'warning'
    })

    return

  }

  const { error, payload } = await deleteOrderLineItem( props.order.id, lineItem.id )

  if ( !error ) {

    items.value = payload?.lineItems ?? []
    total.value = payload?.totalRows ?? 0
    maxPages.value = payload?.totalPages ?? 0
    pageSize.value = payload?.pageSize ?? pageSize.value
    params.pageSize = pageSize.value
    isOrderRpc.value = true

    blockPageUpdate()

    setNotificationOptions({ message: 'Order product is deleted successfully.' })

    emits( 'updateOrderDetails' )
    emits( 'globalActionsUpdate', { partial: canBePartialOrder(), released: canBeReleased() })

  }

}

/**
 * Deletes selected line items from the order.
 * @param {Partial<EditableOrderLineItem>[]} selectedItems - The selected line items to delete.
 */

async function deleteSelectedLineItems( selectedItems: Partial<EditableOrderLineItem>[] ) {

  if ( selectedItems.length === items.value.length && maxPages.value === 1 ) {

    setAlertOptions({
      message:  'Your order must contain at least one item.',
      details:  'Ensure your order isn\'t empty by adding a new product before removing this one.',
      severity: 'warning'
    })

    return

  }

  const lineItemsToDelete = selectedItems.map( item => String( item.id ))

  const { error, payload } = await batchDeleteLineItems( props.order.id, lineItemsToDelete )

  if ( !error ) {

    if ( selectedItems.length === items.value.length )
      params.page = params.page - 1

    items.value = payload?.lineItems ?? []
    total.value = payload?.totalRows ?? 0
    maxPages.value = payload?.totalPages ?? 0
    pageSize.value = payload?.pageSize ?? pageSize.value
    params.pageSize = pageSize.value
    isOrderRpc.value = true

    blockPageUpdate()
    setNotificationOptions({ message: 'All selected products have been successfully deleted from the order.' })

    emits( 'updateOrderDetails' )
    emits( 'globalActionsUpdate', { partial: canBePartialOrder(), released: canBeReleased() })

  }

}

/**
 * Checks if the product already exists in the line items,
 * and if it does it retrieves it as a line item and
 * it maps it as an inventory product and return it,
 * else it adds the product to the inventory selected products.
 *
 * @param {InventoryPanelProduct} product - The product to add.
 * @returns {InventoryPanelProduct} - The product to add.
 */

async function addProduct( product: InventoryPanelProduct ): Promise<InventoryPanelProduct> {

  const lineItemData = await getOrderLineItem( props.order.id, product.sku )
  const lineItem = lineItemData.payload

  shouldUpdate.value = true

  if ( !lineItem )
    return product

  return {
    id:                  lineItem.inventoryId,
    sku:                 lineItem.sku,
    price:               lineItem.cost,
    title:               lineItem.description,
    supplier:            null,
    quantity:            lineItem.requestedQuantity,
    backOrdered:         lineItem.backorderedQuantity,
    kitComponents:       null,
    availableQuantity:   lineItem.requestedQuantity,
    availableByFacility: null,
  } as InventoryPanelProduct

}

/**
 * Returns the options for the line item.
 * @param {EditableOrderLineItem} lineItem - The line item.
 * @returns {DropListOption[]} - The options for the line item.
 */

function itemOptions( lineItem: EditableOrderLineItem ): DropListOption[] {

  return [
    {
      id:   1,
      name: 'Delete',
      icon: {
        name:  'delete',
        color: '#FF4343',
        size:  'm'
      },
      hidden: !guard( 'Order.Write' ) || !props.editable,
      action: () => confirm({
        header:      'Delete Line Item from Order',
        description: `Are you sure you want to delete <span class="text-main font-medium" >${lineItem.sku}</span>?`,
        action:      async () => await deleteItem( lineItem )
      })
    },
    {
      id:   2,
      name: 'Edit',
      icon: {
        name: 'edit',
        size: 'm'
      },
      hidden: !guard( 'Order.Write' ) || !props.editable,
      action: () => openEditItemSidebar( lineItem )
    },
    {
      id:   3,
      name: 'View Details',
      icon: {
        name: 'eye-open',
        size: 'm'
      },
      action: () => {
        productDetailsOptions.id = lineItem.inventoryId
        productDetailsOptions.type = 'Live'
      }
    },
    {
      id:   4,
      name: 'Filter package shipments for this product',
      icon: {
        name: 'filter',
        size: 'm'
      },
      hidden: !hasPackageShipments.value,
      action: () => {
        packageShipmentParams.sku = lineItem.sku
        activeView.value = 3
      }
    }
  ]

}

const lineItemsBatchOptions: BatchOption<EditableOrderLineItem>[] = [
  {
    id:             1,
    type:           'negative',
    icon:           'delete',
    group:          'Bulk Actions',
    action:         selected => deleteSelectedLineItems( selected ),
    actionName:     'Delete',
    pendingMessage: 'Deleting Items',
  }
]

const { pending, blockPageUpdate } = viewSetup( 'Live Order Details', params, router, getOrderProducts )

</script>

<template>

  <div class="w-full h-full grid grid-rows-[1fr_max-content] overflow-hidden shadow-custom">

    <Table
      name="Line Items"
      :flex="true"
      :schema="schema"
      :params="params"
      :pending="pending"
      :records="items"
      :selectable="editable && guard('Order.Write')"
      :batch-options="lineItemsBatchOptions"
      :record-options="itemOptions"
      record-map-key="id"
      :pagination="{
        total,
        maxPages,
      }"
    >

      <template #table-head>

        <div class="h-10 pl-4 flex items-center space-x-3">

          <Icon name="product" size="m" class="text-main" />

          <p class="text-sm font-medium grow">
            Products
          </p>

          <Guard scope="Order.Write">

            <div
              v-if="editable"
              class="h-full border-l border-core-30"
            >

              <Button
                mode="naked"
                size="auto"
                class="h-full px-4 flex items-center space-x-4"
                :disabled="pending || updatePending"
                @click="openInventorySidebar"
              >

                <p class="text-sm">
                  Add Product
                </p>

                <Icon name="add" size="s" class="text-main" />

              </Button>

            </div>

          </Guard>

        </div>

      </template>

    </Table>

    <div class="w-full h-10 px-4 flex md:hidden items-center gap-x-4 justify-end bg-core-10">

      <p class="font-medium">
        <span class="text-core">Ship Fee: </span>{{ formatCurrency(order?.shipHandlingFee) }}
      </p>

      <p class="font-medium">
        <span class="text-core">Total Cost: </span>{{ formatCurrency(order?.total) }}
      </p>

    </div>

    <!-- SIDEBAR :: Inventory -->

    <Sidebar
      :strict="shouldUpdate"
      :open="$route.name === 'Live Order Products'"
      custom-tw-offset="top-0 md:top-[3rem] h-full md:h-[calc(100%-3rem)]"
      @close="cancelInventorySidebar"
    >

      <div class="w-full md:w-[26rem] h-full grid grid-rows-[max-content_1fr_max-content] md:grid-rows-[1fr_max-content] ignore-outside">

        <div class="w-full h-12 pl-4 flex md:hidden items-center space-x-3">

          <Icon name="product" size="m" class="text-main" />

          <p class="text-sm font-medium grow">
            Your Products
          </p>

        </div>

        <RouterView v-slot="{ Component }">

          <Component
            :is="Component"
            v-model:selected-products="selectedProducts"
            :facility="order?.facilityCode"
            :allow-locking="false"
            :allow-price-update="true"
            :disabled="updatePending"
            :actions="{
              add: addProduct,
            }"
          />

        </RouterView>

        <div
          class="w-full h-12 grid grid-cols-2"
          :class="{ 'md:hidden': !shouldUpdate, 'md:grid': shouldUpdate }"
        >

          <Button
            size="auto"
            mode="secondary"
            :disabled="updatePending"
            @click="cancelInventorySidebar"
          >
            Cancel
          </Button>

          <Button
            size="auto"
            :disabled="!canConfirmItems"
            :pending="updatePending"
            @click="updateInventorySidebar"
          >
            Confirm Products
          </Button>

        </div>

      </div>

    </Sidebar>

    <!-- SIDEBAR :: Edit Line Item -->

    <Sidebar
      :dim="true"
      :strict="true"
      :open="openEditForm"
      @close="closeEditItemSidebar"
    >

      <div class="w-full md:w-[26rem] h-full grid grid-rows-[max-content_1fr_max-content] overflow-hidden">

        <div class="w-full h-12 pl-4 flex items-center border-b border-core-30">

          <p class="text-sm font-medium truncate grow">
            Edit Item <span class="text-main">[{{ editedItem.sku.value }}]</span>
          </p>

          <div class="w-12 h-full border-l border-core-30">

            <Button
              mode="naked"
              type="box"
              size="auto"
              class="h-full w-full"
              :icon="{
                name: 'close',
                size: 'm',
              }"
              :disabled="updatePending"
              @click="closeEditItemSidebar"
            />

          </div>

        </div>

        <div
          class="p-4 grid gap-4 content-start overflow-hidden overflow-y-auto"
          :class="{
            'pointer-events-none': updatePending,
          }"
        >

          <Textbox
            v-model="editedItem.description.value"
            v-model:valid="editedItem.description.valid"
            label="Description"
          />

          <Input
            v-model="editedItem.quantity.value"
            v-model:valid="editedItem.quantity.valid"
            label="Quantity"
            type="number"
            :min="1"
          />

          <Input
            v-model="editedItem.price.value"
            v-model:valid="editedItem.price.valid"
            label="Cost"
            type="currency"
            align="right"
          />

        </div>

        <div class="w-full h-12 grid">

          <Button
            size="auto"
            class="h-full"
            :disabled="!canSaveEditedItem"
            :pending="updatePending"
            @click="updateLineItem"
          >
            Save Item
          </Button>

        </div>

      </div>

    </Sidebar>

    <!-- INVOICE :: Commercial Invoice Template -->

    <Invoice
      v-if="exportInvoice"
      :order="order"
      :items="items"
      @close-invoice="exportInvoice = false"
    />

  </div>

</template>
