<script setup lang="ts">

import { Guard } from '@/plugins/guard'
import { useRoute } from 'vue-router'
import { ref, watch } from 'vue'

import Tab from '@lib/components/buttons/Tab.vue'
import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'

const route   = useRoute()
const options = ref<boolean>( false )

watch( route, () => options.value = false )

</script>

<template>

  <div
    class="w-full h-full grid"
    :class="{
      'grid-rows-[max-content_1fr] md:grid-rows-1': route.name === 'Orders Summary',
      'grid-rows-[max-content_1fr_max-content] md:grid-rows-[max-content_1fr]': route.name !== 'Orders Summary',
    }"
  >

    <div v-if="route.name !== 'Orders Summary'" class="w-full h-10 hidden md:flex items-center">

      <Tab to="/orders/live" size="auto" class="text-sm font-medium w-full md:w-auto h-full px-4">
        {{ $t('orders.liveOrders.tabLabel') }}
      </Tab>

      <Tab to="/orders/draft" size="auto" class="text-sm font-medium w-full md:w-auto h-full px-4">
        {{ $t('orders.draftOrders.tabLabel') }}
      </Tab>

      <div class="h-full grow border-b border-core-30" />

      <Guard scope="Order.Write">

        <div class="h-full border-l border-b border-core-30">

          <Button
            size="auto"
            mode="naked"
            class="text-main hover:text-main h-full px-4 flex items-center space-x-2"
            @click="$router.push({ name: 'Upload Bulk Orders' })"
          >

            <p class="text-sm font-medium">
              {{ $t('orders.draftOrders.buttons.bulkUpload') }}
            </p>

            <Icon name="batch-upload" size="s" />

          </Button>

        </div>

      </Guard>

      <Guard scope="Order.Write">

        <div class="h-full border-l border-b border-core-30">

          <Button
            size="auto"
            mode="naked"
            class="text-main hover:text-main h-full px-4 flex items-center space-x-2"
            @click="$router.push({ name: 'Create Order' })"
          >

            <p class="text-sm font-medium">
              {{ $t('orders.liveOrders.buttons.newOrder') }}
            </p>

            <Icon name="add" size="s" />

          </Button>

        </div>

      </Guard>

    </div>

    <div class="w-full h-10 pl-4 flex md:hidden items-center bg-core-10 border-b border-core-30">

      <p class="font-medium grow">
        {{ $route.name }}
      </p>

      <Button
        v-if="route.name !== 'Orders Summary'"
        size="m"
        mode="naked"
        type="box"
        :icon="{
          name: 'dots-vertical',
          size: 'm',
        }"
        @click="() => options = !options"
      />

    </div>

    <div class="w-full h-full overflow-hidden md:page-background-gradient">

      <RouterView v-slot="{ Component }">

        <Transition mode="out-in" name="route">

          <Component :is="Component" />

        </Transition>

      </RouterView>

    </div>

    <div v-if="route.name !== 'Orders Summary'" class="w-full h-12 md:hidden grid grid-cols-2 border-t border-core-30 bg-core-10">

      <RouterLink
        to="/orders/live"
        class="h-full w-full relative grid place-content-center clicked-active-state"
        active-class="text-main bg-core-20"
      >
        <Icon name="portal-orders" size="m" />
      </RouterLink>

      <RouterLink
        to="/orders/draft"
        class="h-full w-full relative grid place-content-center clicked-active-state"
        active-class="text-main bg-core-20"
      >
        <Icon name="draft-order" size="m" />
      </RouterLink>

    </div>

    <Sidebar
      :dim="true"
      :open="options"
      position="bottom"
      @close="() => options = false"
    >

      <div class="bg-core-10">

        <div class="w-full h-12 pl-4 flex items-center bg-core-20 border-b border-core-30">

          <p class="text-sm font-medium grow">
            Available Options
          </p>

          <Button
            mode="naked"
            type="box"
            size="xl"
            :icon="{ name: 'close', size: 'm' }"
            @click="() => options = false"
          />

        </div>

        <Guard scope="Order.Write">

          <div class="p-4 grid gap-y-3">

            <Button
              type="pill"
              class="px-4 flex items-center space-x-2 justify-between"
              @click="$router.push({ name: 'Create Order' })"
            >

              <p>{{ $t('orders.liveOrders.buttons.newOrder') }}</p>

              <Icon name="add" size="s" />

            </Button>

            <Button
              type="pill"
              class="px-4 flex items-center space-x-2 justify-between"
              @click="$router.push({ name: 'Upload Bulk Orders' })"
            >

              <p>{{ $t('orders.draftOrders.buttons.bulkUpload') }}</p>

              <Icon name="batch-upload" size="s" />

            </Button>

          </div>

        </Guard>

      </div>

    </Sidebar>

  </div>

</template>
