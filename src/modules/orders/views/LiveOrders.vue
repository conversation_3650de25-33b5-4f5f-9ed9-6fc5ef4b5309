<script setup lang="ts">

import { useI18n } from 'vue-i18n'
import { ordersParams } from '@/modules/orders/routes'
import { useRoute, useRouter } from 'vue-router'
import { computed, reactive, ref, watch } from 'vue'
import { bulkExportOrders, getLiveOrdersWidgets, getOrders } from '@/modules/orders/store'
import { appMode, booleanToYesNo, defaultFacility, facilitiesList, shippingMethodsList } from '@/store'
import { checkValue, compareObjects, formatDate, removeEmptyKeysFromObject, sanitizeQueryParams, saveFile, viewSetup } from '@lib/scripts/utils'

import Icon from '@lib/components/blocks/Icon.vue'
import Table from '@lib/components/table/Table.vue'
import Input from '@lib/components/inputs/Input.vue'
import Select from '@lib/components/inputs/Select.vue'
import Button from '@lib/components/buttons/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import DatePicker from '@lib/components/inputs/DatePicker.vue'
import LiveWidgetsBlock from '@/modules/orders/components/LiveWidgetsBlock.vue'
import SearchFilterBadge from '@/components/SearchFilterBadge.vue'

import type { BatchOption } from '@lib/types/tableTypes'
import type { Order, OrdersParams } from '@/modules/orders/types'
import type { LiveOrderWidgets, SearchFilter } from '@/types'

interface LiveOrderBooleanParamsTemp {
  isVoid:      number
  isHeld:      number
  isShipped:   number
  atWarehouse: number
  isBackorder: number
}

const { t }       = useI18n()
const total       = ref<number>( 0 )
const route       = useRoute()
const params      = reactive<OrdersParams>({ ...ordersParams, ...sanitizeQueryParams( route.name === 'Live Orders' ? route.query : {}) })
const router      = useRouter()
const orders      = ref<Tablify<Order>[]>( [] )
const maxPages    = ref<number>( 0 )
const openFilters = ref( false )
const hasFacility = ref<boolean>( defaultFacility.value !== 'ALL' )
const widgetsData = ref<LiveOrderWidgets>( null )

/**
 * -- TABLE SCHEMA
 */

function schema( ): TableSchema<Order> {
  return [
    {
      key:     'id',
      sortKey: 'orderId',
      label:   'Order ID',
      hidden:  appMode !== 'ADMIN'
    },
    {
      key:     'owdReference',
      sortKey: 'owdReference',
      label:   t( 'orders.columnLabel.owdReference' )
    },
    {
      key:     'clientReference',
      label:   t( 'orders.columnLabel.clientReference' ),
      sortKey: 'clientReference',
    },
    {
      key:     'facilityDisplayName',
      sortKey: 'facilityDisplayName',
      label:   t( 'orders.columnLabel.facility' ),
    },
    {
      key:     'customerFirstName',
      label:   t( 'orders.columnLabel.firstName' ),
      sortKey: 'customerFirstName',
    },
    {
      key:     'customerLastName',
      label:   t( 'orders.columnLabel.lastName' ),
      sortKey: 'customerLastName',
    },
    {
      key:     'status',
      label:   t( 'orders.columnLabel.orderStatus' ),
      sortKey: 'orderStatus',
    },
    {
      key:     'receivedDate',
      label:   t( 'orders.columnLabel.receivedDate' ),
      format:  'date',
      sortKey: 'receivedDate',
    },
    {
      key:     'shipDate',
      label:   t( 'orders.columnLabel.shipDate' ),
      format:  'date',
      sortKey: 'shipDate',
    },
    {
      key:     'shippingMethod',
      label:   t( 'orders.columnLabel.shippingMethod' ),
      sortKey: 'shippingMethod',
    },
    {
      key:     'total',
      label:   t( 'orders.columnLabel.orderTotal' ),
      format:  'currency',
      sortKey: 'orderTotal',
    },
    {
      key:   'notes',
      label: t( 'orders.columnLabel.notes' ),
    },
    {
      key:     'createdDate',
      label:   'Date Created',
      sortKey: 'createdDate',
      format:  'date'
    }
  ]
}

/**
 * -- SEARCH / FILTER LIVE ORDERS
 */

const searchOrdersDefaultModel: OrdersParams = {
  sku:               null,
  isHeld:            null,
  isVoid:            null,
  facility:          null,
  poNumber:          null,
  groupName:         null,
  isShipped:         null,
  atWarehouse:       null,
  isBackorder:       null,
  owdReference:      null,
  customerCity:      null,
  customerState:     null,
  customerEmail:     null,
  shippingMethod:    null,
  trackingNumber:    null,
  createdEndDate:    null,
  shippedEndDate:    null,
  clientReference:   null,
  createdStartDate:  null,
  shippedStartDate:  null,
  customerLastName:  null,
  customerFirstName: null
}

const searchOrdersModel = reactive({ ...searchOrdersDefaultModel, ...sanitizeQueryParams( route.name === 'Live Orders' ? route.query : {}) })

const liveOrderBooleanParamsTempModel = ref<LiveOrderBooleanParamsTemp> ({
  isHeld:      null,
  isVoid:      null,
  isShipped:   null,
  atWarehouse: null,
  isBackorder: null
})

function generateFilters( selectedFilters: Partial<OrdersParams> ): SearchFilter<OrdersParams>[] {
  return [
    {
      key:   'createdStartDate',
      label: t( 'orders.searchFilterLabel.createdStartDate' ),
      value: selectedFilters?.createdStartDate ? formatDate( selectedFilters?.createdStartDate, 'MMM, DD YYYY' ) : null
    },
    {
      key:   'createdEndDate',
      label: t( 'orders.searchFilterLabel.createdEndDate' ),
      value: selectedFilters?.createdEndDate ? formatDate( selectedFilters?.createdEndDate, 'MMM, DD YYYY' ) : null
    },
    {
      key:   'shippedStartDate',
      label: t( 'orders.searchFilterLabel.shippedStartDate' ),
      value: selectedFilters?.shippedStartDate ? formatDate( selectedFilters?.shippedStartDate, 'MMM, DD YYYY' ) : null
    },
    {
      key:   'shippedEndDate',
      label: t( 'orders.searchFilterLabel.shippedEndDate' ),
      value: selectedFilters?.shippedEndDate ? formatDate( selectedFilters?.shippedEndDate, 'MMM, DD YYYY' ) : null
    },
    {
      key:   'facility',
      label: t( 'orders.searchFilterLabel.facility' ),
      value: facilitiesList.value?.find( item => item.id === selectedFilters?.facility )?.name ?? null
    },
    {
      key:   'owdReference',
      label: t( 'orders.searchFilterLabel.owdReferenceNumber' ),
      value: selectedFilters?.owdReference ?? null
    },
    {
      key:   'clientReference',
      label: t( 'orders.searchFilterLabel.clientReferenceNumber' ),
      value: selectedFilters?.clientReference ?? null
    },
    {
      key:   'customerFirstName',
      label: t( 'orders.searchFilterLabel.customerFirstName' ),
      value: selectedFilters?.customerFirstName ?? null
    },
    {
      key:   'customerLastName',
      label: t( 'orders.searchFilterLabel.customerLastName' ),
      value: selectedFilters?.customerLastName ?? null
    },
    {
      key:   'customerEmail',
      label: t( 'orders.searchFilterLabel.customerEmail' ),
      value: selectedFilters?.customerEmail ?? null
    },
    {
      key:   'sku',
      label: t( 'orders.searchFilterLabel.sku' ),
      value: selectedFilters?.sku ?? null
    },
    {
      key:   'trackingNumber',
      label: t( 'orders.searchFilterLabel.trackingNumber' ),
      value: selectedFilters?.trackingNumber ?? null
    },
    {
      key:   'poNumber',
      label: t( 'orders.searchFilterLabel.poNumber' ),
      value: selectedFilters?.poNumber ?? null
    },
    {
      key:   'isHeld',
      label: t( 'orders.searchFilterLabel.onHold' ),
      value: booleanToYesNo( selectedFilters.isHeld )
    },
    {
      key:   'isShipped',
      label: t( 'orders.searchFilterLabel.isShipped' ),
      value: booleanToYesNo( selectedFilters?.isShipped )
    },
    {
      key:   'atWarehouse',
      label: t( 'orders.searchFilterLabel.atWarehouse' ),
      value: booleanToYesNo( selectedFilters?.atWarehouse )
    },
    {
      key:   'isBackorder',
      label: t( 'orders.searchFilterLabel.backOrderStatus' ),
      value: booleanToYesNo( selectedFilters?.isBackorder )
    },
    {
      key:   'isVoid',
      label: t( 'orders.searchFilterLabel.isVoided' ),
      value: booleanToYesNo( selectedFilters?.isVoid )
    },
    {
      key:   'groupName',
      label: 'Group Name',
      value: selectedFilters.groupName || null
    },
    {
      key:   'customerCity',
      label: 'Customer City',
      value: selectedFilters.customerCity || null
    },
    {
      key:   'customerState',
      label: 'Customer State',
      value: selectedFilters.customerState || null
    },
    {
      key:   'shippingMethod',
      label: 'Shipping Method',
      value: selectedFilters.shippingMethod || null
    }
  ]
}

const searchOrdersFilters = computed(() => generateFilters( removeEmptyKeysFromObject( params )))
const hasFiltersApplied   = computed(() => searchOrdersFilters.value.some( filter => !!filter.value ))

/**
 * If shippedStartDate or shippedEndDate filter
 * param has value, set the shipped status to true.
 *
 * @param value The value to check ( shippedStartDate or shippedEndDate )
 * @returns void
 */

function setShippedStatus( value: string ) {

  if ( checkValue( value )) { // ------------------------------ Check if there is a value
    searchOrdersModel.isShipped = true // --------------------- Update the searchOrders Model
    liveOrderBooleanParamsTempModel.value.isShipped = 1 // ---- Update the boolean model
  }

}

function searchOrders() {

  Object.keys( searchOrdersModel ).forEach(( key ) => {
    params[key] = searchOrdersModel[key]
  })

  openFilters.value = false

}

function resetFilters() {
  Object.keys( searchOrdersDefaultModel ).forEach(( key ) => {
    params[key] = searchOrdersDefaultModel[key]
    searchOrdersModel[key] = searchOrdersDefaultModel[key]
    liveOrderBooleanParamsTempModel.value[key] = searchOrdersDefaultModel[key]
  })
}

function resetSearchModel() {
  Object.keys( searchOrdersDefaultModel ).forEach(( key ) => {
    searchOrdersModel[key] = searchOrdersDefaultModel[key]
    liveOrderBooleanParamsTempModel.value[key] = searchOrdersDefaultModel[key]
  })
}

function removeFilter( key: string ) {

  // If the removed key is "isShipped"
  // shippedStartDate and shippedEndDate are set to null

  if ( key === 'isShipped' ) {

    params.shippedStartDate = null
    searchOrdersModel.shippedStartDate = null

    params.shippedEndDate = null
    searchOrdersModel.shippedEndDate = null

  }

  params[key] = null
  searchOrdersModel[key] = null
  liveOrderBooleanParamsTempModel.value[key] = null

}

// Watch for changes in the URL query params
// If the URL params are changed but the state params are not,
// update the filter models to match the URL params.

watch( route, ( n ) => {

  const URLParams   = sanitizeQueryParams( n.query )
  const cleanParams = removeEmptyKeysFromObject( params )

  if ( !compareObjects( URLParams, cleanParams )) {

    // Add new params to the models

    for ( const key in URLParams ) {
      if ( searchOrdersModel.hasOwnProperty( key ))
        searchOrdersModel[key] = URLParams[key]

    }

    // Remove non existing params from the models

    for ( const key in searchOrdersModel ) {

      if ( !URLParams.hasOwnProperty( key ))
        searchOrdersModel[key] = null

    }

    for ( const key in liveOrderBooleanParamsTempModel.value ) {

      if ( !URLParams.hasOwnProperty( key ))
        liveOrderBooleanParamsTempModel.value[key] = null

    }

  }

})

/**
 * -- GET LIVE ORDERS DATA
 */

async function getOrdersList( viewParams: OrdersParams ) {

  const { payload } = await getOrders( viewParams )

  total.value = payload?.totalRows ?? 0
  orders.value = payload?.orders ?? []
  maxPages.value = payload?.totalPages ?? 0

}

/**
 * -- ROW OPTIONS
 */

function orderRowOptions( record: Order ): DropListOption[] {
  return [
    {
      id:   1,
      name: t( 'orders.details.detailsText' ),
      icon: {
        name: 'edit',
        size: 'm'
      },
      action: () => {
        router.push({ name: 'Live Order Details', params: { orderId: record.id } })
      }
    }
  ]
}

async function getWidgetsData() {

  const { payload } = await getLiveOrdersWidgets()
  widgetsData.value = payload

}

function filterOrdersByWidget( filterParams:  OrdersParams ) {

  resetFilters()

  Object.keys( filterParams ).forEach(( key ) => {
    params[key] = filterParams[key]
    searchOrdersModel[key] = filterParams[key]
  })

}
const ordersBatchOptions: BatchOption<Order>[] = [
  {
    id:     1,
    icon:   'export',
    type:   'neutral',
    group:  'Export',
    action: async ( selected ) => {

      const { payload, error } = await bulkExportOrders( selected.map( item => item.id ))

      if ( !error ) {

        const fileName = `orders_export_${formatDate( new Date(), 'YYYY-MM-DD' )}`

        saveFile(
          payload,
          fileName,
          { type: payload.type }
        )

      }

    },
    actionName: 'Export Excel'
  },
  {
    id:     2,
    icon:   'export',
    type:   'neutral',
    group:  'Export',
    action: async ( selected ) => {

      const { payload, error } = await bulkExportOrders( selected.map( item => item.id ), true )

      if ( !error ) {

        const fileName = `orders_export_for_import_${formatDate( new Date(), 'YYYY-MM-DD' )}`

        saveFile(
          payload,
          fileName,
          { type: payload.type }
        )

      }

    },
    actionName: 'Export CSV for import'
  }
]

const { pending } = viewSetup(
  'Live Orders',
  params,
  router,
  [
    { callback: getOrdersList },
    { callback: getWidgetsData, ignoreParams: 'all' }
  ],
  null,
  true
)

</script>

<template>

  <div class="h-full grid grid-rows-[max-content_1fr] overflow-hidden">

    <LiveWidgetsBlock
      :widgets="widgetsData"
      :disabled="pending"
      @filter="filterOrdersByWidget"
    />

    <div class="grid md:px-[1.625rem] overflow-hidden">

      <Table
        v-model:params="params"
        name="Orders"
        class="md:shadow-custom md:border md:border-core-30"
        :flex="true"
        :schema="schema"
        :records="orders"
        :pending="pending"
        :selectable="true"
        resource-name="Order"
        record-map-key="owdReference"
        :batch-options="ordersBatchOptions"
        :record-options="orderRowOptions"
        :enable-column-chooser="true"
        :pagination="{
          total,
          maxPages,
        }"
      >

        <template #table-head>

          <div class="flex items-center">

            <div class="border-r border-core-30">

              <Button
                mode="naked"
                size="m"
                class="px-3 lg:px-4 flex items-center lg:space-x-2"
                :is-active="openFilters"
                @click="openFilters = !openFilters"
              >
                <Icon size="m" name="search" />
                <p class="text-sm font-medium hidden lg:block">
                  {{ $t('global.label.search', { name: 'Live Orders' }) }}
                </p>
              </Button>

            </div>

            <div v-if="hasFiltersApplied" class="border-r border-core-30">

              <Button
                data-button="reset-filters"
                size="m"
                type="box"
                mode="naked"
                :icon="{
                  name: 'reset',
                  size: 'm',
                }"
                @click="resetFilters"
              />

            </div>

          </div>

        </template>

        <template #table-neck>

          <div v-if="hasFiltersApplied" class="py-1 px-2 flex flex-wrap items-center border-b border-core-30">

            <SearchFilterBadge
              v-for="filter in searchOrdersFilters"
              v-show="filter.value"
              :key="filter.key"
              :filter="filter"
              @remove-filter="removeFilter"
            />

          </div>

        </template>

      </Table>

    </div>

    <!-- Sidebar :: Search Filters -->

    <Sidebar
      :open="openFilters"
      :strict="false"
      :dim="true"
      @close="openFilters = false"
    >

      <div class="w-full h-full flex flex-col">

        <!-- Search Header -->

        <div class="w-full h-12 sticky top-0 z-1 flex shrink-0 items-center bg-core-20 border-b border-core-30">

          <div class="h-full px-4 flex items-center space-x-3 grow border-r border-core-30">

            <p class="text-sm font-medium">
              {{ $t('global.label.search', { name: 'Live Orders' }) }}
            </p>

          </div>

          <div class="h-full border-r border-core-30">

            <Button mode="naked" size="auto" class="h-full px-4 flex items-center space-x-2" data-button="reset-filters-modal" @click="resetSearchModel">

              <p class="text-sm">
                Reset Filters
              </p>

              <Icon name="reset" size="s" class="text-main" />
            </Button>

          </div>

          <Button
            type="box"
            size="auto"
            mode="naked"
            class="w-12 h-full min-w-[3rem]"
            :icon="{
              name: 'close',
              size: 'm',
            }"
            @click="openFilters = false"
          />

        </div>

        <!-- Search Options -->

        <div class="w-full h-full overflow-hidden overflow-y-auto">

          <form class="w-full md:w-[46rem] md:max-w-[46rem] md:px-6 grid md:gap-4" @submit.prevent>

            <!-- Date Created -->

            <section>

              <div class="h-12 sticky top-0 z-1 px-4 md:px-2 flex items-center md:col-span-2 bg-core-20 border-b md:border-b-0 border-core-30">

                <p class="text-xs text-core uppercase">
                  {{ $t('orders.searchFilterLabel.dateCreated') }}
                </p>

              </div>

              <div class="grid md:grid-cols-2 md:gap-4">

                <DatePicker
                  v-model="searchOrdersModel.createdStartDate"
                  :label="$t('orders.searchFilterLabel.startDate')"
                  :required="false"
                  :limit-to="searchOrdersModel.createdEndDate"
                />

                <DatePicker
                  v-model="searchOrdersModel.createdEndDate"
                  :label="$t('orders.searchFilterLabel.endDate')"
                  :required="false"
                  :limit-from="searchOrdersModel.createdStartDate"
                />

              </div>

            </section>

            <!-- Date Shipped -->

            <section>

              <div class="h-12 sticky top-0 z-1 px-4 md:px-2 flex items-center md:col-span-2 bg-core-20 border-b md:border-b-0 border-core-30">

                <p class="text-xs text-core uppercase">
                  {{ $t('orders.searchFilterLabel.dateShipped') }}
                </p>

              </div>

              <div class="grid md:grid-cols-2 md:gap-4">

                <DatePicker
                  v-model="searchOrdersModel.shippedStartDate"
                  :label="$t('orders.searchFilterLabel.startDate')"
                  :required="false"
                  :limit-to="searchOrdersModel.shippedEndDate"
                  @update:model-value="setShippedStatus"
                />

                <DatePicker
                  v-model="searchOrdersModel.shippedEndDate"
                  :label="$t('orders.searchFilterLabel.endDate')"
                  :required="false"
                  :limit-from="searchOrdersModel.shippedStartDate"
                  @update:model-value="setShippedStatus"
                />

              </div>

            </section>

            <!-- Search By -->

            <section>

              <div class="h-12 w-full sticky top-0 z-1 px-4 md:px-2 flex items-center bg-core-20 border-b md:border-b-0 border-core-30">

                <p class="text-xs text-core uppercase">
                  {{ $t('global.label.search', { name: 'By' }) }}
                </p>

              </div>

              <div class="grid md:grid-cols-2 md:gap-4">

                <Select
                  v-show="!hasFacility"
                  v-model="searchOrdersModel.facility"
                  :disabled="hasFacility"
                  :label="$t('orders.searchFilterLabel.facility')"
                  class="hidden md:block md:col-span-2"
                  :required="false"
                  :options="facilitiesList"
                  :teleport="false"
                />

                <Select
                  v-show="!hasFacility"
                  v-model="searchOrdersModel.facility"
                  :disabled="hasFacility"
                  :label="$t('orders.searchFilterLabel.facility')"
                  class="md:hidden"
                  :required="false"
                  :options="facilitiesList"
                />

                <Input
                  v-model="searchOrdersModel.owdReference"
                  :label="$t('orders.searchFilterLabel.owdReferenceNumber')"
                  :required="false"
                />

                <Input
                  v-model="searchOrdersModel.clientReference"
                  :label="$t('orders.searchFilterLabel.clientReferenceNumber')"
                  :required="false"
                />

                <Input
                  v-model="searchOrdersModel.customerFirstName"
                  :label="$t('orders.searchFilterLabel.customerFirstName')"
                  :required="false"
                />

                <Input
                  v-model="searchOrdersModel.customerLastName"
                  :label="$t('orders.searchFilterLabel.customerLastName')"
                  :required="false"
                />

                <Input
                  v-model="searchOrdersModel.customerEmail"
                  :label="$t('orders.searchFilterLabel.customerEmail')"
                  :required="false"
                />

                <Input
                  v-model="searchOrdersModel.sku"
                  :label="$t('orders.searchFilterLabel.sku')"
                  :required="false"
                />

                <Input
                  v-model="searchOrdersModel.trackingNumber"
                  :label="$t('orders.searchFilterLabel.trackingNumber')"
                  :required="false"
                />

                <Input
                  v-model="searchOrdersModel.poNumber"
                  :label="$t('orders.searchFilterLabel.poNumber')"
                  :required="false"
                />

                <Input
                  v-model="searchOrdersModel.customerCity"
                  label="Customer City"
                  :required="false"
                />

                <Input
                  v-model="searchOrdersModel.customerState"
                  label="Customer State"
                  :required="false"
                />

                <Input
                  v-model="searchOrdersModel.groupName"
                  label="Group Name"
                  :required="false"
                />

                <Select
                  v-model="searchOrdersModel.shippingMethod"
                  label="Shipping Method"
                  :options="shippingMethodsList"
                  :required="false"
                  return-type="name"
                />

              </div>

            </section>

            <!-- Filter By Status -->

            <section>

              <div class="h-12 sticky top-0 z-1 flex items-center md:col-span-2 bg-core-20 px-4 md:px-2 border-b md:border-b-0 border-core-30">

                <p class="text-xs text-core uppercase">
                  {{ $t('orders.searchFilterLabel.filterByStatus') }}
                </p>

              </div>

              <div class="grid md:grid-cols-2 md:gap-4 md:pb-4">

                <Select
                  v-model="liveOrderBooleanParamsTempModel.isHeld"
                  v-model:boolean-model="searchOrdersModel.isHeld"
                  label="On Hold Status"
                  :options="[
                    {
                      id: 1,
                      name: 'On Hold',
                      mapToBoolean: 'true',
                    },
                    {
                      id: 2,
                      name: 'Not On Hold',
                      mapToBoolean: 'false',
                    },
                  ]"
                  :required="false"
                />

                <Select
                  v-model="liveOrderBooleanParamsTempModel.isShipped"
                  v-model:boolean-model="searchOrdersModel.isShipped"
                  label="Shipped Status"
                  :options="[
                    {
                      id: 1,
                      name: 'Shipped',
                      mapToBoolean: 'true',
                    },
                    {
                      id: 2,
                      name: 'Not Shipped',
                      mapToBoolean: 'false',
                    },
                  ]"
                  :required="false"
                  :disabled="!!searchOrdersModel.shippedStartDate || !!searchOrdersModel.shippedEndDate"
                />

                <Select
                  v-model="liveOrderBooleanParamsTempModel.isVoid"
                  v-model:boolean-model="searchOrdersModel.isVoid"
                  label="Void Status"
                  :options="[
                    {
                      id: 1,
                      name: 'Voided',
                      mapToBoolean: 'true',
                    },
                    {
                      id: 2,
                      name: 'Not Voided',
                      mapToBoolean: 'false',
                    },
                  ]"
                  :required="false"
                />

                <Select
                  v-model="liveOrderBooleanParamsTempModel.atWarehouse"
                  v-model:boolean-model="searchOrdersModel.atWarehouse"
                  label="At Warehouse Status"
                  :options="[
                    {
                      id: 1,
                      name: 'At Warehouse',
                      mapToBoolean: 'true',
                    },
                    {
                      id: 2,
                      name: 'Not At Warehouse',
                      mapToBoolean: 'false',
                    },
                  ]"
                  :required="false"
                />

                <Select
                  v-model="liveOrderBooleanParamsTempModel.isBackorder"
                  v-model:boolean-model="searchOrdersModel.isBackorder"
                  label="Backorder Status"
                  class="md:col-span-2"
                  :options="[
                    {
                      id: 1,
                      name: 'Backordered',
                      mapToBoolean: 'true',
                    },
                    {
                      id: 2,
                      name: 'Not Backordered',
                      mapToBoolean: 'false',
                    },
                  ]"
                  :required="false"
                />

              </div>

            </section>

          </form>

        </div>

        <!-- Search Buttons -->

        <div class="shrink-0 w-full h-12 sticky top-0 z-1 grid grid-cols-2 bg-core-20 border-t border-core-30">

          <Button
            size="auto"
            mode="naked"
            @click="openFilters = false"
          >
            {{ $t('global.button.cancel') }}
          </Button>

          <Button
            size="auto"
            @click="searchOrders"
          >
            Search
          </Button>

        </div>

      </div>

    </Sidebar>

    <!-- Sidebar :: Children router view -->

    <Sidebar
      :open="['Live Order Details', 'Create Order', 'Live Order Products'].includes(String($route.name))"
      :fit-content="false"
    >

      <div class="w-full h-full">
        <RouterView return-to="Live Orders" />
      </div>

    </Sidebar>

  </div>

</template>
