<script setup lang="ts">

import { computed } from 'vue'
import { formatDate } from '@lib/scripts/utils'

import Loader from '@lib/components/blocks/Loader.vue'
import TrendCard from '@/modules/orders/views/summary/components/TrendCard.vue'
import ProductCard from '@/modules/orders/views/summary/components/ProductCard.vue'
import FulfillmentStatusSummary from '@/modules/orders/views/summary/components/FulfillmentStatusSummary.vue'

import type {
  GaugeChartData,
  StackedAreaChartData,
  TopSellingProduct,
} from '@/modules/orders/types'

const props = defineProps<{
  products?:          TopSellingProduct[]
  statusData?:        GaugeChartData
  onHoldData?:        StackedAreaChartData
  shippedData?:       StackedAreaChartData
  atWarehouseData?:   StackedAreaChartData
  productsPending?:   boolean
  ordersDataPending?: boolean
}>()

const hasProductsData = computed(() => props.products && props.products.length > 0 )

</script>

<template>

  <div class="w-full md:w-[22rem] h-full md:px-8 md:bg-core-10 overflow-hidden overflow-y-auto">

    <!-- Today's Orders -->

    <div class="md:pb-6 md:pt-2 border-b border-core-30">

      <div class="w-full h-10 px-3 md:px-0 md:h-14 flex items-center md:bg-core-10 bg-core-20 border-b md:border-b-0 border-core-30">

        <p class="text-sm md:text-base font-medium">
          Order Processing Insights
        </p>

      </div>

      <FulfillmentStatusSummary
        :status-data="statusData"
        class="bg-core-10 md:bg-core-20"
      />

      <div class="mt-px grid gap-y-px">

        <TrendCard
          name="Shipped"
          class="bg-core-10 md:bg-core-20"
          chart-type="Bar"
          :data="shippedData"
          :pending="ordersDataPending"
          @click="$router.push(`/orders/live?page=1&pageSize=25&isShipped=true&shippedStartDate=${formatDate(new Date(), 'UTC-date-time')}`)"
        />

        <TrendCard
          name="At Warehouse"
          class="bg-core-10 md:bg-core-20"
          chart-type="Line"
          :unit="null"
          :data="atWarehouseData"
          :pending="ordersDataPending"
          @click="$router.push('/orders/live?page=1&pageSize=25&atWarehouse=true')"
        />

        <TrendCard
          name="On Hold"
          class="bg-core-10 md:bg-core-20"
          chart-type="Bar"
          :unit="null"
          :data="onHoldData"
          :pending="ordersDataPending"
          @click="$router.push('/orders/live?page=1&pageSize=25&isHeld=true')"
        />

      </div>

    </div>

    <!-- Top Selling Items -->

    <div class="md:pb-6 md:pt-2">

      <div class="w-full h-10 px-3 md:px-0 md:h-14 flex items-center md:bg-core-10 bg-core-20 border-b md:border-b-0 border-core-30">

        <p class="text-sm md:text-base font-medium">
          Top Selling Items
        </p>

      </div>

      <div v-if="productsPending" class="text-sm text-core py-4 flex items-center justify-center space-x-2">
        <Loader name="Products" />
      </div>

      <div v-else-if="hasProductsData && !productsPending" class="grid gap-y-0.5">

        <ProductCard
          v-for="product, index in products"
          :key="product.sku"
          :index
          :product="product"
          class="bg-core-10 md:bg-core-20"
        />

      </div>

      <div v-else>
        <p class="text-sm text-core py-4 flex items-center justify-center">
          No top selling items found.
        </p>
      </div>

    </div>

  </div>

</template>
