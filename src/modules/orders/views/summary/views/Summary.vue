<script setup lang="ts">

import { growNumberToTarget } from '@lib/scripts/utils'
import { onMounted, reactive, ref } from 'vue'

import {
  getOrdersByChannel,
  getOrdersByStatus,
  getShippedOrdersByChannel,
  getTopSellingItems,
  mapCardData
} from '@/modules/orders/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'
import TodayPanel from '@/modules/orders/views/summary/components/TodayPanel.vue'
import DashboardsPanel from '@/modules/orders/views/summary/components/DashboardsPanel.vue'

import type {
  DoughnutChartData,
  GaugeChartData,
  OrderByChannel,
  OrdersByStatusOrder,
  ShippedOrderByChannel,
  StackedAreaChartData,
  TopSellingProduct,
} from '@/modules/orders/types'

interface ShippedByMethod {
  chart:   StackedAreaChartData
  methods: string[]
  pending: boolean
}

interface ShippedByChannel {
  chart:    StackedAreaChartData
  pending:  boolean
  channels: string[]
}

interface ChannelOrders {
  chart:   {
    raw:      DoughnutChartData
    filtered: DoughnutChartData
  }
  pending:  boolean
  channels: string[]
}

interface TopItems {
  chart:   TopSellingProduct[]
  pending: boolean
}

type Cards = Record<'onHold' | 'shipped' | 'warehouse', StackedAreaChartData>
type ActiveView = 'today' | 'dashboards'

// ---- [ SUMMARY GLOBALS ]

const cards             = reactive<Cards>({ onHold: null, shipped: null, warehouse: null })
const topItems          = reactive<TopItems>({ chart: [], pending: false })
const activeView        = ref<ActiveView>( 'today' )
const channelOrders     = reactive<ChannelOrders>({ chart: { raw: [], filtered: [] }, pending: false, channels: [] })
const shippedByMethod   = reactive<ShippedByMethod>({ chart: [], pending: false, methods: [] })
const shippedByChannel  = reactive<ShippedByChannel>({ chart: [], pending: false, channels: [] })
const fulfillmentStatus = reactive<GaugeChartData>({ group: 'value', value: 0 })

// ---- [ UTILITY FUNCTIONS ]

/**
 * Cleans up the orders data.
 * @description If the order has the same method and date as the previous one, merge them.
 * @param {OrdersByStatusOrder[]} ordersData Orders data to clean up.
 * @param {string[]} methodsList List of shipping methods.
 * @param {'orderDate' | 'shippingDate'} dateKey Key to use for date comparison.
 */
function mergeOrdersData( ordersData: OrdersByStatusOrder[], methodsList: string[], dateKey: 'orderDate' | 'shippingDate' ) {

  if ( !ordersData ) // ------------------------------------------------------ Return early if there is no data.
    return []

  if ( ordersData.length === 0 ) // ------------------------------------------ Return early if the data is an empty array.
    return []

  let orders = JSON.parse( JSON.stringify( ordersData )) // ------------------ Deep clone the orders data.

  const ordersByMethod: Record<string, OrdersByStatusOrder[]> = {} // -------- Create an object to group the orders by method.

  methodsList.forEach(( method ) => { // ------------------------------------- Group the orders by method.

    ordersByMethod[method] = [] // ------------------------------------------- Initialize the method key.

    for ( const order of orders ) {

      if ( order?.shippingMethod === method )
        ordersByMethod[method].push( order ) // ------------------------------ Push the order to his corresponding shippingMethod key.

    }

  })

  Object.keys( ordersByMethod ).forEach(( method ) => {

    const methodOrders = ordersByMethod[method] // -------------------------- Get the orders for the current method.

    methodOrders.sort(( a, b ) => { // -------------------------------------- Sort the orders by shipping date.

      const dateA = new Date( a[dateKey] ).getTime()
      const dateB = new Date( b[dateKey] ).getTime()

      return dateA - dateB

    })

    for ( let i = 0; i < methodOrders.length - 1; i++ ) {

      const nextShippingDate    = methodOrders[i + 1][dateKey] // ----------- Get the next order shipping date.
      const currentShippingDate = methodOrders[i][dateKey] // --------------- Get the current order shipping date.

      if ( currentShippingDate === nextShippingDate ) { // ------------------ If the current order has the same shipping date as the next one,

        methodOrders[i].orderCount += methodOrders[i + 1].orderCount // ----- Merge the order count.
        methodOrders.splice( i + 1, 1 ) // ---------------------------------- Remove the next order.
        i-- // -------------------------------------------------------------- Decrement the index to check the new next order.

      }

    }

  })

  orders = [] // ------------------------------------------------------------ Reset the orders data.

  for ( const method in ordersByMethod )
    orders = [ ...orders, ...ordersByMethod[method] ] // -------------------- Merge the orders data together.

  return orders

}

/**
 * Maps the shipped orders data to the chart.
 * @param {OrdersByStatusOrder[]} orders Orders data to map.
 * @param {string[]} keysList List of shipping methods or order channels.
 */
function mapShippedToChart( orders: OrdersByStatusOrder[] | ShippedOrderByChannel[], keysList: string[], mapKey: 'shippingMethod' | 'orderChannel', dateKey: 'orderDate' | 'shippingDate' = 'shippingDate' ): StackedAreaChartData {

  const mappedOrders: StackedAreaChartData = []

  for ( const key of keysList ) { // ------------------- Iterate trough the available keys.

    for ( const order of orders ) { // ----------------- Iterate trough the orders data.

      if ( order[mapKey] === key ) { // ---------------- If the order has the same map key as the current key,

        mappedOrders.push({
          date:  new Date( order[dateKey] ), // ---- Map the order date to the chart date.
          group: key, // ------------------------------- Set the key as the group.
          value: order.orderCount // ------------------- Set the order count as the value.
        })

      }

    }

  }

  return mappedOrders

}

/**
 * Maps the orders data to the chart.
 * @param {OrderByChannel[]} data Orders data to map.
 * @param {number} threshold Threshold to group small channels into 'Other'.
 */
function mapOrdersByChannelChartData( data: OrderByChannel[], threshold: number = 0.1 ): { group: string, value: number }[] {

  // Calculate the total number of orders

  const total = data.reduce(( sum, order ) => sum + order.orderCount, 0 )
  const channelCounts: Record<string, number> = {}

  // Calculate the total count per channel

  data.forEach(( order ) => {
    channelCounts[order.orderChannel] = ( channelCounts[order.orderChannel] || 0 ) + order.orderCount
  })

  const mappedOrders: { group: string, value: number }[] = []
  let othersTotal = 0

  // Assign counts to channels or group small channels into 'Other'

  Object.entries( channelCounts ).forEach(( [ channel, count ] ) => {

    if ( count / total < threshold )
      othersTotal += count

    else mappedOrders.push({ group: channel, value: count })

  })

  // Add 'Other' category if there is any

  if ( othersTotal > 0 )
    mappedOrders.push({ group: 'Other', value: othersTotal })

  return mappedOrders

}

// ---- [ GET SUMMARY DATA ]

async function getOrdersDataByStatus() {

  shippedByMethod.pending = true

  const { error, payload } = await getOrdersByStatus()

  if ( !error ) {

    // Set the fulfillment status data

    growNumberToTarget(
      payload.fulfillmentStatusPercent,
      value => fulfillmentStatus.value = value
    )

    // Merge all orders and extract their shipping methods.

    const allOrdersData = [
      ...payload?.voided || [],
      ...payload?.onHold || [],
      ...payload?.shipped || [],
      ...payload?.unknown || [],
      ...payload?.backordered || [],
      ...payload?.atWarehouse || [],
    ]

    const existingOrderMethods = allOrdersData.map( o => o.shippingMethod ) // ------- Get the existing order methods.

    shippedByMethod.methods = existingOrderMethods.filter(( method, index ) => // ---- Remove duplicates and set the methods list.
      existingOrderMethods.indexOf( method ) === index
    )

    // Clean up the orders data

    // If there are multiple orders with the same shipping date and same
    // shipping method for the allOrders and shippedOrders data merge them.

    const allOrders       = mergeOrdersData( allOrdersData, shippedByMethod.methods, 'orderDate' )
    const shippedOrders   = mergeOrdersData( payload?.shipped || [], shippedByMethod.methods, 'shippingDate' )

    // Sum the orders count for the onHold and atWarehouse statuses.

    const onHoldOrders    = payload?.onHold?.reduce(( a, b ) => a + b.orderCount, 0 ) || 0
    const warehouseOrders = payload?.atWarehouse?.reduce(( a, b ) => a + b.orderCount, 0 ) || 0

    // Extract orders data and map it to the charts.

    cards.onHold = [ { date: new Date(), group: 'Dateset 1', value: onHoldOrders } ]
    cards.shipped = mapCardData( shippedOrders, 'shippingDate' )
    cards.warehouse = [ { date: new Date(), group: 'Dateset 1', value: warehouseOrders } ]
    shippedByMethod.chart = mapShippedToChart( allOrders, shippedByMethod.methods, 'shippingMethod', 'orderDate' )

  }

  shippedByMethod.pending = false

}

async function getShippedOrdersByChannelData() {

  const { error, payload } = await getShippedOrdersByChannel()

  if ( !error ) {

    shippedByChannel.channels = payload?.orderChannelKeys || []
    shippedByChannel.chart = mapShippedToChart( payload?.orders ?? [], shippedByChannel.channels, 'orderChannel' )
  }

}

async function getOrdersByChannelData() {

  channelOrders.pending = true

  const { error, payload } = await getOrdersByChannel()

  if ( !error ) {

    channelOrders.channels = payload?.orderChannelKeys
    channelOrders.chart.raw = mapOrdersByChannelChartData( payload?.orders ?? [], 0 )
    channelOrders.chart.filtered = mapOrdersByChannelChartData( payload?.orders ?? [] )

  }

  channelOrders.pending = false

}

async function getTopSellingItemsData() {

  topItems.pending = true

  const { error, payload } = await getTopSellingItems()

  if ( !error )
    topItems.chart = payload?.products ?? []

  topItems.pending = false

}

async function getSummaryData() {

  await Promise.all( [
    getOrdersDataByStatus(),
    getOrdersByChannelData(),
    getTopSellingItemsData(),
    getShippedOrdersByChannelData()
  ] )

}

onMounted( getSummaryData )

</script>

<template>

  <div class="w-full h-full grid grid-rows-[1fr_max-content] md:grid-rows-1 overflow-hidden">

    <!-- Desktop Panels -->

    <div class="w-full h-full hidden md:grid grid-cols-[max-content_1fr] md:overflow-hidden bg-core-30">

      <TodayPanel
        :products="topItems.chart"
        :status-data="fulfillmentStatus"
        :on-hold-data="cards.onHold"
        :shipped-data="cards.shipped"
        :products-pending="topItems.pending"
        :at-warehouse-data="cards.warehouse"
        :orders-data-pending="shippedByMethod.pending"
      />

      <DashboardsPanel
        :methods="shippedByMethod.methods"
        :method-orders="shippedByMethod.chart"
        :shipped-orders="shippedByChannel.chart"
        :channel-orders="channelOrders.chart.filtered"
        :method-pending="shippedByMethod.pending"
        :channel-pending="channelOrders.pending"
        :orders-channels="channelOrders.channels"
        :shipped-channels="shippedByChannel.channels"
        :default-channel-orders="channelOrders.chart.raw"
      />

    </div>

    <!-- Mobile Panels -->

    <div class="w-full h-full md:hidden overflow-y-auto">

      <Transition :name="activeView === 'today' ? 'mobile-view-left' : 'mobile-view-right'">

        <TodayPanel
          v-if="activeView === 'today'"
          :products="topItems.chart"
          :status-data="fulfillmentStatus"
          :on-hold-data="cards.onHold"
          :shipped-data="cards.shipped"
          :products-pending="topItems.pending"
          :at-warehouse-data="cards.warehouse"
          :orders-data-pending="shippedByMethod.pending"
        />

        <DashboardsPanel
          v-else
          :methods="shippedByMethod.methods"
          :method-orders="shippedByMethod.chart"
          :shipped-orders="shippedByChannel.chart"
          :channel-orders="channelOrders.chart.filtered"
          :method-pending="shippedByMethod.pending"
          :channel-pending="channelOrders.pending"
          :orders-channels="channelOrders.channels"
          :shipped-channels="shippedByChannel.channels"
          :default-channel-orders="channelOrders.chart.raw"
        />

      </Transition>

    </div>

    <div class="w-full h-12 relative z-1 md:hidden grid grid-cols-2 bg-core-10 border-t border-core-30">

      <Button
        mode="naked"
        size="auto"
        class="h-full grid place-content-center"
        :class="{ 'text-main bg-core-20': activeView === 'today' }"
        @click="activeView = 'today'"
      >

        <Icon name="details" size="m" />

      </Button>

      <Button
        mode="naked"
        size="auto"
        class="h-full grid place-content-center"
        :class="{ 'text-main bg-core-20': activeView === 'dashboards' }"
        @click="activeView = 'dashboards'"
      >

        <Icon name="order-data" size="m" />

      </Button>

    </div>

  </div>

</template>
