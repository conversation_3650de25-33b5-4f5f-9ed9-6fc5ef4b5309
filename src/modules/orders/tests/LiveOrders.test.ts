import { ref } from 'vue'
import { mount } from '@vue/test-utils'
import { ordersParams } from '@/modules/orders/routes'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { bulkExportOrders, getLiveOrdersWidgets, getOrders } from '@/modules/orders/store'

import Sidebar from '@lib/components/blocks/Sidebar.vue'
import LiveOrders from '@/modules/orders/views/LiveOrders.vue'
import CounterWidget from '@/components/CounterWidget.vue'
import LiveWidgetsBlock from '@/modules/orders/components/LiveWidgetsBlock.vue'

// Setup mock route and router at the top level
const mockRoute = ref({
  query:  { customerFirstName: 'John' },
  params: {},
  path:   '/orders/live',
  name:   'Live Orders'
})

const mockRouter = {
  push:         vi.fn(),
  replace:      vi.fn(),
  currentRoute: {
    value: mockRoute
  }
}

// Mock vue-router before any other mocks
vi.mock( 'vue-router', () => ({
  useRouter: () => mockRouter,
  useRoute:  () => mockRoute
}))

const mockOrdersResponse = {
  payload: {
    orders: [
      {
        id:                  27485032,
        owdReference:        '36578745',
        clientReference:     'tb-system-ref-1001',
        facilityCode:        'DC1',
        facilityDisplayName: 'Midwest - Mobridge, SD',
        customerFirstName:   'John',
        customerLastName:    'Doe',
        customerEmail:       '<EMAIL>',
        customerCity:        'Skopje',
        customerState:       'Skopje',
        status:              'BO (Level1)',
        receivedDate:        null,
        shipDate:            null,
        createdDate:         '2024-12-11',
        total:               29.95,
        notes:               '',
      }
    ],
    currentPage: 1,
    pageSize:    50,
    nextPage:    2,
    totalRows:   2796,
    totalPages:  56,
    isLastPage:  false
  }
}

const mockWidgetsData = {
  payload: {
    totalOrders:   2796,
    pendingOrders: 50,
    shippedOrders: 30,
    voidedOrders:  20
  }
}

// Mock store after router is mocked, using vi.fn() to create proper mock functions
vi.mock( '@/modules/orders/store', async ( importOriginal ) => {

  const original = await importOriginal() as any

  return {
    ...original,
    getOrders:            vi.fn(() => mockOrdersResponse ),
    getLiveOrdersWidgets: vi.fn(() => mockWidgetsData ),
    bulkExportOrders:     vi.fn( async () => {
      return Promise.resolve({
        payload: new Blob( [ 'test data' ], { type: 'application/vnd.ms-excel' }),
        error:   null,
        status:  200
      })
    })
  }

})

vi.mock( '@lib/scripts/utils', async ( importOriginal ) => {
  const original = await importOriginal() as any

  return {
    ...original,
    saveFile: vi.fn()
  }

})

// Create a properly typed mock for bulkExportOrders

describe( 'liveOrders - [ view ]', () => {

  let wrapper

  beforeEach( async () => {
    vi.clearAllMocks()

    const teleportTarget = document.createElement( 'div' )
    teleportTarget.id = 'app'
    document.body.appendChild( teleportTarget )

    wrapper = mount( LiveOrders, {
      global: {
        stubs: {
          RouterView:       false,
          Table:            false,
          LiveWidgetsBlock: false,
          Sidebar:          true
        },
        mocks: {
          $route:  mockRoute,
          $router: mockRouter
        }
      }
    })

  })

  describe( 'api Integration', () => {

    it( 'fetches initial orders data with correct params', () => {
      expect( getOrders ).toHaveBeenCalledWith( expect.objectContaining( ordersParams ))
    })

    it( 'updates orders data when API call succeeds', async () => {
      await wrapper.vm.getOrdersList( ordersParams )
      expect( wrapper.vm.total ).toBe( 2796 )
      expect( wrapper.vm.maxPages ).toBe( 56 )
      expect( wrapper.vm.orders ).toHaveLength( 1 )
    })

    it( 'fetches widgets data on mount', () => {
      expect( getLiveOrdersWidgets ).toHaveBeenCalled()
    })
  })

  describe( 'search Functionality', () => {

    it( 'updates params when filters are applied', async () => {
      // Set all possible filter fields
      wrapper.vm.searchOrdersModel.sku = 'TEST-SKU'
      wrapper.vm.searchOrdersModel.isHeld = true
      wrapper.vm.searchOrdersModel.isVoid = true
      wrapper.vm.searchOrdersModel.facility = 'DC1'
      wrapper.vm.searchOrdersModel.poNumber = 'PO123'
      wrapper.vm.searchOrdersModel.groupName = 'Test Group'
      wrapper.vm.searchOrdersModel.isShipped = true
      wrapper.vm.searchOrdersModel.atWarehouse = true
      wrapper.vm.searchOrdersModel.isBackorder = true
      wrapper.vm.searchOrdersModel.owdReference = 'OWD123'
      wrapper.vm.searchOrdersModel.customerCity = 'Test City'
      wrapper.vm.searchOrdersModel.customerState = 'Test State'
      wrapper.vm.searchOrdersModel.customerEmail = '<EMAIL>'
      wrapper.vm.searchOrdersModel.shippingMethod = 'Express'
      wrapper.vm.searchOrdersModel.trackingNumber = 'TRACK123'
      wrapper.vm.searchOrdersModel.createdEndDate = '2024-12-31'
      wrapper.vm.searchOrdersModel.shippedEndDate = '2024-12-31'
      wrapper.vm.searchOrdersModel.clientReference = 'CLIENT123'
      wrapper.vm.searchOrdersModel.createdStartDate = '2024-12-01'
      wrapper.vm.searchOrdersModel.shippedStartDate = '2024-12-01'
      wrapper.vm.searchOrdersModel.customerLastName = 'Doe'
      wrapper.vm.searchOrdersModel.customerFirstName = 'John'

      await wrapper.vm.searchOrders()

      // Verify all params are updated
      expect( wrapper.vm.params.sku ).toBe( 'TEST-SKU' )
      expect( wrapper.vm.params.isHeld ).toBe( true )
      expect( wrapper.vm.params.isVoid ).toBe( true )
      expect( wrapper.vm.params.facility ).toBe( 'DC1' )
      expect( wrapper.vm.params.poNumber ).toBe( 'PO123' )
      expect( wrapper.vm.params.groupName ).toBe( 'Test Group' )
      expect( wrapper.vm.params.isShipped ).toBe( true )
      expect( wrapper.vm.params.atWarehouse ).toBe( true )
      expect( wrapper.vm.params.isBackorder ).toBe( true )
      expect( wrapper.vm.params.owdReference ).toBe( 'OWD123' )
      expect( wrapper.vm.params.customerCity ).toBe( 'Test City' )
      expect( wrapper.vm.params.customerState ).toBe( 'Test State' )
      expect( wrapper.vm.params.customerEmail ).toBe( '<EMAIL>' )
      expect( wrapper.vm.params.shippingMethod ).toBe( 'Express' )
      expect( wrapper.vm.params.trackingNumber ).toBe( 'TRACK123' )
      expect( wrapper.vm.params.createdEndDate ).toBe( '2024-12-31' )
      expect( wrapper.vm.params.shippedEndDate ).toBe( '2024-12-31' )
      expect( wrapper.vm.params.clientReference ).toBe( 'CLIENT123' )
      expect( wrapper.vm.params.createdStartDate ).toBe( '2024-12-01' )
      expect( wrapper.vm.params.shippedStartDate ).toBe( '2024-12-01' )
      expect( wrapper.vm.params.customerLastName ).toBe( 'Doe' )
      expect( wrapper.vm.params.customerFirstName ).toBe( 'John' )
    })

    it( 'resets all filter values to null, page to 1 and pageSize to 25', async () => {

      // Fields are populated in the previous it
      // Perform reset
      await wrapper.vm.resetFilters()
      await wrapper.vm.$nextTick()

      // Verify all searchOrdersModel properties are reset to null
      Object.keys( wrapper.vm.searchOrdersModel ).forEach(( key ) => {
        expect( wrapper.vm.searchOrdersModel[key] ).toBeNull()
      })

      // Verify pagination params maintain their default values
      expect( wrapper.vm.params.page ).toBe( 1 )
      expect( wrapper.vm.params.pageSize ).toBe( 25 )

      // Verify all other params are reset to null
      Object.keys( wrapper.vm.searchOrdersModel ).forEach(( key ) => {
        expect( wrapper.vm.params[key] ).toBeNull()
      })

    })

    it( 'sets isShipped to true when searching for shipped status', () => {

      wrapper.vm.setShippedStatus( '2024-12-11' )

      expect( wrapper.vm.searchOrdersModel.isShipped ).toBe( true )
      expect( wrapper.vm.liveOrderBooleanParamsTempModel.isShipped ).toBe( 1 )

    })

  })

  describe( 'route Navigation', () => {

    it( 'navigates to order details with correct ID', async () => {

      const order = mockOrdersResponse.payload.orders[0]
      const options = wrapper.vm.orderRowOptions( order )

      await options[0].action()

      expect( mockRouter.push ).toHaveBeenCalledWith(
        expect.objectContaining({
          name:   'Live Order Details',
          params: { orderId: order.id }
        })
      )

    })

  })

  describe( 'table Schema', () => {

    it( 'generates correct table columns', () => {

      const schema = wrapper.vm.schema()

      expect( schema ).toContainEqual({
        key:     'owdReference',
        sortKey: 'owdReference',
        label:   'OWD Reference'
      })

      expect( schema ).toContainEqual({
        key:     'clientReference',
        label:   'Client Reference',
        sortKey: 'clientReference'
      })

      expect( schema ).toContainEqual({
        key:     'facilityDisplayName',
        label:   'Facility',
        sortKey: 'facilityDisplayName'
      })

      expect( schema ).toContainEqual({
        key:     'customerFirstName',
        label:   'First Name',
        sortKey: 'customerFirstName'
      })

      expect( schema ).toContainEqual({
        key:     'customerLastName',
        label:   'Last Name',
        sortKey: 'customerLastName'
      })

      expect( schema ).toContainEqual({
        key:     'status',
        label:   'Order Status',
        sortKey: 'orderStatus'
      })

      expect( schema ).toContainEqual({
        key:     'receivedDate',
        label:   'Received Date',
        format:  'date',
        sortKey: 'receivedDate'
      })

      expect( schema ).toContainEqual({
        key:     'shipDate',
        label:   'Ship Date',
        format:  'date',
        sortKey: 'shipDate'
      })

      expect( schema ).toContainEqual({
        key:     'total',
        label:   'Order Total',
        format:  'currency',
        sortKey: 'orderTotal'
      })

      expect( schema ).toContainEqual({
        key:   'notes',
        label: 'Notes'
      })

      expect( schema ).toContainEqual({
        key:     'createdDate',
        label:   'Date Created',
        sortKey: 'createdDate',
        format:  'date'
      })

    })

  })

  describe( 'batch Operations', () => {

    it( 'exports multiple orders to Excel', async () => {

      const selectedOrders = [ { id: 27485032 }, { id: 27485031 } ]

      // Call the batch export action
      await wrapper.vm.ordersBatchOptions[0].action( selectedOrders )

      // Verify bulkExportOrders was called with correct IDs
      expect( bulkExportOrders ).toHaveBeenCalledWith( [ 27485032, 27485031 ] )

    })

  })

  describe( 'filter Removal', () => {

    it( 'removes single filter correctly', () => {

      wrapper.vm.params.customerFirstName = 'Test'
      wrapper.vm.searchOrdersModel.customerFirstName = 'Test'
      wrapper.vm.removeFilter( 'customerFirstName' )

      expect( wrapper.vm.params.customerFirstName ).toBeNull()
      expect( wrapper.vm.searchOrdersModel.customerFirstName ).toBeNull()

    })

    it( 'removes shippedStartDate and shippedEndDate when isShipped filter is removed', () => {

      wrapper.vm.params.isShipped = true
      wrapper.vm.params.shippedStartDate = '2024-12-01'
      wrapper.vm.params.shippedEndDate = '2024-12-31'

      wrapper.vm.removeFilter( 'isShipped' )

      expect( wrapper.vm.params.isShipped ).toBeNull()
      expect( wrapper.vm.params.shippedStartDate ).toBeNull()
      expect( wrapper.vm.params.shippedEndDate ).toBeNull()

    })

    it( 'resets all filter parameters when reset button is clicked', async () => {

      // Set various filter values
      const testFilters = {
        customerFirstName: 'John',
        customerLastName:  'Doe',
        customerEmail:     '<EMAIL>',
        owdReference:      'REF123',
        clientReference:   'CLIENT456',
        sku:               'SKU789',
        facility:          'FACILITY1',
        poNumber:          'PO123',
        trackingNumber:    'TRACK456',
        customerCity:      'New York',
        customerState:     'NY',
        groupName:         'Group1',
        shippingMethod:    'Express',
        isHeld:            true,
        isVoid:            true,
        isShipped:         true,
        atWarehouse:       true,
        isBackorder:       true,
        createdStartDate:  '2024-01-01',
        createdEndDate:    '2024-12-31',
        shippedStartDate:  '2024-01-01',
        shippedEndDate:    '2024-12-31'
      }

      // Apply all test filters to both params and searchOrdersModel
      Object.entries( testFilters ).forEach(( [ key, value ] ) => {
        wrapper.vm.params[key] = value
        wrapper.vm.searchOrdersModel[key] = value
        if ( key.startsWith( 'is' ))
          wrapper.vm.liveOrderBooleanParamsTempModel[key] = 1

      })

      await wrapper.vm.$nextTick()

      // Verify filters are applied
      expect( wrapper.vm.hasFiltersApplied ).toBe( true )

      // Find and click the reset button
      const resetButton = wrapper.find( '[data-button="reset-filters"]' )
      await resetButton.trigger( 'click' )

      // Verify all params are reset to null except pagination
      Object.keys( wrapper.vm.params ).forEach(( key ) => {
        if ( ![ 'page', 'pageSize' ].includes( key )) {
          expect( wrapper.vm.params[key] ).toBeNull()
          expect( wrapper.vm.searchOrdersModel[key] ).toBeNull()
        }
      })

      // Verify boolean params in temp model are reset
      const booleanKeys = [ 'isHeld', 'isVoid', 'isShipped', 'atWarehouse', 'isBackorder' ]
      booleanKeys.forEach(( key ) => {
        expect( wrapper.vm.liveOrderBooleanParamsTempModel[key] ).toBeNull()
      })

      // Verify pagination params maintain default values
      expect( wrapper.vm.params.page ).toBe( 1 )
      expect( wrapper.vm.params.pageSize ).toBe( 25 )

      // Verify hasFiltersApplied is false after reset
      expect( wrapper.vm.hasFiltersApplied ).toBe( false )

    })

    it( 'resets the search model when modal reset button is clicked', async () => {

      // Set all initial values in search model
      wrapper.vm.searchOrdersModel.customerLastName = 'Doe'
      wrapper.vm.searchOrdersModel.customerEmail = '<EMAIL>'
      wrapper.vm.searchOrdersModel.owdReference = 'REF123'
      wrapper.vm.searchOrdersModel.clientReference = 'CLIENT456'
      wrapper.vm.searchOrdersModel.sku = 'SKU789'
      wrapper.vm.searchOrdersModel.facility = 'FACILITY1'
      wrapper.vm.searchOrdersModel.poNumber = 'PO123'
      wrapper.vm.searchOrdersModel.trackingNumber = 'TRACK456'
      wrapper.vm.searchOrdersModel.customerCity = 'New York'
      wrapper.vm.searchOrdersModel.customerState = 'NY'
      wrapper.vm.searchOrdersModel.groupName = 'Group1'
      wrapper.vm.searchOrdersModel.shippingMethod = 'Express'
      wrapper.vm.searchOrdersModel.isHeld = true
      wrapper.vm.searchOrdersModel.isVoid = true
      wrapper.vm.searchOrdersModel.isShipped = true
      wrapper.vm.searchOrdersModel.atWarehouse = true
      wrapper.vm.searchOrdersModel.isBackorder = true
      wrapper.vm.searchOrdersModel.createdStartDate = '2024-01-01'
      wrapper.vm.searchOrdersModel.createdEndDate = '2024-12-31'
      wrapper.vm.searchOrdersModel.shippedStartDate = '2024-01-01'
      wrapper.vm.searchOrdersModel.shippedEndDate = '2024-12-31'

      wrapper.vm.liveOrderBooleanParamsTempModel.isShipped = 1
      wrapper.vm.liveOrderBooleanParamsTempModel.isHeld = 1
      wrapper.vm.liveOrderBooleanParamsTempModel.isVoid = 1
      wrapper.vm.liveOrderBooleanParamsTempModel.atWarehouse = 1

      await wrapper.vm.$nextTick()

      const sidebar = wrapper.findComponent( Sidebar )

      // Find and click the modal reset button
      const resetButton = sidebar.find( '[data-button="reset-filters-modal"]' )
      await resetButton.trigger( 'click' )

      await wrapper.vm.$nextTick()

      // Verify searchOrdersModel values are reset to null
      Object.keys( wrapper.vm.searchOrdersModel ).forEach(( key ) => {
        expect( wrapper.vm.searchOrdersModel[key] ).toBeNull()
      })

      // Verify boolean temp model values are reset
      Object.keys( wrapper.vm.liveOrderBooleanParamsTempModel ).forEach(( key ) => {
        expect( wrapper.vm.liveOrderBooleanParamsTempModel[key] ).toBeNull()
      })

    })

  })

  describe( 'widget Filtering', () => {

    it( 'applies widget filters and resets previous filters', async () => {

      const widgetFilters = {
        isHeld:      true,
        isBackorder: false
      }

      wrapper.vm.params.customerFirstName = 'Test' // Existing filter
      await wrapper.vm.filterOrdersByWidget( widgetFilters )

      expect( wrapper.vm.params.customerFirstName ).toBeNull() // Should be reset
      expect( wrapper.vm.params.isHeld ).toBe( true )
      expect( wrapper.vm.params.isBackorder ).toBe( false )

    })

  })

})

describe( 'liveWidgetsBlock', () => {

  const mockWidgets = {
    onHoldCount:      34,
    backorderedCount: 108,
    atWarehouseCount: 171,
    shippedCount:     2288,
    voidCount:        25
  }

  // Updated mock CounterWidget component to respect disabled prop
  const createWrapper = ( props ) => {
    return mount( LiveWidgetsBlock, {
      props,
    })
  }

  describe( 'rendering', () => {

    it( 'renders all five widgets when widgets prop is provided', () => {

      const wrapper = createWrapper({ widgets: mockWidgets })
      const counterWidgets = wrapper.findAllComponents( CounterWidget )

      expect( counterWidgets ).toHaveLength( 5 )

    })

    it( 'passes correct props to each CounterWidget', () => {

      const wrapper = createWrapper({ widgets: mockWidgets })
      const counterWidgets = wrapper.findAllComponents( CounterWidget )

      // Test On Hold widget
      expect( counterWidgets[0].props()).toMatchObject({
        icon:     'on-hold',
        title:    'On Hold',
        color:    'warning',
        count:    34,
        disabled: false
      })

      // Test Backorder widget
      expect( counterWidgets[1].props()).toMatchObject({
        icon:     'backorder',
        title:    'Backorder',
        color:    'data1-120',
        count:    108,
        disabled: false
      })

      // Test At Warehouse widget
      expect( counterWidgets[2].props()).toMatchObject({
        icon:     'at-warehouse-widget',
        title:    'At Warehouse',
        color:    'main',
        count:    171,
        disabled: false
      })

      // Test Shipped widget
      expect( counterWidgets[3].props()).toMatchObject({
        icon:     'shipped-widget',
        title:    'Shipped',
        color:    'success',
        count:    2288,
        disabled: false
      })

      // Test Void widget
      expect( counterWidgets[4].props()).toMatchObject({
        icon:     'void',
        title:    'Void',
        color:    'error',
        count:    25,
        disabled: false
      })

    })

    it( 'handles null widgets prop gracefully', () => {

      const wrapper = createWrapper({ widgets: null })
      const counterWidgets = wrapper.findAllComponents( CounterWidget )

      counterWidgets.forEach(( widget ) => {
        expect( widget.props( 'count' )).toBeUndefined()
      })

    })

    it( 'respects disabled prop', () => {

      const wrapper = createWrapper({
        widgets:  mockWidgets,
        disabled: true
      })
      const counterWidgets = wrapper.findAllComponents( CounterWidget )

      counterWidgets.forEach(( widget ) => {
        expect( widget.props( 'disabled' )).toBe( true )
      })

    })

  })

  describe( 'events', () => {

    it( 'emits filter event with correct payload when On Hold widget is clicked', async () => {

      const wrapper = createWrapper({ widgets: mockWidgets })
      await wrapper.findAllComponents( CounterWidget )[0].trigger( 'click' )

      expect( wrapper.emitted( 'filter' )).toBeTruthy()
      expect( wrapper.emitted( 'filter' )[0] ).toEqual( [ { isHeld: true } ] )

    })

    it( 'emits filter event with correct payload when Backorder widget is clicked', async () => {

      const wrapper = createWrapper({ widgets: mockWidgets })
      await wrapper.findAllComponents( CounterWidget )[1].trigger( 'click' )

      expect( wrapper.emitted( 'filter' )).toBeTruthy()
      expect( wrapper.emitted( 'filter' )[0] ).toEqual( [ { isBackorder: true } ] )

    })

    it( 'emits filter event with correct payload when At Warehouse widget is clicked', async () => {

      const wrapper = createWrapper({ widgets: mockWidgets })
      await wrapper.findAllComponents( CounterWidget )[2].trigger( 'click' )

      expect( wrapper.emitted( 'filter' )).toBeTruthy()
      expect( wrapper.emitted( 'filter' )[0] ).toEqual( [ { atWarehouse: true } ] )

    })

    it( 'emits filter event with correct payload when Shipped widget is clicked', async () => {

      const wrapper = createWrapper({ widgets: mockWidgets })
      await wrapper.findAllComponents( CounterWidget )[3].trigger( 'click' )

      expect( wrapper.emitted( 'filter' )).toBeTruthy()
      expect( wrapper.emitted( 'filter' )[0] ).toEqual( [ { isShipped: true } ] )

    })

    it( 'emits filter event with correct payload when Void widget is clicked', async () => {

      const wrapper = createWrapper({ widgets: mockWidgets })
      await wrapper.findAllComponents( CounterWidget )[4].trigger( 'click' )

      expect( wrapper.emitted( 'filter' )).toBeTruthy()
      expect( wrapper.emitted( 'filter' )[0] ).toEqual( [ { isVoid: true } ] )

    })

  })

})
