<script setup lang="ts">

import { useRoute } from 'vue-router'
import { clientId } from '@/modules/auth/store'
import { getDocuments } from '@/modules/profile/store'
import { computed, onMounted, reactive, ref } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Card from '@/modules/profile/components/Card.vue'
import Button from '@lib/components/buttons/Button.vue'
import DatePicker from '@lib/components/inputs/DatePicker.vue'
import InfiniteScroll from '@lib/components/utils/InfiniteScroll.vue'

import type { DocumentModel, DocumentRouteParams } from '@/modules/profile/types'

const route         = useRoute()
const isBilling     = computed(() => route.params?.documentType === 'billing-history' )
const documentType  = computed(() => isBilling.value ? 'invoice' : 'contract' )

const total       = ref<number>( 0 )
const pending     = ref<boolean>( false )
const documents   = ref<DocumentModel[]>( [] )
const isLastPage  = ref<boolean>( false )

const params = reactive<DocumentRouteParams>({
  page:            1,
  clientId:        clientId.value,
  pageSize:        25,
  documentType:    documentType.value,
  periodEndDate:   null,
  periodStartDate: null
})

async function getData( viewParams: DocumentRouteParams ) {

  pending.value = true

  const { payload } = await getDocuments( viewParams )

  documents.value = payload.documents

  total.value = payload.totalRows
  isLastPage.value = payload?.isLastPage

  pending.value = false

}

async function updateData() {

  params.page++

  const { payload } = await getDocuments( params )

  if ( payload?.documents )
    documents.value = [ ...documents.value, ...payload.documents ]

  isLastPage.value = payload?.isLastPage

}

onMounted(() => getData( params ))

</script>

<template>

  <div class="w-full 2xl:max-w-7xl md:max-w-[90%] h-full md:h-[80%] grid grid-rows-[max-content_max-content_1fr] md:grid-rows-[max-content_1fr] bg-core-10 rounded-xs">

    <div class="w-full h-14 flex items-center border-b border-core-30">

      <div class="px-4 flex items-center space-x-3 grow">

        <Icon :name="isBilling ? 'portal-invoice' : 'portal-contract'" size="l" class="text-main" />

        <p class="text-lg font-medium">
          {{ isBilling ? $t('profile.documents.billing.header') : $t('profile.documents.contracts.header') }}
        </p>

      </div>

      <div class="hidden md:grid grid-cols-[11rem_11rem] border-r border-l border-core-30">

        <DatePicker
          v-model="params.periodStartDate"
          mode="naked"
          class="border-r border-core-30"
          :label="$t('profile.documents.label.dateFrom')"
          :required="false"
          :limit-to="params.periodEndDate"
          return-type="MM-DD-YYYY"
          @update:model-value="getData(params)"
        />

        <DatePicker
          v-model="params.periodEndDate"
          mode="naked"
          :label="$t('profile.documents.label.dateTo')"
          :required="false"
          :limit-from="params.periodStartDate"
          return-type="MM-DD-YYYY"
          @update:model-value="getData(params)"
        />

      </div>

      <Button
        type="box"
        size="auto"
        mode="naked"
        class="w-14 h-14"
        :icon="{
          name: 'close',
          size: 'm',
        }"
        @click="$router.push({ name: 'Profile' })"
      />

    </div>

    <div class="md:hidden grid grid-cols-2 border-b border-core-30">

      <DatePicker
        v-model="params.periodStartDate"
        mode="naked"
        size="m"
        class="border-r border-core-30"
        :label="$t('profile.documents.label.dateFrom')"
        :required="false"
        :limit-to="params.periodEndDate"
        @update:model-value="getData(params)"
      />

      <DatePicker
        v-model="params.periodEndDate"
        mode="naked"
        size="m"
        :label="$t('profile.documents.label.dateTo')"
        :required="false"
        :limit-from="params.periodStartDate"
        @update:model-value="getData(params)"
      />

    </div>

    <InfiniteScroll
      :name="documentType === 'invoice' ? $t('profile.documents.billing.name') : $t('profile.documents.contracts.name')"
      :total="total"
      :finished="isLastPage"
      :update-action="updateData"
      :init-data-loading="pending"
    >

      <div class="p-4 grid md:grid-cols-2 lg:grid-cols-3 gap-4">

        <Card
          v-for="record in documents"
          :key="record.id"
          :invoice="record"
        />

      </div>

    </InfiniteScroll>

  </div>

</template>
