<script setup lang="ts">

import { ref } from 'vue'
import { formatDate } from '@lib/scripts/utils'
import { downloadDocument, saveDocumentFile } from '@/modules/profile/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

import type { DocumentModel } from '@/modules/profile/types'

const props = defineProps<{
  invoice: DocumentModel
}>()

const pending = ref<boolean>( false )

async function download() {

  pending.value = true

  const { payload } = await downloadDocument( props.invoice.id )
  saveDocumentFile( payload, props.invoice.documentNumber || props.invoice.documentTitle )

  pending.value = false

}

</script>

<template>

  <Button
    size="auto"
    mode="skinny"
    class="p-3 grid grid-cols-[1fr_max-content] content-center"
    :class="{ 'pointer-events-none opacity-60': pending }"
    @click="download"
  >

    <div class="w-full text-left truncate">

      <p class="truncate font-medium">
        {{ invoice.documentNumber || invoice.documentTitle }}
      </p>

      <p class="text-sm text-core">
        {{ formatDate(invoice.periodStartDate, 'MMM, DD YYYY') }} - {{ formatDate(invoice.periodEndDate, 'MMM, DD YYYY') }}
      </p>

    </div>

    <div class="h-full px-2 grid place-content-center">
      <Icon :name="pending ? 'loading' : 'download'" size="m" class="text-main" />
    </div>

  </Button>

</template>
