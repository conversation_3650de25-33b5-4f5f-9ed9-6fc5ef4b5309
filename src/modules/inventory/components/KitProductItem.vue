<script setup lang="ts">

import { Guard } from '@/plugins/guard'
import { appMode } from '@/store'
import { confirm } from '@lib/store/confirm'
import { computed } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

import type { DraftKitComponent } from '@/modules/inventory/types'

const props = defineProps<{
  pending?:      number
  disabled?:     boolean
  kitProduct:    DraftKitComponent
  inventoryType: 'draft' | 'live'
}>()

defineEmits<{
  edit:    [ toggleSelected: boolean ]
  swap:    [ swapId: number ]
  delete:  [ kitId: number ]
  resolve: [ sku: string ]
}>()

const isAdmin       = computed(() => appMode === 'ADMIN' )
const isRecordError = computed(() => props.kitProduct?.importErrors?.some( err => err.propertyName === 'record' ))

</script>

<template>

  <div class="h-[3.75rem] px-4 flex gap-x-3 items-center border-b border-core-30">

    <div class="w-9 h-9 grid place-content-center border border-core-30 bg-core-10 rounded-xs">
      <Icon name="shaded-product" size="m" />
    </div>

    <div class="grow grid grid-cols-[max-content_1fr] gap-x-2 items-center overflow-hidden">

      <Icon
        v-if="kitProduct?.importErrorCount > 0"
        v-tooltip="{ content: kitProduct.importErrors.map(err => err.errorMsg).join('\n') }"
        name="issue-circle"
        size="m" class="text-error"
      />

      <p class="text-sm truncate">
        {{ kitProduct.sku }}
      </p>

    </div>

    <Guard scope="Inventory.Write">

      <Button
        v-if="!isRecordError && isAdmin && kitProduct?.importErrorCount > 0"
        size="xs"
        mode="skinny"
        class="px-3"
        :disabled="disabled"
        @click="$emit('resolve', kitProduct.sku)"
      >
        <p class="text-sm text-error">
          Resolve
        </p>
      </Button>

    </Guard>

    <Guard scope="Inventory.Write">

      <Button
        v-if="isRecordError && isAdmin"
        size="xs"
        mode="skinny"
        class="px-3"
        :disabled="disabled"
        @click="$emit('swap', kitProduct.id)"
      >

        <p class="text-sm text-main">
          Swap
        </p>

      </Button>

    </Guard>

    <div v-if="!isRecordError" class="w-max h-[1.625rem] px-3 grid place-content-center rounded-xs bg-core-20">

      <p class="text-sm font-medium text-main">
        {{ kitProduct.quantity }} <span class="text-core font-normal">{{ kitProduct.quantity === 1 ? 'Unit' : 'Units' }}</span>
      </p>

    </div>

    <Guard scope="Inventory.Write">

      <Button
        v-if="inventoryType === 'draft' && isAdmin"
        type="box"
        size="xs"
        mode="skinny"
        class="text-error!"
        :icon="{
          name: 'delete',
          size: 'm',
        }"
        :pending="pending === kitProduct.id"
        :disabled="disabled"
        @click="() => confirm({
          header: 'Are you sure you want to delete this kit product?',
          description: 'This action cannot be undone.',
          action: () => $emit('delete', kitProduct.id),
        })"
      />

    </Guard>

  </div>

</template>
