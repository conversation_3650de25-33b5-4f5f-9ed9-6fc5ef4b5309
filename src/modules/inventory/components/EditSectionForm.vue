<script setup lang="ts">

import { computed } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Input from '@lib/components/inputs/Input.vue'
import Toggle from '@lib/components/inputs/Toggle.vue'
import Select from '@lib/components/inputs/Select.vue'
import Button from '@lib/components/buttons/Button.vue'
import Textbox from '@lib/components/inputs/Textbox.vue'

import type { ProductDetailsSection } from '@/modules/inventory/types'
import type { InputType, TextInputProps } from '@lib/types/inputTypes'

const props = defineProps<{
  section:      ProductDetailsSection
  savePending?: boolean
}>()

defineEmits<{
  save:  []
  close: []
}>()

function isTextInput( type: InputType ) {

  return {
    type:   type as TextInputProps<string>['type'],
    isText: [ 'text', 'number', 'strict-number', 'email', 'password', 'phone', 'currency' ].includes( type ),
  }

}

const canSaveChanges = computed(() => props.section.model.length > 0 && props.section.model.filter( field => field.required ).every( field => field.valid ))

</script>

<template>

  <div class="w-full h-full md:min-w-[24rem] grid grid-rows-[max-content_1fr_max-content]">

    <div class="w-full pl-4 flex items-center space-x-3 border-b border-core-30">

      <Icon
        :name="section.iconName" size="m"
        :class="{
          'text-main': section.key !== 'ERRORS',
          'text-error': section.key === 'ERRORS',
        }"
      />

      <p v-if="section.key === 'ERRORS'" class="text-sm font-medium grow">
        Resolve Errors
      </p>

      <p v-else class="text-sm font-medium grow">
        Edit <span class="text-main">[{{ section.name }}]</span>
      </p>

      <div class="h-full border-l border-core-30">

        <Button
          mode="naked"
          type="box"
          :disabled="savePending"
          :icon="{
            name: 'close',
            size: 'm',
          }"
          @click="$emit('close')"
        />

      </div>

    </div>

    <div
      class="md:p-4 grid content-start overflow-hidden overflow-y-auto"
      :class="{
        'bg-core-30 gap-2': section.key === 'ERRORS',
        'md:gap-2': section.key !== 'ERRORS',
      }"
    >

      <div
        v-for="field, index in section.model.filter(field => !field.nonEditable)"
        :key="field.key"
        :class="{
          'pointer-events-none': savePending,
        }"
      >

        <div v-if="field?.error" class="p-4 bg-core-20">

          <p class="text-sm">
            <span class="text-error">[{{ index + 1 }}]</span> {{ field.error }}
          </p>

        </div>

        <Input
          v-if="isTextInput(field.type).isText"
          v-model="field.declaredValue"
          v-model:valid="field.valid"
          :type="isTextInput(field.type).type"
          :label="field.label"
          :min="0"
          :required="field.required"
          :readonly="field.readonly"
        >

          <template #suffix>
            <div v-if="field.suffix" class="h-full px-3 grid place-content-center border-l border-core-30">
              <p class="text-sm text-main">
                {{ field.suffix }}
              </p>
            </div>
          </template>

        </Input>

        <Select
          v-if="field.type === 'select'"
          v-model="field.declaredValue"
          v-model:valid="field.valid"
          :label="field.label"
          :options="field.options"
          :readonly="field.readonly"
        >

          <template #suffix>
            <div v-if="field.suffix" class="h-full px-3 grid place-content-center border-l border-core-30">
              <p class="text-sm text-main">
                {{ field.suffix }}
              </p>
            </div>
          </template>

        </Select>

        <Textbox
          v-if="field.type === 'textbox'"
          v-model="field.declaredValue"
          v-model:valid="field.valid"
          :label="field.label"
          :required="field.required"
          :readonly="field.readonly"
        />

        <div
          v-if="field.type === 'toggle'"
          class="w-full flex items-center border-b border-b-core-30 md:border-none"
          :class="{
            'bg-core-10 px-4 h-[3.375rem]': section.key === 'ERRORS',
            'px-4 md:px-0 h-10': section.key !== 'ERRORS',
          }"
        >

          <Toggle
            v-model="field.declaredValue"
            v-model:valid="field.valid"
            :readonly="field.readonly"
          >
            <p class="text-sm px-4">
              {{ field.label }}
            </p>

          </Toggle>

        </div>

      </div>

    </div>

    <div class="grid">

      <Button
        :pending="savePending"
        :disabled="!canSaveChanges"
        @click="$emit('save')"
      >
        <p>Save Changes</p>
      </Button>

    </div>

  </div>

</template>
