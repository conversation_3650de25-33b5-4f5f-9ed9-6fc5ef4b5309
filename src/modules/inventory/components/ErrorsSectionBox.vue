<script setup lang="ts">

import { capitalize } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'
import SectionBox from '@/modules/inventory/components/SectionBox.vue'

import type { DraftProduct } from '@/modules/inventory/types'

withDefaults( defineProps<{
  type?:  'product' | 'kit'
  errors: DraftProduct['importErrors']
}>(), {
  type: 'product'
})

defineEmits<{
  resolveErrors: []
}>()

</script>

<template>

  <SectionBox

    mode="error"
    :name="`<p>${capitalize(type)} Errors <span class='text-error' >[${errors.length}]</span></p>`"
    icon-name="issue-circle"
  >

    <template #head-slot>

      <div v-if="type === 'product'" class="h-full border-l border-core-30">

        <Button
          size="auto"
          mode="naked"
          class="text-error h-full px-3 flex items-center space-x-3"
          @click="$emit('resolveErrors')"
        >

          <p class="text-sm">
            Resolve Errors
          </p>
          <Icon name="chevron-right" size="m" />

        </Button>

      </div>

    </template>

    <div
      v-for="error in errors"
      :key="error.propertyName"
      class="h-auto min-h-10 px-3 flex items-center space-x-3 border-b border-core-30 last:border-b-0"
    >

      <div>
        <Icon name="issue-circle" size="m" class="text-error" />
      </div>

      <div class="py-2">
        <p class="text-sm">
          {{ error.errorMsg }}
        </p>
      </div>

    </div>

  </SectionBox>

</template>
