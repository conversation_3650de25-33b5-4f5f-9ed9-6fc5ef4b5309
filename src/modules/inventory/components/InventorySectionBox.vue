<script setup lang="ts">

import { ref } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import SectionBox from '@/modules/inventory/components/SectionBox.vue'
import SectionItem from '@/modules/inventory/components/SectionItem.vue'

import type { AvailableStockLevel } from '@/modules/inventory/types'

defineProps<{
  reorderAlertQuantity: number
  availableStockLevels: AvailableStockLevel[]
}>()

const activeFacility = ref<AvailableStockLevel>( null )

</script>

<template>

  <SectionBox name="Inventory" icon-name="portal-inventory" class="w-full">

    <SectionItem v-for="facility in availableStockLevels" :key="facility.facilityCode" :label="facility.facilityName">

      <div class="h-full flex items-center justify-end space-x-3">

        <button
          v-if="facility.lots"
          class="relative text-sm font-medium text-main bg-main-20 px-2 py-0.5 rounded-xs clicked-active-state"
          @click="activeFacility = facility"
        >
          Lots: {{ facility.lots.length }}
        </button>

        <div v-if="facility.lots" class="w-0.5 h-4 bg-core-30" />

        <p class="text-sm">
          {{ facility.unitsAvailable }}
        </p>

        <div
          class="w-4 h-4 relative rounded-full after:absolute after:w-2 after:h-2 after:rounded-full after:top-1 after:left-1"
          :class="{
            'bg-data2-110 after:bg-data2-120': facility.unitsAvailable > reorderAlertQuantity,
            'bg-data1-110 after:bg-data1-120': facility.unitsAvailable <= reorderAlertQuantity,
            'bg-data4-110 after:bg-data4-120': facility.unitsAvailable === 0,
          }"
        />

      </div>

    </SectionItem>

    <Sidebar
      :dim="true"
      :open="!!activeFacility"
      @close="() => activeFacility = null"
    >

      <div class="w-full h-full md:w-[27rem] grid grid-rows-[max-content_1fr] overflow-hidden">

        <div class="w-full h-12 pl-4 flex items-center space-x-3 border-b border-core-30">

          <Icon name="portal-inventory" size="m" class="text-main" />

          <p class="text-sm font-medium grow">
            LOTs <span class="text-main">[{{ activeFacility.facilityName }}]</span>
          </p>

          <Button
            mode="naked"
            type="box"
            :icon="{
              name: 'close',
              size: 'm',
            }"
            @click="() => activeFacility = null"
          />

        </div>

        <div class="w-full h-full overflow-hidden overflow-y-auto">

          <div class="h-10 grid grid-cols-3 items-center border-b border-core-30">

            <div class="h-full col-span-2 px-3 grid items-center border-r border-core-30">
              <p class="text-sm font-medium">
                Lot Number
              </p>
            </div>

            <div class="h-full px-3 grid items-center">
              <p class="text-sm font-medium">
                Lot Quantity
              </p>
            </div>

          </div>

          <div
            v-for="lot in activeFacility.lots"
            :key="lot.lotNumber"
            class="h-10 grid grid-cols-3 items-center border-b border-core-30 even:bg-core-10"
          >

            <div class="h-full col-span-2 px-3 grid items-center border-r border-core-30">

              <p class="text-sm">
                {{ lot.lotNumber }}
              </p>

            </div>

            <div class="h-full px-3 grid items-center">

              <p class="text-sm">
                {{ lot.lotQuantity }}
              </p>

            </div>

          </div>

        </div>

      </div>
    </Sidebar>

  </SectionBox>

</template>
