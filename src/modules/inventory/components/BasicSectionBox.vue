<script setup lang="ts">

import { Guard } from '@/plugins/guard'
import { checkValue, formatCurrency } from '@lib/scripts/utils'

import Icon from '@lib/components/blocks/Icon.vue'
import Toggle from '@lib/components/inputs/Toggle.vue'
import Button from '@lib/components/buttons/Button.vue'
import SectionBox from '@/modules/inventory/components/SectionBox.vue'
import SectionItem from '@/modules/inventory/components/SectionItem.vue'

import type { InputType } from '@lib/types/inputTypes'
import type { ProductDetailsSection, SectionKeys } from '@/modules/inventory/types'

defineProps<{
  section:  ProductDetailsSection
  canEdit?: boolean
}>()

defineEmits<{
  openSectionForm: [ key: SectionKeys ]
}>()

function formatSectionValue( value: string | number | boolean, type: InputType, suffix?: string ) {

  if ( typeof value !== 'boolean' ) {
    if ( !checkValue( value ))
      return '<span class="text-core-60" >/</span>'
  }

  if ( type === 'currency' ) {

    const formattedValue = formatCurrency( value as number )

    if ( suffix )
      return `<p>${formattedValue} <span class="text-main" >${suffix}</span></p>`

    return formatCurrency( value as number )

  }

  if ( suffix )
    return `<p>${value} <span class="text-main" >${suffix}</span></p>`

  return value

}

</script>

<template>

  <SectionBox
    :key="section.name"
    :name="section.name"
    :icon-name="section.iconName"
  >

    <template #head-slot>

      <Guard scope="Inventory.Write">

        <div class="h-full border-l border-core-30">

          <Button
            class="h-full px-4 flex items-center space-x-2"
            size="auto"
            mode="naked"
            :disabled="!canEdit"
            @click="$emit('openSectionForm', section.key)"
          >

            <p class="text-sm">
              Edit
            </p>
            <Icon name="edit" size="s" class="text-main" />

          </Button>

        </div>

      </Guard>

    </template>

    <SectionItem
      v-for="item in section.model.filter(item => !item.hidden)"
      :key="item.key"
      :label="item.label"
      :textbox="item.type === 'textbox'"
      :class="[item?.class, {
        'bg-error/20': item.error,
      }]"
    >

      <div v-if="typeof item.value === 'boolean'" class="w-full grid justify-end">

        <Toggle
          v-model="item.value"
          :readonly="true"
        />

      </div>

      <div v-else-if="item?.to" class="truncate flex items-center justify-end space-x-3">

        <a
          v-if="item?.externalLink"
          :href="String(item?.to)"
          target="_blank"
          class="truncate text-sm text-main-60 underline"
          v-html="formatSectionValue(item.value, item.type, item.suffix)"
        />

        <RouterLink v-else :to="item.to">
          <p class="truncate text-sm text-main-60 underline" v-html="formatSectionValue(item.value, item.type, item.suffix)" />
        </RouterLink>

        <Icon
          v-if="item.error"
          v-tooltip.bottom="{ content: item.error }"
          name="issue-circle"
          size="m"
          class="text-error"
        />

      </div>

      <div v-else class="flex items-center justify-end space-x-3">

        <p
          class="text-sm"
          :class="{
            'text-right truncate': item.type !== 'textbox',
          }"
          v-html="formatSectionValue(item.value, item.type, item.suffix)"
        />

        <Icon
          v-if="item.error"
          v-tooltip.bottom="{ content: item.error }"
          name="issue-circle"
          size="m"
          class="text-error"
        />

      </div>

    </SectionItem>

  </SectionBox>

</template>
