<script setup lang="ts">

import { guard } from '@/plugins/guard'
import { confirm } from '@lib/store/confirm'
import { inventoryParams } from '@/modules/inventory/routes'
import { useRoute, useRouter } from 'vue-router'
import { setNotificationOptions } from '@lib/store/snackbar'
import { computed, reactive, ref, watch } from 'vue'
import { appMode, booleanToYesNo, defaultFacility, facilitiesList, importStatusList } from '@/store'
import { compareObjects, formatDate, removeEmptyKeysFromObject, sanitizeQueryParams, viewSetup } from '@lib/scripts/utils'

import {
  bulkDeleteDraftProducts,
  bulkUploadProducts,
  deleteDraftProduct,
  draftProductType,
  getDraftProducts,
  getDraftProductsWidgets,
  publishDraftProducts
} from '@/modules/inventory/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Table from '@lib/components/table/Table.vue'
import Input from '@lib/components/inputs/Input.vue'
import Button from '@lib/components/buttons/Button.vue'
import Select from '@lib/components/inputs/Select.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import DatePicker from '@lib/components/inputs/DatePicker.vue'
import SearchFilterBadge from '@/components/SearchFilterBadge.vue'
import ImportStatusBadge from '@/components/ImportStatusBadge.vue'
import DraftWidgetsBlock from '@/components/DraftWidgetsBlock.vue'

import type { BatchOption } from '@lib/types/tableTypes'
import type { DraftWidgets, SearchFilter } from '@/types'
import type { DraftKitComponent, DraftProduct, DraftProductsParams } from '@/modules/inventory/types'

interface ProductBooleanParams {
  isActive:        number
  hasErrors:       number
  hasMasterRecord: number
}

const isAdmin = computed(() => appMode === 'ADMIN' )

// ---- PRODUCTS SCHEMAS

function setRecordStatus( key: keyof DraftProduct | keyof DraftKitComponent, record: DraftProduct | DraftKitComponent ): TableRecordStatus {

  const fieldError = record?.importErrors?.find( e => e.propertyName === key )

  if ( !fieldError )
    return null

  return {
    type:    'error',
    message: fieldError.errorMsg
  }

}

function schema( record: DraftProduct ): TableSchema<DraftProduct> {
  return [
    {
      key:     'id',
      label:   'Draft ID',
      sortKey: 'id',
      status:  setRecordStatus( 'id', record )
    },
    {
      key:     'sku',
      label:   'SKU',
      sortKey: 'sku',
      status:  setRecordStatus( 'sku', record )
    },
    {
      key:     'title',
      label:   'Title',
      sortKey: 'title',
      status:  setRecordStatus( 'title', record )
    },
    {
      key:      null,
      label:    'Status',
      lockFlex: true,
      value:    {
        component: ImportStatusBadge,
        props:     {
          status: record?.importStatus
        }
      }
    },
    {
      key:     'masterRecordId',
      sortKey: 'masterRecordId',
      link:    record?.masterRecordId ? { name: 'Live Product Details', params: { productId: record?.masterRecordId } } : null,
      label:   'Product ID',
      value:   record?.masterRecordId ?? null,
      status:  setRecordStatus( 'masterRecordId', record )
    },
    {
      key:     'defaultFacilityCode',
      label:   'Facility Code',
      sortKey: 'defaultFacilityCode',
      hidden:  !isAdmin.value,
      status:  setRecordStatus( 'defaultFacilityCode', record )
    },
    {
      key:     'retailPrice',
      label:   'Price',
      sortKey: 'retailPrice',
      format:  'currency',
      status:  setRecordStatus( 'retailPrice', record )
    },
    {
      key:     'supplier',
      label:   'Supplier',
      sortKey: 'supplier',
      status:  setRecordStatus( 'supplier', record )
    },
    {
      key:     'createdTs',
      label:   'Date Created',
      sortKey: 'createdTs',
      format:  'date',
      status:  setRecordStatus( 'createdTs', record )
    }
  ]
}

function kitSchema( record: DraftKitComponent ): TableSchema<DraftKitComponent> {
  return [
    {
      key:    'id',
      label:  'Draft ID',
      status: setRecordStatus( 'id', record )
    },
    {
      key:    'sku',
      label:  'SKU',
      status: setRecordStatus( 'sku', record )
    },
    {
      key:    'quantity',
      label:  'Required Kit Quantity',
      status: setRecordStatus( 'quantity', record )
    }
  ]
}

// ---- PRODUCTS GLOBALS

const total       = ref<number>( 0 )
const route       = useRoute()
const router      = useRouter()
const params      = reactive<DraftProductsParams>({ ...inventoryParams, ...sanitizeQueryParams( route.query ) })
const widgets     = ref<DraftWidgets>( null )
const products    = ref<DraftProduct[]>( [] )
const maxPages    = ref<number>( 0 )
const hasFacility = computed<boolean>(() => defaultFacility.value !== 'ALL' )
const openFilters = ref<boolean>( false )

function closeBulkUploadDrawer( blockUpdate = true ) {
  blockPageUpdate( blockUpdate )
  router.push({ name: 'Draft Products' })
}

// ---- SEARCH PRODUCTS

/**
 * Generates the schema for the search filters.
 * @param selectedFilters - The selected filters.
 */

function generateFilters( selectedFilters: Partial<DraftProductsParams> ): SearchFilter<DraftProductsParams>[] {
  return [
    {
      key:   'sku',
      label: 'SKU',
      value: selectedFilters?.sku ?? null
    },
    {
      key:   'productTitle',
      label: 'Title',
      value: selectedFilters?.productTitle ?? null
    },
    {
      key:   'productType',
      label: 'Product Type',
      value: draftProductType.find( item => item.id === selectedFilters.productType )?.name ?? null
    },
    {
      key:   'createdStartDate',
      label: 'From Date Created',
      value: selectedFilters?.createdStartDate ? formatDate( selectedFilters?.createdStartDate, 'MMM, DD YYYY' ) : null
    },
    {
      key:   'createdEndDate',
      label: 'To Date Created',
      value: selectedFilters?.createdEndDate ? formatDate( selectedFilters?.createdEndDate, 'MMM, DD YYYY' ) : null
    },
    {
      key:   'hasMasterRecord',
      label: 'Has Master Record',
      value: booleanToYesNo( selectedFilters?.hasMasterRecord )
    },
    {
      key:   'hasErrors',
      label: 'Has Errors',
      value: booleanToYesNo( selectedFilters?.hasErrors )
    },
    {
      key:   'isActive',
      label: 'Is Active',
      value: booleanToYesNo( selectedFilters?.isActive )
    },
    {
      key:   'importStatus',
      label: 'Import Status',
      value: selectedFilters.importStatus || null
    },
    {
      key:   'defaultFacilityCode',
      label: 'Facility Code',
      value: selectedFilters.defaultFacilityCode || null
    },
    {
      key:   'supplier',
      label: 'Supplier',
      value: selectedFilters.supplier || null
    }
  ]
}

/**
 * Applies the selected filters to the search model.
 */

function searchProducts() {

  Object.keys( searchModel ).forEach(( key ) => {
    params[key] = searchModel[key]
  })

  openFilters.value = false

}

/**
 * Resets the filters to their default values.
 */

function resetFilters() {
  Object.keys( searchDefaultModel ).forEach(( key ) => {
    params[key] = searchDefaultModel[key]
    searchModel[key] = searchDefaultModel[key]
    searchBooleansModel.value[key] = searchDefaultModel[key]
  })
}

/**
 * Resets the search model to its default values.
 */

function resetSearchModel() {
  Object.keys( searchDefaultModel ).forEach(( key ) => {
    searchModel[key] = searchDefaultModel[key]
    searchBooleansModel.value[key] = searchDefaultModel[key]
  })
}

function removeFilter( key: ( keyof DraftProductsParams ) | string ) {
  params[key] = null
  searchModel[key] = null
  searchBooleansModel.value[key] = null
}

/**
 * Filters the products from the widgets params.
 * @param filterParams - The params to filter by.
 */

function filterProducts( filterParams: DraftProductsParams ) {

  resetFilters()

  Object.keys( filterParams ).forEach(( key ) => {
    params[key] = filterParams[key]
    searchModel[key] = filterParams[key]
  })

}

const searchDefaultModel: DraftProductsParams = {
  sku:                 null,
  supplier:            null,
  isActive:            null,
  hasErrors:           null,
  productType:         null,
  productTitle:        null,
  importStatus:        null,
  createdEndDate:      null,
  hasMasterRecord:     null,
  createdStartDate:    null,
  defaultFacilityCode: null
}

const searchModel = reactive<DraftProductsParams>({ ...searchDefaultModel, ...sanitizeQueryParams( route.query ) })

const searchBooleansModel = ref<ProductBooleanParams>({
  isActive:        null,
  hasErrors:       null,
  hasMasterRecord: null
})

const searchFilters = computed(() => generateFilters( removeEmptyKeysFromObject( params )))
const filtersActive = computed(() => searchFilters.value.some( filter => !!filter.value ))

// Watch for changes in the URL query params
// If the URL params are changed but the state params are not,
// update the filter models to match the URL params.

watch( route, ( n ) => {

  const URLParams = sanitizeQueryParams( n.query )
  const cleanParams = removeEmptyKeysFromObject( params )

  if ( !compareObjects( URLParams, cleanParams )) {

    // Add new params to the models

    for ( const key in URLParams ) {

      if ( searchModel.hasOwnProperty( key ))
        searchModel[key] = URLParams[key]

    }

    // Remove non existing params from the models

    for ( const key in searchModel ) {

      if ( !URLParams.hasOwnProperty( key ))
        searchModel[key] = null

    }

    for ( const key in searchBooleansModel.value ) {

      if ( !URLParams.hasOwnProperty( key ))
        searchBooleansModel.value[key] = null

    }

  }

})

// ---- PRODUCT OPTIONS && BULK OPTIONS

function productOptions( record: DraftProduct ): DropListOption[] {
  return [
    {
      id:     1,
      name:   'Details',
      icon:   { name: 'edit' },
      action: () => { router.push({ name: 'Draft Product Details', params: { productId: record.id } }) }
    },
    {
      id:     2,
      name:   'Delete',
      icon:   { name: 'delete', color: 'red' },
      hidden: !guard( 'Inventory.Write' ) || ![ 'Draft', 'Failed', 'Processed' ].includes( record.importStatus ),
      action: () => confirm({
        header:      'Are you sure you want to delete this product?',
        description: 'This action cannot be undone.',
        action:      () => deleteDraftProduct( record.id ),
        onSuccess:   () => {
          updateView()
          setNotificationOptions({ message: 'Draft product is deleted successfully.' })
        }
      })
    }
  ]
}

const productsBulkOptions: BatchOption<DraftProduct>[] = [
  {
    id:             1,
    icon:           'live',
    type:           'positive',
    group:          'Bulk Actions',
    action:         publishSelectedProducts,
    filter:         r => r.importStatus === 'Draft' && r.importErrorCount === 0,
    actionName:     'Publish',
    description:    'Ready to Publish',
    pendingMessage: 'Publishing Products',
  },
  {
    id:             2,
    icon:           'delete',
    type:           'negative',
    group:          'Bulk Actions',
    action:         selected => deleteSelectedDraftProducts( selected.map( product => product.id )),
    filter:         r => [ 'Draft', 'Failed' ].includes( r.importStatus ),
    actionName:     'Delete',
    pendingMessage: 'Deleting Products',
  }
]

// ---- PUBLISH & DELETE PRODUCTS

async function publishSelectedProducts( selectedProducts: DraftProduct[] ) {

  const { error } = await publishDraftProducts( selectedProducts.map( product => product.id ))

  if ( !error ) {

    setNotificationOptions({ message: 'Draft product is published and is now pending.' })
    await updateView()

  }

}

async function deleteSelectedDraftProducts( productsIds: number[] ) {

  const { error } = await bulkDeleteDraftProducts( productsIds )

  if ( !error ) {

    setNotificationOptions({ message: 'The selected draft products were deleted successfully.' })
    await updateView()

  }

}

// ---- GET DRAFT PRODUCTS VIEW DATA

function mapChildrenToProducts( records: Tablify<DraftProduct, DraftKitComponent>[] ) {

  if ( !records )
    return null

  records.forEach(( record ) => {

    if ( record?.importErrorCount ) {

      const statusMessage
        = record?.importErrors?.some( e => e.propertyName === 'record' )
          ? record?.importErrors?.find( e => e.propertyName === 'record' )?.errorMsg
          : `This Product has ${record?.importErrorCount} import error${record?.importErrorCount > 1 ? 's' : ''}.`

      const status: TableRecordStatus = {
        type:    'error',
        count:   record?.importErrorCount,
        message: statusMessage
      }

      record.tableRecordStatus = status

    }

    if ( record.kitComponents ) {

      record.nested = {
        name:         'Kit Components',
        type:         'nested',
        schema:       kitSchema,
        records:      [],
        recordMapKey: 'id'
      }

      record.kitComponents.forEach(( kitComponent: Tablify<DraftKitComponent> ) => {

        if ( kitComponent?.importErrorCount ) {

          const statusMessage
            = kitComponent?.importErrors?.some( e => e.propertyName === 'record' )
              ? kitComponent?.importErrors?.find( e => e.propertyName === 'record' )?.errorMsg
              : `This Product has ${kitComponent?.importErrorCount} import error${kitComponent?.importErrorCount > 1 ? 's' : ''}.`

          const status: TableRecordStatus = {
            type:    'error',
            count:   kitComponent?.importErrorCount,
            message: statusMessage
          }

          kitComponent.tableRecordStatus = status

        }

      })

      record.nested.records = record.kitComponents

    }

  })

  return records

}

async function getProducts( viewParams: DraftProductsParams ) {

  const { error, status, payload } = await getDraftProducts( viewParams )

  if ( !error ) {
    total.value = payload.totalRows ?? 0
    maxPages.value = payload.totalPages ?? 0
    products.value = mapChildrenToProducts( payload?.stageProducts ) ?? []
  }

  return { error, status, payload }

}

async function getWidgets() {

  const { error, status, payload } = await getDraftProductsWidgets()

  if ( !error )
    widgets.value = payload

  return { error, status, payload }

}

function downloadExampleCsv() {
  window.open( 'https://owdnewportalstaticassets.z5.web.core.windows.net/bulk_products_upload_example.csv', '_blank' )
}

function downloadExampleExcel() {
  window.open( 'https://owdnewportalstaticassets.z5.web.core.windows.net/bulk_products_upload_example.xlsx', '_blank' )
}

function downloadInstructionsPdf() {
  window.open( 'https://owdnewportalstaticassets.z5.web.core.windows.net/Bulk_Upload_Inventory.pdf', '_blank' )
}

async function uploadProducts( file: File ) {

  const formData = new FormData()

  formData.append( 'file', file )

  const response = await bulkUploadProducts( formData )

  if ( !response.error ) {

    setNotificationOptions({
      message: 'Success',
      details: `Imported ${response.payload.totalRowsInserted} out of ${response.payload.totalRowsInserted + response.payload.totalRowsRejected} rows.`
    })

    closeBulkUploadDrawer( false )
  }

}

// ---- VIEW SETUP

const { pending, updateView, blockPageUpdate } = viewSetup(
  'Draft Products',
  params,
  router,
  [
    { callback: getProducts },
    { callback: getWidgets, ignoreParams: 'all' }
  ],
  null,
  true
)

</script>

<template>

  <div class="h-full grid grid-rows-[max-content_1fr]">

    <DraftWidgetsBlock
      :widgets
      :disabled="pending"
      @filter="filterProducts"
    />

    <div class="h-full grid md:px-[1.625rem] md:pb-0 overflow-hidden sm:pt-4">

      <Table
        v-model:params="params"
        name="Draft Products"
        class="md:shadow-custom md:border md:border-core-30"
        record-map-key="id"
        :flex="true"
        :schema="schema"
        :records="products"
        :pending="pending"
        :selectable="guard('Inventory.Write')"
        :pagination="{ total, maxPages }"
        resource-name="Product"
        :batch-options="productsBulkOptions"
        :record-options="productOptions"
        :enable-column-chooser="true"
      >

        <template #table-head>

          <div class="flex items-center">

            <div class="border-r border-core-30">

              <Button
                mode="naked"
                size="m"
                class="px-3 lg:px-4 flex items-center lg:space-x-2"
                :is-active="openFilters"
                @click="openFilters = !openFilters"
              >
                <Icon
                  size="m"
                  name="search"
                />

                <p class="hidden lg:block text-sm font-medium">
                  {{ $t('global.label.search', { name: 'Draft Products' }) }}
                </p>

              </Button>

            </div>

            <div v-if="filtersActive" class="border-r border-core-30">

              <Button
                size="m"
                type="box"
                mode="naked"
                :icon="{
                  name: 'reset',
                  size: 'm',
                }"
                @click="resetFilters"
              />

            </div>

          </div>

        </template>

        <template #table-neck>

          <div v-if="filtersActive" class="py-1 px-2 flex flex-wrap items-center border-b border-core-30">

            <SearchFilterBadge
              v-for="filter in searchFilters"
              v-show="filter.value"
              :key="filter.key"
              :filter="filter"
              @remove-filter="removeFilter"
            />

          </div>

        </template>

      </Table>

    </div>

    <!-- Sidebar :: Filter Products -->

    <Sidebar
      :dim="true"
      :open="openFilters"
      :strict="false"
      @close="openFilters = false"
    >

      <div class="w-full h-full flex flex-col">

        <!-- Search Header -->

        <div class="w-full h-12 sticky top-0 z-1 flex shrink-0 items-center bg-core-20 border-b border-core-30">

          <div class="h-full px-4 flex items-center space-x-3 grow border-r border-core-30">

            <p class="text-sm font-medium">
              {{ $t('global.label.search', { name: 'Draft Products' }) }}
            </p>

          </div>

          <div class="h-full border-r border-core-30">

            <Button
              mode="naked"
              size="auto"
              class="h-full px-4 flex items-center space-x-2"
              @click="resetSearchModel"
            >

              <p class="text-sm">
                Reset Filters
              </p>

              <Icon name="reset" size="s" class="text-main" />

            </Button>

          </div>

          <Button
            type="box"
            size="auto"
            mode="naked"
            class="w-12 h-full min-w-[3rem]"
            :icon="{
              name: 'close',
              size: 'm',
            }"
            @click="openFilters = false"
          />

        </div>

        <!-- Search Options -->

        <div class="w-full h-full overflow-hidden overflow-y-auto">

          <form class="w-full md:w-[46rem] md:max-w-[46rem] md:px-6 grid md:gap-4" @submit.prevent>

            <!-- Date Created -->

            <section>

              <div class="h-12 sticky top-0 z-1 px-4 md:px-2 flex items-center md:col-span-2 bg-core-20 border-b md:border-b-0 border-core-30">
                <p class="text-xs text-core uppercase">
                  {{ $t('orders.searchFilterLabel.dateCreated') }}
                </p>
              </div>

              <div class="grid md:grid-cols-2 md:gap-4">

                <DatePicker
                  v-model="searchModel.createdStartDate"
                  :label="$t('orders.searchFilterLabel.startDate')"
                  :required="false"
                  :limit-to="searchModel.createdEndDate"
                />

                <DatePicker
                  v-model="searchModel.createdEndDate"
                  :label="$t('orders.searchFilterLabel.endDate')"
                  :required="false"
                  :limit-from="searchModel.createdStartDate"
                />

              </div>

            </section>

            <!-- Search By -->

            <section>

              <div class="h-12 w-full sticky top-0 z-1 px-4 md:px-2 flex items-center bg-core-20 border-b md:border-b-0 border-core-30">

                <p class="text-xs text-core uppercase">
                  {{ $t('global.label.search', { name: 'By' }) }}
                </p>

              </div>

              <div class="grid md:grid-cols-2 md:gap-4">

                <Select
                  v-model="searchModel.productType"
                  label="Product Type"
                  class="hidden md:block"
                  :required="false"
                  :options="draftProductType"
                  :teleport="false"
                />

                <Select
                  v-model="searchModel.productType"
                  label="Product Type"
                  class="md:hidden"
                  :required="false"
                  :options="draftProductType"
                />

                <Select
                  v-model="searchModel.importStatus"
                  label="Import Status"
                  class="hidden md:block"
                  :required="false"
                  :options="importStatusList"
                  :teleport="false"
                />

                <Select
                  v-model="searchModel.importStatus"
                  label="Import Status"
                  class="md:hidden"
                  :required="false"
                  :options="importStatusList"
                />

                <Input
                  v-model="searchModel.sku"
                  label="SKU"
                  :required="false"
                />

                <Input
                  v-model="searchModel.productTitle"
                  label="Title"
                  :required="false"
                />

                <Input
                  v-model="searchModel.supplier"
                  label="Supplier"
                  :class="{
                    'md:col-span-2': hasFacility,
                  }"
                  :required="false"
                />

                <Select
                  v-show="!hasFacility"
                  v-model="searchModel.defaultFacilityCode"
                  :disabled="hasFacility"
                  :label="$t('orders.searchFilterLabel.facility')"
                  class="hidden md:block"
                  :required="false"
                  :options="facilitiesList"
                  :teleport="false"
                />

                <Select
                  v-show="!hasFacility"
                  v-model="searchModel.defaultFacilityCode"
                  :disabled="hasFacility"
                  :label="$t('orders.searchFilterLabel.facility')"
                  class="md:hidden"
                  :required="false"
                  :options="facilitiesList"
                />

              </div>

            </section>

            <!-- Filter By Other Params -->

            <section>

              <div class="px-4 md:px-2 h-12 sticky top-0 z-1 flex items-center md:col-span-2 bg-core-20 border-b md:border-b-0 border-core-30">

                <p class="text-xs text-core uppercase">
                  Filter By Other Parameters
                </p>

              </div>

              <div class="grid md:grid-cols-2 md:gap-4 md:pb-4">

                <Select
                  v-model="searchBooleansModel.hasMasterRecord"
                  v-model:boolean-model="searchModel.hasMasterRecord"
                  label="Master Record"
                  :options="[
                    {
                      id: 1,
                      name: 'Has Master Record',
                      mapToBoolean: 'true',
                    },
                    {
                      id: 2,
                      name: 'No Master Record',
                      mapToBoolean: 'false',
                    },
                  ]"
                  :required="false"
                />

                <Select
                  v-model="searchBooleansModel.hasErrors"
                  v-model:boolean-model="searchModel.hasErrors"
                  label="Errors"
                  :options="[
                    {
                      id: 1,
                      name: 'Has Errors',
                      mapToBoolean: 'true',
                    },
                    {
                      id: 2,
                      name: 'No Errors',
                      mapToBoolean: 'false',
                    },
                  ]"
                  :required="false"
                />

                <Select
                  v-model="searchBooleansModel.isActive"
                  v-model:boolean-model="searchModel.isActive"
                  label="Is Active"
                  class="md:col-span-2"
                  :options="[
                    {
                      id: 1,
                      name: 'Active',
                      mapToBoolean: 'true',
                    },
                    {
                      id: 2,
                      name: 'Inactive',
                      mapToBoolean: 'false',
                    },
                  ]"
                  :required="false"
                />

              </div>

            </section>

          </form>

        </div>

        <!-- Search Buttons -->

        <div class="shrink-0 w-full h-12 sticky top-0 z-1 grid grid-cols-2 bg-core-20 border-t border-core-30">

          <Button
            size="auto"
            mode="naked"
            @click="openFilters = false"
          >
            {{ $t('global.button.cancel') }}
          </Button>

          <Button
            size="auto"
            @click="searchProducts"
          >
            Search
          </Button>

        </div>

      </div>

    </Sidebar>

    <!-- Sidebar :: Draft Product Details -->

    <Sidebar
      :open="['Draft Product Details', 'Draft Product Details Inventory', 'Create Product'].includes(String($route.name))"
      :fit-content="false"
    >

      <div class="w-full h-full">
        <RouterView return-to="Draft Products" />
      </div>

    </Sidebar>

    <!-- Sidebar :: Upload Bulk Products -->

    <Sidebar
      :open="['Upload Bulk Products'].includes(String($route.name))"
      :dim="true"
      :strict="true"
      @close="closeBulkUploadDrawer"
    >

      <div class="w-full h-full">
        <RouterView
          drawer-name="Products"
          :download-example-csv="downloadExampleCsv"
          :download-example-excel="downloadExampleExcel"
          :download-instructions-pdf="downloadInstructionsPdf"
          :bulk-upload-items="uploadProducts"
          @close="closeBulkUploadDrawer"
          @update="closeBulkUploadDrawer(false)"
        />
      </div>

    </Sidebar>

  </div>

</template>
