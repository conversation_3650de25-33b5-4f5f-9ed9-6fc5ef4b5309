<script setup lang="ts">

import {
  activeStep,
  createNewProduct,
  createProductPending,
  kitComponentsValid,
  selectedKitProducts
} from '@/modules/inventory/views/create-product/store'

import Button from '@lib/components/buttons/Button.vue'
import InventoryPanel from '@/components/InventoryPanel.vue'

</script>

<template>

  <div class="w-full h-full grid grid-rows-[1fr_max-content]">

    <InventoryPanel
      v-model:selected-products="selectedKitProducts"
      :disabled="createProductPending"
      :allow-locking="false"
      :allow-kit-items="false"
      :allow-price-update="false"
    />

    <div class="w-full sticky bottom-0 z-2 grid grid-cols-2 md:hidden bg-core-20">

      <Button
        mode="secondary"
        :disabled="!kitComponentsValid"
        :pending="createProductPending"
        @click="() => createNewProduct((productId) => $router.push({ name: 'Draft Product Details', params: { productId } }))"
      >
        Create Product
      </Button>

      <Button
        :disabled="!kitComponentsValid || createProductPending"
        @click="activeStep++"
      >
        Continue
      </Button>

    </div>

  </div>

</template>
