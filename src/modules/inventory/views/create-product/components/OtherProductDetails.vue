<script setup lang="ts">

import { appMode } from '@/store'
import { Guard, guard } from '@/plugins/guard'
import { createNewProduct, createProductPending, productDetails } from '@/modules/inventory/views/create-product/store'

import Input from '@lib/components/inputs/Input.vue'
import Button from '@lib/components/buttons/Button.vue'
import Textbox from '@lib/components/inputs/Textbox.vue'

</script>

<template>

  <div class="w-full md:max-w-[38rem] md:p-8 md:pb-0">

    <form
      :class="{
        'pointer-events-none': createProductPending,
      }"
      @submit.prevent
    >

      <div class="md:grid md:gap-4">

        <!-- 1. Product Details Section -->
        <div class="w-full bg-core-20 md:shadow-custom border-r border-core-30 md:border-r-0">
          <div class="w-full h-10 sticky top-0 z-1 px-4 flex items-center space-x-2 bg-core-20 border-b border-core-30">

            <div class="h-4 w-4 justify-center items-center text-center bg-main-70 rounded-[1px] text-xs text-core-10">
              1
            </div>

            <p class="text-sm font-medium">
              Product Details
            </p>

          </div>

          <div class="w-full p-0 md:px-7 md:py-7 grid md:grid-cols-2 md:gap-4">

            <Input
              v-model="productDetails.retailPrice.value"
              v-model:valid="productDetails.retailPrice.valid"
              label="Retail Price"
              type="currency"
              :required="false"
            />

            <Input
              v-if="guard(appMode)"
              v-model="productDetails.isbn.value"
              v-model:valid="productDetails.isbn.valid"
              label="ISBN"
              :required="false"
            />

            <Input
              v-if="guard(appMode)"
              v-model="productDetails.upc.value"
              v-model:valid="productDetails.upc.valid"
              label="UPC"
              :required="false"
            />

            <Input
              v-model="productDetails.groupCode.value"
              v-model:valid="productDetails.groupCode.valid"
              label="Group Code"
              :required="false"
            />

            <Input
              v-model="productDetails.keywords.value"
              v-model:valid="productDetails.keywords.valid"
              class="md:col-span-2"
              label="Keywords"
              placeholder="durable, widget, outdoor"
              :required="false"
            />

            <Textbox
              v-model="productDetails.packingInstructions.value"
              v-model:valid="productDetails.packingInstructions.valid"
              class="md:col-span-2"
              label="Packing Instructions"
              :required="false"
            />

            <Textbox
              v-model="productDetails.notes.value"
              v-model:valid="productDetails.notes.valid"
              class="md:col-span-2"
              label="Notes"
              :required="false"
            />

          </div>
        </div>

        <!-- 2. Supplier Section -->
        <div class="w-full bg-core-20 md:shadow-custom border-r border-core-30 md:border-r-0">
          <div class="w-full h-10 sticky top-0 z-1 px-4 flex items-center space-x-2 bg-core-20 border-b border-core-30">
            <div class="h-4 w-4 justify-center items-center text-center bg-main-70 rounded-[1px] text-xs text-core-10">
              2
            </div>

            <p class="text-sm font-medium">
              Supplier
            </p>

          </div>

          <div class="w-full p-0 md:px-7 md:py-7 grid md:grid-cols-3 md:gap-4">

            <Input
              v-model="productDetails.supplier.value"
              v-model:valid="productDetails.supplier.valid"
              label="Supplier"
              :required="false"
            />

            <Input
              v-model="productDetails.supplierSku.value"
              v-model:valid="productDetails.supplierSku.valid"
              label="Supplier SKU"
              :required="false"
            />

            <Input
              v-model="productDetails.supplierCost.value"
              v-model:valid="productDetails.supplierCost.valid"
              label="Supplier Cost"
              type="currency"
              :required="false"
            />

          </div>
        </div>

        <!-- 3. Attributes Section -->
        <div class="w-full bg-core-20 md:shadow-custom border-r border-core-30 md:border-r-0">
          <div class="w-full h-10 sticky top-0 z-1 px-4 flex items-center space-x-2 bg-core-20 border-b border-core-30">
            <div class="h-4 w-4 justify-center items-center text-center bg-main-70 rounded-[1px] text-xs text-core-10">
              3
            </div>

            <p class="text-sm font-medium">
              Attributes
            </p>

          </div>

          <div class="w-full p-0 md:px-7 md:py-7 grid md:grid-cols-2 md:gap-4">

            <Input
              v-model="productDetails.color.value"
              v-model:valid="productDetails.color.valid"
              label="Color"
              :required="false"
            />

            <Input
              v-model="productDetails.size.value"
              v-model:valid="productDetails.size.valid"
              label="Size"
              :required="false"
            />

          </div>
        </div>

        <!-- 4. Customs Section -->
        <div class="w-full bg-core-20 md:shadow-custom border-r border-core-30 md:border-r-0">
          <div class="w-full h-10 sticky top-0 z-1 px-4 flex items-center space-x-2 bg-core-20 border-b border-core-30">
            <div class="h-4 w-4 justify-center items-center text-center bg-main-70 rounded-[1px] text-xs text-core-10">
              4
            </div>

            <p class="text-sm font-medium">
              Customs Information
            </p>

          </div>

          <div class="w-full p-0 md:px-7 md:py-7 grid md:grid-cols-2 md:gap-4">

            <Input
              v-model="productDetails.customsValue.value"
              v-model:valid="productDetails.customsValue.valid"
              class="md:col-span-2"
              label="Customs Value"
              type="currency"
              :required="false"
            />

            <Textbox
              v-model="productDetails.customsDescription.value"
              v-model:valid="productDetails.customsDescription.valid"
              class="md:col-span-2"
              label="Customs Description"
              :required="false"
            />

          </div>
        </div>

        <!-- 5. Stock Section -->

        <Guard :scope="appMode">

          <div class="w-full bg-core-20 md:shadow-xl border-r border-core-30 md:border-r-0">
            <div class="w-full h-10 sticky top-0 z-1 px-4 flex items-center space-x-2 bg-core-20 border-b border-core-30">
              <div class="h-4 w-4 justify-center items-center text-center bg-main-70 rounded-[1px] text-xs text-core-10">
                5
              </div>

              <p class="text-sm font-medium">
                Stock
              </p>

            </div>

            <div class="w-full p-0 md:px-7 md:py-7 grid md:grid-cols-2 md:gap-4">

              <Input
                v-model="productDetails.masterCaseQuantity.value"
                v-model:valid="productDetails.masterCaseQuantity.valid"
                label="Master Case QTY"
                :min="0"
                type="strict-number"
                :required="false"
              />

              <Input
                v-model="productDetails.caseQuantity.value"
                v-model:valid="productDetails.caseQuantity.valid"
                label="Case QTY"
                :min="0"
                type="strict-number"
                :required="false"
              />

            </div>
          </div>

        </Guard>

        <!-- 6. External Links -->
        <div class="w-full bg-core-20 md:shadow-custom border-r border-core-30 md:border-r-0">
          <div class="w-full h-10 sticky top-0 z-1 px-4 flex items-center space-x-2 bg-core-20 border-b border-core-30">
            <div class="h-4 w-4 justify-center items-center text-center bg-main-70 rounded-[1px] text-xs text-core-10">
              {{ guard(appMode) ? 6 : 5 }}
            </div>

            <p class="text-sm font-medium">
              External Links
            </p>

          </div>

          <div class="w-full p-0 md:px-7 md:py-7 grid md:grid-cols-2 md:gap-4">

            <Input
              v-model="productDetails.webUrl.value"
              v-model:valid="productDetails.webUrl.valid"
              class="md:col-span-2"
              label="Web URL"
              :required="false"
            />

            <Input
              v-model="productDetails.imageUrl.value"
              v-model:valid="productDetails.imageUrl.valid"
              class="md:col-span-2"
              label="Image URL"
              :required="false"
            />

            <Input
              v-model="productDetails.imageThumbUrl.value"
              v-model:valid="productDetails.imageThumbUrl.valid"
              class="md:col-span-2"
              label="Image Thumbnail URL"
              :required="false"
            />

          </div>
        </div>

      </div>
    </form>

    <div class="w-full sticky bottom-0 z-2 grid md:hidden bg-core-20">

      <Button
        :pending="createProductPending"
        @click="() => createNewProduct((productId) => $router.push({ name: 'Draft Product Details', params: { productId } }))"
      >
        Create Product
      </Button>

    </div>
  </div>

</template>
