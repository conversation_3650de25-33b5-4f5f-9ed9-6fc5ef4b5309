<script setup lang="ts">

import { selectedKitProducts } from '@/modules/inventory/views/create-product/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

import type { InventoryPanelProduct } from '@/modules/inventory/types'

defineProps<{
  kitComponent: InventoryPanelProduct
}>()

const product = defineModel<InventoryPanelProduct>( 'kitComponent' )

function removeProduct() {

  if ( !product.value )
    return

  selectedKitProducts.value = selectedKitProducts.value.filter( i => i.id !== product.value.id )

}

</script>

<template>

  <div class="hidden md:flex flex-row items-center justify-between px-4 py-3 bg-core-10 border-b border-core-30">
    <div class="flex shrink-0 w-9 h-9 justify-center items-center border border-core-30 rounded-xs">
      <Icon name="shaded-product" size="m" />
    </div>

    <div class="flex-auto pl-2 text-sm leading-4">
      <div class="line-clamp-1 text-core-120">
        {{ product.sku }}
      </div>
      <div class="line-clamp-1 text-core-70">
        {{ product.title }}
      </div>
    </div>

    <div class="flex items-center justify-end gap-2">

      <div class="w-max h-[1.625rem] px-3 grid place-content-center rounded-xs bg-core-20">

        <p class="text-sm font-medium text-main">
          {{ product.quantity }} <span class="text-core font-normal">{{ product.quantity === 1 ? 'Unit' : 'Units' }}</span>
        </p>

      </div>

      <Button
        type="box"
        size="xs"
        mode="skinny"
        class="hidden md:grid place-content-center justify-self-end text-error hover:text-error"
        :icon="{ name: 'delete', size: 'm' }"
        @click="removeProduct"
      />

    </div>
  </div>
</template>
