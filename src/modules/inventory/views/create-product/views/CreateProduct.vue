<script setup lang="ts">

import { confirm } from '@lib/store/confirm'
import { computed } from 'vue'
import { CreateProductFlow, CreateProductMicroFlow } from '@/modules/inventory/views/create-product/types'

import {
  activeFlow,
  activeStep,
  createNewProduct,
  createProductPending,
  enableAddKitItems,
  isKitProduct,
  kitComponentsValid,
  productInfoStepValid,
  resetCreateProductState,
  storedStep
} from '@/modules/inventory/views/create-product/store'

import Button from '@lib/components/buttons/Button.vue'
import FlowBar from '@lib/components/utils/FlowBar.vue'
import ProductInfo from '@/modules/inventory/views/create-product/components/ProductInfo.vue'
import AddKitItemsPanel from '@/modules/inventory/views/create-product/components/AddKitItemsPanel.vue'
import OtherProductDetails from '@/modules/inventory/views/create-product/components/OtherProductDetails.vue'

import type { FlowStep } from '@lib/types/flowBarTypes'

const steps = computed<FlowStep<CreateProductMicroFlow>[]>(() => [
  {
    id:            1,
    isGroup:       true,
    stepName:      'Product Info',
    groupName:     'Product Info',
    inProgress:    activeStep.value === CreateProductMicroFlow.productInfo,
    stepComplete:  productInfoStepValid.value,
    groupComplete: productInfoStepValid.value && kitComponentsValid.value,
  },
  {
    id:            2,
    hidden:        !isKitProduct.value,
    isGroup:       false,
    stepName:      'Kit Components',
    disabled:      !isKitProduct.value || !productInfoStepValid.value,
    groupName:     'Product Info',
    inProgress:    activeStep.value === CreateProductMicroFlow.kitComponents,
    stepComplete:  kitComponentsValid.value,
    groupComplete: productInfoStepValid.value && kitComponentsValid.value
  },
  {
    id:            3,
    isGroup:       true,
    disabled:      !productInfoStepValid.value || !kitComponentsValid.value,
    stepName:      'Other Details',
    groupName:     'Other Details',
    inProgress:    activeStep.value === CreateProductMicroFlow.otherDetails,
    stepComplete:  false,
    groupComplete: false
  }
] )

</script>

<template>

  <div class="w-full h-full grid grid-rows-[max-content_1fr] overflow-hidden">

    <div class="w-full h-12 grid grid-cols-[max-content_1fr_max-content] place-items-center bg-core-120">

      <div class="w-[3rem] md:w-[9rem] h-full pl-4 md:px-4 flex items-center">

        <p class="text-xs md:text-base text-core-10 font-medium hidden md:inline-block">
          Create Product
        </p>

      </div>

      <FlowBar :steps="steps" @set-active-step="(step) => activeStep = step.id" />

      <div class="w-[3rem] md:w-[8rem] h-full flex justify-end">

        <Button
          type="box"
          mode="naked"
          size="auto"
          class="w-12 h-full text-core-20 hover:text-core-20"
          :icon="{ name: 'close', size: 'm' }"
          :disabled="createProductPending"
          @click="() => confirm({
            header: 'Are you sure you want to exit product creation?',
            description: 'Exiting now will result in the loss of all entered data.',
            action: () => {
              $router.push('/inventory/draft')
              resetCreateProductState()
            },
          })"
        />

      </div>

    </div>

    <div class="w-full h-full overflow-hidden">

      <!-- Flow :: Desktop -->

      <div class="w-full h-full hidden md:block overflow-hidden bg-core-30">

        <Transition name="route" mode="out-in">

          <div
            v-if="activeFlow === CreateProductFlow.productInfo"
            class="w-full h-full grid grid-rows-[1fr_max-content] overflow-hidden"
          >
            <!-- Content -->

            <div class="w-full h-full grid overflow-hidden grid-cols-[max-content_1fr]">

              <!-- Add Kit Items -->

              <div
                class="h-full overflow-hidden bg-core-20 shadow-custom transition-[width]"
                :class="{
                  'w-0': !(enableAddKitItems),
                  'w-96': enableAddKitItems,
                  'pointer-events-none': createProductPending,
                }"
              >

                <Transition name="sidebar-modal-left" mode="out-in">

                  <AddKitItemsPanel v-if="enableAddKitItems" />

                </Transition>

              </div>

              <!-- General Information -->

              <div
                class="w-full grid grid-flow-row gap-8 justify-items-center overflow-hidden overflow-y-auto"
                :class="{ 'border-l border-core-30': enableAddKitItems }"
              >

                <ProductInfo />

              </div>

            </div>

            <!-- Action Buttons -->

            <div class="w-full h-12 hidden md:flex items-center justify-end bg-core-20">

              <div class="h-full w-full border-t border-core-30" />

              <div class="h-full min-w-[27rem] grid grid-cols-2">

                <Button
                  mode="secondary"
                  size="auto"
                  class="h-full w-full"
                  :pending="createProductPending"
                  :disabled="!(productInfoStepValid && kitComponentsValid)"
                  @click="() => createNewProduct((productId) => $router.push({ name: 'Draft Product Details', params: { productId } }))"
                >
                  Create Product
                </Button>

                <Button
                  size="auto"
                  class="h-full w-full"
                  :disabled="!(productInfoStepValid && kitComponentsValid) || createProductPending"
                  @click="activeStep = CreateProductMicroFlow.otherDetails"
                >
                  Continue
                </Button>

              </div>

            </div>

          </div>

          <div
            v-else-if="activeFlow === CreateProductFlow.otherDetails"
            class="w-full h-full grid grid-rows-[1fr_max-content] overflow-hidden"
          >

            <div class="w-full grid grid-flow-row gap-8 justify-items-center overflow-hidden overflow-y-auto">

              <OtherProductDetails />

            </div>

            <div class="w-full h-12 hidden md:flex items-center justify-end bg-core-20 border-t border-core-30">

              <Button
                size="auto"
                class="h-full min-w-[13.5rem]"
                :pending="createProductPending"
                :disabled="!(productInfoStepValid && kitComponentsValid)"
                @click="() => createNewProduct((productId) => $router.push({ name: 'Draft Product Details', params: { productId } }))"
              >
                Create Product
              </Button>

            </div>

          </div>

        </Transition>

      </div>

      <!-- Micro Flow :: Small Screens -->

      <div class="w-full h-full relative md:hidden overflow-hidden overflow-y-auto">

        <Transition
          :name="activeStep > storedStep ? 'mobile-view-right' : 'mobile-view-left'"
          @enter="() => storedStep = activeStep"
        >

          <ProductInfo v-if="activeStep === CreateProductMicroFlow.productInfo" />
          <AddKitItemsPanel v-else-if="activeStep === CreateProductMicroFlow.kitComponents && enableAddKitItems" />
          <OtherProductDetails v-else-if="activeStep === CreateProductMicroFlow.otherDetails" />

        </Transition>

      </div>

    </div>

  </div>

</template>
