<script setup lang="ts">

import { SectionKeys } from '@/modules/inventory/types'
import { Guard, guard } from '@/plugins/guard'
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { handleErrors, viewSetup } from '@lib/scripts/utils'
import { appMode, importStatusList } from '@/store'
import { setAlertOptions, setNotificationOptions } from '@lib/store/snackbar'

import {
  createKitComponent,
  createSections,
  deleteKitComponent,
  getDraftProduct,
  mapDetailsToSections,
  sanitizeKey,
  updateDraftProduct,
  updateKitComponent
} from '@/modules/inventory/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'
import Loader from '@lib/components/blocks/Loader.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import Snackbar from '@lib/components/utils/Snackbar.vue'
import SectionBox from '@/modules/inventory/components/SectionBox.vue'
import PublishBar from '@/components/PublishBar.vue'
import RecordError from '@/components/RecordError.vue'
import KitProductItem from '@/modules/inventory/components/KitProductItem.vue'
import BasicSectionBox from '@/modules/inventory/components/BasicSectionBox.vue'
import EditSectionForm from '@/modules/inventory/components/EditSectionForm.vue'
import ImportErrorsBar from '@/components/ImportErrorsBar.vue'
import ErrorsSectionBox from '@/modules/inventory/components/ErrorsSectionBox.vue'

import type {
  AllProductProperties,
  DraftKitComponent,
  DraftProduct,
  InventoryPanelProduct,
  KitPostData,
  ProductDetailsSection,
  SectionModel
} from '@/modules/inventory/types'

const route               = useRoute()
const router              = useRouter()
const product             = ref<DraftProduct>( null )
const editable            = computed<boolean>(() => ( !hasImportErrors.value || recordError.value ) && [ 'Draft', 'Failed' ].includes( product.value?.importStatus ))
const sections            = ref<ProductDetailsSection[]>()
const productId           = computed(() => Number.parseInt( String( route.params.productId )))
const recordError         = computed(() => product.value?.importErrors?.find( e => e.propertyName === 'record' )?.errorMsg )
const isKitProduct        = computed(() => product.value?.type === 'KIT' )
const deletePending       = ref<number>( null )
const updatePending       = ref<boolean>( false )
const hasKitProducts      = computed(() => product.value?.kitComponents?.length > 0 )
const isProductEmpty      = computed(() => isKitProduct.value ? ( !product.value.masterRecordId && !hasKitProducts.value ) : false )
const editSectionForm     = ref<SectionKeys>( null )
const hasImportErrors     = computed(() => product.value?.importErrorCount > 0 )
const selectedProducts    = ref<InventoryPanelProduct[]>( [] )
const swapKitProductId    = ref<number>( null )
const addedProductsIds    = ref<number[]>( [] )
const openSelectedPanel   = ref( false )
const canPublishProduct   = computed<boolean>(() => editable.value && !recordError.value && !hasImportErrors.value && !hasKitImportErrors.value )
const canUpdateKitItems   = computed(() => selectedProducts.value?.every( k => k.quantity > 0 ))
const hasKitImportErrors  = computed(() => product.value?.kitComponents?.some( kit => kit.importErrorCount > 0 ))

// ---- SIDEBARS UTILITIES

function openEditForm( key: SectionKeys ) {
  editSectionForm.value = key
  sections.value = mapDetailsToSections( sections.value, product.value )
}

function closeEditForm() {

  editSectionForm.value = null

  sections.value.forEach(( section ) => {
    section.model.forEach(( item ) => {
      item.declaredValue = null
    })
  })

}

function openInventory( sku: string, swapId: number, toggleSelected: boolean = true ) {

  swapKitProductId.value = swapId

  const validKitProducts = product.value?.kitComponents ? product.value?.kitComponents?.filter( kit => !kit?.importErrors?.some( err => err.propertyName === 'record' )) : []

  selectedProducts.value = []
  mapKitProductsToSelected( validKitProducts )

  if ( sku ) {
    router.push({ name: 'Draft Product Details Inventory', params: { productId: product.value.id }, query: { sku } })
    return
  }

  openSelectedPanel.value = toggleSelected && validKitProducts.length > 0
  router.push({ name: 'Draft Product Details Inventory', params: { productId: product.value.id } })

}

function closeInventory() {

  router.push({ name: 'Draft Product Details', params: { productId: product.value.id } })
  openSelectedPanel.value = false
  swapKitProductId.value = null

}

// --- PRODUCT SERVICES

async function getProduct( pId: number ) {

  const { error, status, payload } = await getDraftProduct( pId )

  if ( !error ) {
    product.value = payload
    sections.value = createSections( 'draft', payload?.defaultFacilityCode, payload.masterRecordId )
    sections.value = mapDetailsToSections( sections.value, payload )
  }

  return {
    error,
    status,
    payload
  }

}

async function updateProduct() {

  updatePending.value = true

  const editedSection = sections.value.find( s => s.key === editSectionForm.value )

  // If the product import status is 'Failed',
  // we need to manually set it back to 'Draft'
  // Otherwise, keep the current status

  const data: DraftProduct = {
    ...product.value,
    importStatus: product.value.importStatus === 'Failed' ? 'Draft' : product.value.importStatus
  }

  editedSection.model.forEach(( item ) => {

    if ( item.key in product.value )
      data[item.key] = sanitizeKey( item.declaredValue, item.type )

  })

  if ( data?.kitComponents?.length > 0 )
    data.type = 'KIT'

  delete ( data.kitComponents )

  const { error, payload } = await updateDraftProduct( data, product.value.id )

  if ( !error ) {

    product.value = payload
    sections.value = createSections( 'draft', payload.defaultFacilityCode, payload.masterRecordId )
    sections.value = mapDetailsToSections( sections.value, payload )
    setNotificationOptions({ message: 'Product details are updated successfully.' })
    closeEditForm()

  }

  updatePending.value = false

}

// ---- KIT PRODUCTS SERVICES

async function updateKitProducts() {

  updatePending.value = true

  const requests = []

  if ( swapKitProductId.value )
    requests.push( deleteKitComponent( product.value.id, swapKitProductId.value ))

  selectedProducts.value.forEach(( kit ) => {

    const data: KitPostData = {
      sku:      kit.sku,
      quantity: kit.quantity
    }

    if ( addedProductsIds.value.includes( kit.id ))
      requests.push( createKitComponent( product.value.id, data ))

    else
      requests.push( updateKitComponent( product.value.id, kit.id, data ))

  })

  const responses = await Promise.all( requests )

  const errors = handleErrors( responses.map( r => r.error ))

  if ( !errors ) {

    setNotificationOptions({ message: 'All selected kit components have been successfully added or updated in the product.' })

    if ( product.value.importStatus === 'Failed' ) {

      const { payload, error } = await updateDraftProduct({ ...product.value, importStatus: 'Draft' }, product.value.id )

      if ( !error ) {

        product.value = payload
        sections.value = mapDetailsToSections( sections.value, payload as AllProductProperties )

        closeInventory()

      }

    }

    else {

      const { error } = await updateView( 'silent' )

      if ( !error )
        closeInventory()

    }

  }

  updatePending.value = false

}

async function deleteKitProduct( productId: number, kitProductId: number ) {

  if ( !product.value.masterRecordId && product.value?.kitComponents?.length === 1 ) {

    setAlertOptions({
      message:  'Ensure your kit isn\'t empty by adding a new product before removing this one.',
      severity: 'warning'
    })

    return

  }

  deletePending.value = kitProductId

  const { error } = await deleteKitComponent( productId, kitProductId )

  if ( !error ) {

    setNotificationOptions({ message: 'Product kit component is deleted successfully.' })

    if ( product.value.importStatus === 'Failed' ) {

      const { payload, error } = await updateDraftProduct({ ...product.value, importStatus: 'Draft' }, productId )

      if ( !error )
        product.value = payload

    }

    else { await updateView( 'silent' ) }

  }

  deletePending.value = null

}

function mapKitProductsToSelected( kitProducts: DraftKitComponent[] ) {

  if ( !kitProducts || kitProducts.length === 0 )
    return

  kitProducts.forEach(( kit ) => {

    selectedProducts.value.push({
      id:                  kit.id,
      sku:                 kit.sku,
      title:               null,
      price:               null,
      supplier:            null,
      quantity:            kit.quantity ?? 0,
      kitComponents:       null,
      availableQuantity:   null,
      availableByFacility: null
    })
  })

}

function trackAddedProduct( product: InventoryPanelProduct ) {
  addedProductsIds.value.push( product.id )
}

function resolveErrors() {

  const errorsModel: SectionModel[]  = []
  const errorsSection = sections.value.find( s => s.key === SectionKeys.errors )

  errorsSection.model = []

  sections.value.forEach(( section ) => {

    section.model.forEach(( item ) => {

      if ( product.value.importErrors.map( e => e.propertyName ).includes( item.key as keyof DraftProduct ))
        errorsModel.push( item )

    })

  })

  errorsSection.model = errorsModel
  editSectionForm.value = SectionKeys.errors

}

// --- GET PRODUCT DATA

const { pending, updateView } = viewSetup(
  null,
  null,
  router,
  () => getProduct( productId.value ),
  productId,
  true
)

</script>

<template>

  <div class="w-full h-full grid grid-rows-[max-content_1fr] bg-core-30 overflow-hidden">

    <!-- Header -->

    <div class="w-full h-[5.5rem] md:h-12 grid grid-rows-[max-content_max-content] md:grid-rows-1 grid-cols-[1fr_max-content] md:grid-cols-[max-content_1fr_max-content_max-content] bg-core-120">

      <!-- Draft Id :: Product Master Id -->

      <div class="h-12 flex items-center border-b border-core-100">

        <p v-if="pending" class="text-core-10 px-4">
          Loading Product Details ...
        </p>

        <div v-else class="h-full flex items-center">

          <!-- Draft Id -->

          <div class="h-full px-4 flex items-center space-x-2 md:border-r border-core-100">

            <div class="flex items-center space-x-2">

              <div class="text-warning px-2 py-px bg-warning/30 font-medium flex items-center space-x-2">

                <Icon name="draft-product" size="s" />
                <p>Draft Product:</p>

              </div>

            </div>

            <p class="text-main-50 font-medium">
              [{{ product?.id }}]
            </p>

          </div>

          <!-- Master Id -->

          <div v-if="product?.masterRecordId" class="h-full px-4 hidden lg:flex items-center border-r border-core-100">

            <p class="text-core-10 font-medium">
              Product ID: <router-link :to="{ name: 'Live Product Details', params: { productId: product?.masterRecordId } }" class="text-main-50 underline">
                [{{ product?.masterRecordId }}]
              </router-link>
            </p>

          </div>

        </div>

      </div>

      <!-- Import Status -->

      <div v-if="product?.importStatus" class="h-10 md:h-12 md:w-max px-4 flex items-center row-start-2 md:row-start-auto md:border-r border-core-100">

        <p class="text-core-10 font-medium">
          Import Status:
          <span
            :class="{
              'text-error': product.importStatus === 'Failed',
              'text-success': product.importStatus === 'Processed',
              'text-core-60': product.importStatus === 'Draft',
              'text-main-50': product.importStatus === 'Processing',
              'text-data1-120': product.importStatus === 'Pending',
            }"
          >
            [{{ importStatusList.find(s => s.id === product.importStatus)?.name }}]
          </span>
        </p>

      </div>

      <!-- Other Header Options -->

      <div v-if="pending" class="hidden md:block" />

      <!-- Close Button -->

      <div class="w-12 h-12 border-b md:border-l border-core-100">

        <Button
          mode="naked"
          type="box"
          size="auto"
          class="w-12 h-full text-core-10"
          :icon="{
            name: 'close',
            size: 'm',
          }"
          @click="$router.push({ name: 'Draft Products' })"
        />

      </div>

    </div>

    <!-- BARS [ mobile ] :: Publish | Record Error | Resolve Errors -->

    <div v-if="!pending" class="w-full md:hidden">

      <ImportErrorsBar
        v-if="!recordError && hasImportErrors"
        type="product"
        :count="product?.importErrors?.length"
        :can-resolve="!recordError && hasImportErrors && guard('Inventory.Write')"
        @resolve="resolveErrors"
      />

      <RecordError v-if="!!recordError">

        <p class="text-sm">

          <span class="font-medium">{{ recordError }}</span>
          <span> - </span>
          <span>This product has a record error, which unfortunately can't be resolved through the guided resolution form. Please fix this error manually. </span>

        </p>

      </RecordError>

      <Transition name="publish-bar">

        <PublishBar
          v-if="canPublishProduct"
          :id="productId"
          type="product"
          :is-empty="isProductEmpty"
          :can-publish="guard('Inventory.Write')"
          @update="() => product.importStatus = 'Pending'"
        />

      </Transition>

    </div>

    <Transition name="view" mode="out-in">

      <div v-if="pending" class="w-full h-full flex items-center justify-center space-x-2">
        <Loader name="Product Data" />
      </div>

      <!-- Content -->

      <div v-else-if="!!product" class="w-full h-full grid md:grid-cols-[max-content_1fr] content-start md:grid-rows-[max-content_1fr] overflow-y-auto md:overflow-hidden">

        <!-- SECTION [ mobile ] :: Kit Errors -->

        <ErrorsSectionBox
          v-if="!recordError && hasKitImportErrors"
          type="kit"
          class="md:hidden"
          :errors="product.kitComponents.reduce((acc, kit) => acc.concat(kit.importErrors ?? []), [])"
        />

        <!-- General Info Sidebar -->

        <div class="md:w-[20rem] h-full md:row-span-2 border-t md:border-t-0 border-core-30 md:overflow-y-auto md:shadow-custom">

          <BasicSectionBox
            :section="sections.find(s => s.name === 'General Information')"
            :can-edit="editable && sections.find(s => s.name === 'General Information').editable"
            class="w-full md:min-h-full md:w-[20rem] md:max-w-[20rem] md:min-w-[20rem]"
            @open-section-form="openEditForm"
          />

        </div>

        <!-- BARS [ desktop ] :: Publish | Record Error | Resolve Errors -->

        <div class="w-full hidden md:block">

          <ImportErrorsBar
            v-if="!recordError && hasImportErrors"
            type="product"
            :count="product?.importErrors?.length"
            :can-resolve="!recordError && hasImportErrors && guard('Inventory.Write')"
            @resolve="resolveErrors"
          />

          <RecordError v-if="!!recordError">

            <p class="text-sm">

              <span class="font-medium">{{ recordError }}</span>
              <span> - </span>
              <span>This product has a record error, which unfortunately can't be resolved through the guided resolution form. Please fix this error manually. </span>

            </p>

          </RecordError>

          <Transition name="publish-bar">

            <PublishBar
              v-if="canPublishProduct"
              :id="productId"
              type="product"
              :is-empty="isProductEmpty"
              :can-publish="guard('Inventory.Write')"
              @update="() => product.importStatus = 'Pending'"
            />

          </Transition>

        </div>

        <!-- Product Details -->

        <div class="md:overflow-y-auto">

          <div class="w-full md:p-8 grid xl:grid-cols-2 md:gap-8">

            <!-- SECTION [ desktop ] :: Kit Errors -->

            <ErrorsSectionBox
              v-if="!recordError && hasKitImportErrors"
              type="kit"
              :errors="product.kitComponents.reduce((acc, kit) => acc.concat(kit.importErrors ?? []), [])"
              class="hidden md:block xl:col-span-2"
            />

            <div class="w-full grid md:gap-8 content-start">

              <!-- SECTION :: Kit Components -->

              <SectionBox
                v-if="isKitProduct"
                name="Kit Components"
                class="w-full"
                icon-name="products-group"
              >

                <template #head-slot>

                  <Guard scope="Inventory.Write">

                    <div class="h-full border-l border-core-30">

                      <Button
                        v-if="appMode === 'ADMIN'"
                        class="h-full px-4 flex items-center space-x-2"
                        size="auto"
                        mode="naked"
                        :disabled="!editable"
                        @click="() => openInventory(null, null, true)"
                      >

                        <p class="text-sm">
                          Edit
                        </p>
                        <Icon name="edit" size="s" class="text-main" />

                      </Button>

                    </div>

                  </Guard>

                </template>

                <div v-if="hasImportErrors" class="w-full p-2 border-b border-core-30">

                  <Snackbar
                    type="alert"
                    :grow="true"
                    message="Resolve product errors before updating kit components."
                    severity="warning"
                    :show-close-button="false"
                  />

                </div>

                <div v-if="!hasKitProducts && !hasImportErrors" class="w-full p-2 border-b border-core-30">

                  <Snackbar
                    type="alert"
                    :grow="true"
                    message="There are no components in this kit."
                    severity="warning"
                    :show-close-button="false"
                  />

                </div>

                <KitProductItem
                  v-for="kit in product.kitComponents"
                  :key="kit.id"
                  :pending="deletePending"
                  :disabled="!editable"
                  :kit-product="kit"
                  inventory-type="draft"
                  @edit="(toggleSelected) => openInventory(null, null, toggleSelected)"
                  @swap="(swapId) => openInventory(null, swapId, false)"
                  @delete="(kitId) => deleteKitProduct(product.id, kitId)"
                  @resolve="(sku) => openInventory(sku, null, false)"
                />

              </SectionBox>

              <!-- SECTION :: Attributes | Other Details | External Links -->

              <BasicSectionBox
                v-for="section in sections.filter(s => !s.hidden && s.viewSection === 'left')"
                :key="section.name"
                :section="section"
                :can-edit="editable && section.editable"
                @open-section-form="openEditForm"
              />

            </div>

            <!-- SECTION :: Stock | Supplier | Customs -->

            <div class="grid md:gap-8 content-start">

              <BasicSectionBox
                v-for="section in sections.filter(s => !s.hidden && s.viewSection === 'right')"
                :key="section.name"
                :section="section"
                :can-edit="editable && section.editable"
                @open-section-form="openEditForm"
              />

            </div>

          </div>

        </div>

      </div>

    </Transition>

    <!-- Sidebar :: Edit Section Form -->

    <Sidebar
      :dim="true"
      :open="!!editSectionForm"
      :strict="true"
      @close="closeEditForm"
    >

      <EditSectionForm
        :section="sections.find(s => s.key === editSectionForm)"
        :save-pending="updatePending"
        @save="updateProduct"
        @close="closeEditForm"
      />

    </Sidebar>

    <!-- Sidebar :: Inventory -->

    <Sidebar
      :open="$route.name === 'Draft Product Details Inventory'"
      :strict="true"
      position="right"
      custom-tw-offset="top-0 md:top-[3rem] h-full md:h-[calc(100%-3rem)]"
      @close="closeInventory"
    >

      <div class="w-full md:w-[26rem] h-full grid grid-rows-[max-content_1fr_max-content] md:grid-rows-[1fr_max-content] ignore-outside">

        <div class="w-full h-12 pl-4 flex md:hidden items-center space-x-3">

          <Icon name="product" size="m" class="text-main" />

          <p class="text-sm font-medium grow">
            Your Products
          </p>

        </div>

        <RouterView v-slot="{ Component }">

          <Component
            :is="Component"
            v-model:selected-products="selectedProducts"
            :disabled="updatePending"
            :open-selected="openSelectedPanel"
            :allow-locking="false"
            :allow-kit-items="false"
            match-selected-products-by="sku"
            @add-product="trackAddedProduct"
          />

        </RouterView>

        <div class="w-full h-12 grid grid-cols-2">

          <Button
            size="auto"
            mode="secondary"
            :disabled="updatePending"
            @click="closeInventory"
          >
            Cancel
          </Button>

          <Button
            size="auto"
            :pending="updatePending"
            :disabled="!canUpdateKitItems"
            @click="updateKitProducts"
          >
            Update Kit Components
          </Button>

        </div>

      </div>

    </Sidebar>

  </div>

</template>
