<script setup lang="ts">

import { Guard } from '@/plugins/guard'
import { useRoute } from 'vue-router'
import { ref, watch } from 'vue'

import Tab from '@lib/components/buttons/Tab.vue'
import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'

const route   = useRoute()
const options = ref<boolean>( false )

watch( route, () => options.value = false )

</script>

<template>

  <div class="w-full h-full grid grid-rows-[max-content_1fr_max-content] md:grid-rows-[max-content_1fr]">

    <div class="w-full h-10 hidden md:flex items-center bg-core-20">

      <Tab to="/inventory/live" size="auto" class="text-sm font-medium w-full md:w-auto h-full px-4">
        Live Products
      </Tab>

      <Tab to="/inventory/draft" size="auto" class="text-sm font-medium w-full md:w-auto h-full px-4">
        Draft Products
      </Tab>

      <div class="h-full grow border-b border-core-30" />

      <Guard scope="Inventory.Write">

        <div class="h-full border-l border-b border-core-30">

          <Button
            size="auto"
            mode="naked"
            class="text-main hover:text-main h-full px-4 flex items-center space-x-2"
            @click="$router.push({ name: 'Upload Bulk Products' })"
          >

            <p class="text-sm font-medium">
              Bulk Upload
            </p>

            <Icon name="batch-upload" size="s" />

          </Button>

        </div>

      </Guard>

      <Guard scope="Inventory.Write">

        <div class="h-full border-l border-b border-core-30">

          <Button
            size="auto"
            mode="naked"
            class="text-main hover:text-main h-full px-4 flex items-center space-x-2"
            @click="$router.push({ name: 'Create Product' })"
          >

            <p class="text-sm font-medium">
              New Product
            </p>

            <Icon name="add" size="s" />

          </Button>

        </div>

      </Guard>

    </div>

    <div class="w-full h-10 pl-4 flex md:hidden items-center bg-core-10 border-b border-core-30">

      <p class="font-medium grow">
        {{ route.name }}
      </p>

      <Guard scope="Inventory.Write">

        <Button
          size="m"
          mode="naked"
          type="box"
          :icon="{
            name: 'dots-vertical',
            size: 'm',
          }"
          @click="() => options = !options"
        />

      </Guard>

    </div>

    <div class="w-full overflow-hidden md:page-background-gradient">

      <RouterView v-slot="{ Component }">

        <Transition mode="out-in" name="route">

          <Component :is="Component" />

        </Transition>

      </RouterView>

    </div>

    <div class="w-full h-12 md:hidden grid grid-cols-2 border-t border-core-30 bg-core-10">

      <RouterLink
        to="/inventory/live"
        class="h-full w-full relative grid place-content-center clicked-active-state"
        active-class="text-main bg-core-20"
      >
        <Icon name="product" size="m" />
      </RouterLink>

      <RouterLink
        to="/inventory/draft"
        class="h-full w-full relative grid place-content-center clicked-active-state"
        active-class="text-main bg-core-20"
      >
        <Icon name="draft-product" size="m" />
      </RouterLink>

    </div>

    <!-- SIDEBAR :: Mobile Options -->

    <Sidebar
      :dim="true"
      :open="options"
      position="bottom"
      @close="() => options = false"
    >

      <div class="bg-core-10">

        <div class="w-full h-12 pl-4 flex items-center bg-core-20 border-b border-core-30">

          <p class="text-sm font-medium grow">
            Available Options
          </p>

          <Button
            mode="naked"
            type="box"
            size="xl"
            :icon="{ name: 'close', size: 'm' }"
            @click="() => options = false"
          />

        </div>

        <div class="p-4 grid gap-y-3">

          <Button
            type="pill"
            class="px-4 flex items-center space-x-2 justify-between"
            @click="$router.push({ name: 'Create Product' })"
          >

            <p>New Product</p>

            <Icon name="add" size="s" />

          </Button>

          <Button
            type="pill"
            class="px-4 flex items-center space-x-2 justify-between"
            @click="$router.push({ name: 'Upload Bulk Products' })"
          >

            <p>Bulk Upload</p>

            <Icon name="batch-upload" size="s" />

          </Button>

        </div>

      </div>

    </Sidebar>

  </div>

</template>
