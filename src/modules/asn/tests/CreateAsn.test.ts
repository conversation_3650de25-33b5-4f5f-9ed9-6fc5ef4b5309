import { Flow } from '@/modules/asn/views/CreateAsn/types'
import { mount } from '@vue/test-utils'
import { defaultFacility } from '@/store'
import { describe, expect, it, vi } from 'vitest'
import { activeStep, asnGeneralInfo, asnItems } from '@/modules/asn/views/CreateAsn/store'

import Row from '@lib/components/table/Row.vue'
import Cell from '@lib/components/table/Cell.vue'
import Icon from '@lib/components/blocks/Icon.vue'
import Input from '@lib/components/inputs/Input.vue'
import Radio from '@lib/components/inputs/Radio.vue'
import Table from '@lib/components/table/Table.vue'
import Select from '@lib/components/inputs/Select.vue'
import Button from '@lib/components/buttons/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import Textbox from '@lib/components/inputs/Textbox.vue'
import DatePicker from '@lib/components/inputs/DatePicker.vue'
import AsnSummary from '@/modules/asn/views/CreateAsn/components/AsnSummary.vue'
import AsnProducts from '@/modules/asn/views/CreateAsn/components/AsnProducts.vue'
import GeneralDetails from '@/modules/asn/views/CreateAsn/components/GeneralDetails.vue'

vi.mock( '@lib/scripts/inputValidation', async ( importOriginal ) => {
  const actual = await importOriginal() as any

  return {
    ...actual
  }

})

vi.mock( '@/store', async ( importOriginal ) => {
  const actual = await importOriginal() as any

  actual.facilitiesList.value = [
    {
      id:   'DC1',
      name: 'Midwest - Mobridge, SD'
    },
    {
      id:   'DC6',
      name: 'West - Los Angeles, CA'
    },
    {
      id:   'DC7',
      name: 'East - Wilmington, OH'
    }
  ]

  return {
    ...actual,
  }

})

describe( 'create ASN :: GeneralDetails', () => {
  const wrapper = mount( GeneralDetails )

  it( 'exists', () => {
    expect( wrapper.exists()).toBe( true )
  })

  describe( 'structure', () => {

    it( 'renders client details section', () => {
      const clientSection = wrapper.find( '[data-section="client"]' )
      expect( clientSection.exists()).toBe( true )
      expect( wrapper.findAllComponents( Icon ).find( i => i.props( 'name' ) === 'details' )).toBeTruthy()
      expect( clientSection.find( 'p' ).text()).toBe( 'Client Details' )
    })

    it( 'renders shipping details section', () => {
      const shippingSection = wrapper.find( '[data-section="shipping"]' )
      expect( shippingSection.exists()).toBe( true )
      expect( wrapper.findAllComponents( Icon ).find( i => i.props( 'name' ) === 'shipped' )).toBeTruthy()
      expect( shippingSection.find( 'p' ).text()).toBe( 'Shipping Details' )
    })

  })

  describe( 'form fields', () => {

    it( 'renders client details form inputs', () => {
      const inputs = wrapper.findAllComponents( Input )
      const textBoxes = wrapper.findAllComponents( Textbox )

      expect( inputs.find( i => i.props( 'label' ) === 'Client Reference' ).exists()).toBe( true )
      expect( inputs.find( i => i.props( 'label' ) === 'Client Reference' ).props().required ).toBe( false )

      expect( inputs.find( i => i.props( 'label' ) === 'Client PO Number' ).exists()).toBe( true )
      expect( inputs.find( i => i.props( 'label' ) === 'Client PO Number' ).props().required ).toBe( false )

      expect( textBoxes.find( t => t.props( 'label' ) === 'Instruction Notes' ).exists()).toBe( true )
      expect( textBoxes.find( t => t.props( 'label' ) === 'Instruction Notes' ).props().required ).toBe( false )
    })

    it( 'renders shipping details form inputs', () => {
      const inputs = wrapper.findAllComponents( Input )
      const datePicker = wrapper.findAllComponents( DatePicker )
      const select = wrapper.findAllComponents( Select )

      expect( inputs.find( i => i.props( 'label' ) === 'Shipper Name' ).exists()).toBe( true )
      expect( inputs.find( i => i.props( 'label' ) === 'Shipper Name' ).props().required ).toBe( true )

      expect( datePicker.find( d => d.props( 'label' ) === 'Expected Date' ).exists()).toBe( true )
      expect( datePicker.find( d => d.props( 'label' ) === 'Expected Date' ).props().required ).toBe( true )

      expect( select.find( s => s.props( 'label' ) === 'Carrier' ).exists()).toBe( true )
      expect( select.find( s => s.props( 'label' ) === 'Carrier' ).props().required ).toBe( true )

      expect( inputs.find( i => i.props( 'label' ) === 'Carton/Package Count' ).exists()).toBe( true )
      expect( inputs.find( i => i.props( 'label' ) === 'Carton/Package Count' ).props().required ).toBe( false )

      expect( inputs.find( i => i.props( 'label' ) === 'Pallet Count' ).exists()).toBe( true )
      expect( inputs.find( i => i.props( 'label' ) === 'Pallet Count' ).props().required ).toBe( false )
    })

    it( 'validates required fields', async () => {
      expect( asnGeneralInfo.value.shipperName.valid ).toBe( false )
      expect( asnGeneralInfo.value.expectedDate.valid ).toBe( false )

      // There is a default carrier Unknown
      expect( asnGeneralInfo.value.carrier.valid ).toBe( true )

      await wrapper.vm.$nextTick()

      asnGeneralInfo.value.shipperName.value = 'Test Shipper'
      asnGeneralInfo.value.facilityCode.value = 'DC6'
      asnGeneralInfo.value.expectedDate.value = new Date( '2025-01-22T00:00:00.000Z' )

      await wrapper.vm.$nextTick()

      expect( asnGeneralInfo.value.carrier.valid ).toBe( true )
      expect( asnGeneralInfo.value.carrier.value ).toBe( 'Unknown' )

      expect( asnGeneralInfo.value.shipperName.valid ).toBe( true )
      expect( asnGeneralInfo.value.expectedDate.valid ).toBe( true )
      expect( asnGeneralInfo.value.facilityCode.valid ).toBe( true )
    })

  })

  describe( 'functionality', () => {

    it( 'sets facility code on mount when defaultFacility is not ALL', async () => {
      defaultFacility.value = 'DC6'

      const newWrapper = mount( GeneralDetails )

      expect( asnGeneralInfo.value.facilityCode.value ).toBe( 'DC6' )
      expect( newWrapper.vm.hasFacility ).toBe( true )
    })

    it( 'enables continue button when form is valid and disables it when form is invalid', async () => {
      const allButtons = wrapper.findAllComponents( Button )
      const continueButton = allButtons.find( b => b.attributes()['data-button'] === 'continue' )

      expect( continueButton.props().disabled ).toBe( false )

      asnGeneralInfo.value.shipperName.value = null
      asnGeneralInfo.value.expectedDate.value = null

      await wrapper.vm.$nextTick()

      expect( continueButton.props().disabled ).toBe( true )
    })

    it( 'navigates to products step when continue is clicked', async () => {

      asnGeneralInfo.value.shipperName.value = 'testname'
      asnGeneralInfo.value.expectedDate.value = new Date( '2025-01-22T00:00:00.000Z' )
      asnGeneralInfo.value.carrier.value = 'Unknown'

      await wrapper.vm.$nextTick()
      const allButtons = wrapper.findAllComponents( Button )

      const continueButton = allButtons.find( b => b.attributes()['data-button'] === 'continue' )

      expect( continueButton.props().disabled ).toBe( false )

      await continueButton.trigger( 'click' )

      expect( activeStep.value ).toBe( Flow.PRODUCTS )
    })

    it( 'shows facility selection when defaultFacility is ALL', async () => {
      defaultFacility.value = 'ALL'
      const newWrapper = mount( GeneralDetails )
      const facilityRadio = newWrapper.findComponent( Radio )

      expect( facilityRadio.isVisible()).toBe( true )
      expect( facilityRadio.attributes( 'disabled' )).toBeUndefined()
    })

  })

})

describe( 'create ASN :: AsnProducts', () => {
  // Create a div to serve as the teleport target
  const teleportTarget = document.createElement( 'div' )
  teleportTarget.id = 'app'
  document.body.appendChild( teleportTarget )

  const wrapper = mount( AsnProducts, {
    attachTo: document.body,
  })

  it( 'exists', () => {
    expect( wrapper.exists()).toBe( true )
  })

  describe( 'structure', () => {

    it( 'renders products section header', () => {
      const productsSection = wrapper.find( '[data-section="products"]' )
      expect( productsSection.exists()).toBe( true )
      expect( wrapper.find( '[data-section="products"] p' ).text()).toBe( 'Selected Products' )
    })

    it( 'renders the products table', () => {
      const table = wrapper.findComponent( Table )
      expect( table.exists()).toBe( true )
    })

    it( 'renders navigation buttons', () => {
      const buttons = wrapper.findAllComponents( Button )
      const continueButton = buttons.find( b => b.attributes()['data-button'] === 'continue' )

      expect( continueButton.exists()).toBe( true )
    })

  })

  describe( 'functionality', () => {
    it( 'disables continue button when no products are selected', async () => {
      asnItems.value = []
      await wrapper.vm.$nextTick()

      const continueButton = wrapper.findAllComponents( Button )
        .find( b => b.attributes()['data-button'] === 'continue' )

      expect( continueButton.props().disabled ).toBe( true )
    })

    it( 'enables continue button when products are selected', async () => {
      asnItems.value = [
        {
          id:                  814676,
          sku:                 'testbob1',
          title:               'portal review1',
          availableQuantity:   0,
          availableByFacility: [
            {
              facilityName:   'West - Los Angeles, CA',
              facilityCode:   'DC6',
              unitsAvailable: 0,
              lots:           null
            }
          ],
          backOrdered:   0,
          price:         0.1,
          supplier:      '',
          kitComponents: null,
          inventoryId:   814676,
          locked:        false,
          quantity:      1
        },
        {
          id:                  814675,
          sku:                 'SKU-0014443',
          title:               'Title',
          availableQuantity:   0,
          availableByFacility: [
            {
              facilityName:   'West - Los Angeles, CA',
              facilityCode:   'DC6',
              unitsAvailable: 0,
              lots:           null
            }
          ],
          backOrdered:   0,
          price:         149.99,
          supplier:      'Supplier 1',
          kitComponents: null,
          inventoryId:   814675,
          locked:        false,
          quantity:      1
        }
      ]

      await wrapper.vm.$nextTick()

      const continueButton = wrapper.findAllComponents( Button )
        .find( b => b.attributes()['data-button'] === 'continue' )

      expect( continueButton.props().disabled ).toBe( false )
    })

    it( 'navigates to next step when continue button is clicked', async () => {
      asnItems.value = [
        {
          id:                  814676,
          sku:                 'testbob1',
          title:               'portal review1',
          availableQuantity:   0,
          availableByFacility: [
            {
              facilityName:   'West - Los Angeles, CA',
              facilityCode:   'DC6',
              unitsAvailable: 0,
              lots:           null
            }
          ],
          backOrdered:   0,
          price:         0.1,
          supplier:      '',
          kitComponents: null,
          inventoryId:   814676,
          locked:        false,
          quantity:      1
        },
        {
          id:                  814675,
          sku:                 'SKU-0014443',
          title:               'Title',
          availableQuantity:   0,
          availableByFacility: [
            {
              facilityName:   'West - Los Angeles, CA',
              facilityCode:   'DC6',
              unitsAvailable: 0,
              lots:           null
            }
          ],
          backOrdered:   0,
          price:         149.99,
          supplier:      'Supplier 1',
          kitComponents: null,
          inventoryId:   814675,
          locked:        false,
          quantity:      1
        }
      ]
      await wrapper.vm.$nextTick()

      const continueButton = wrapper.findAllComponents( Button )
        .find( b => b.attributes()['data-button'] === 'continue' )

      await continueButton.trigger( 'click' )
      expect( activeStep.value ).toBe( Flow.SUMMARY )
    })
  })

  describe( 'table functionality', () => {

    it( 'displays selected products in the table', async () => {
      const testProducts = [
        {
          id:                  814676,
          sku:                 'testbob1',
          title:               'portal review1',
          availableQuantity:   0,
          availableByFacility: [
            {
              facilityName:   'West - Los Angeles, CA',
              facilityCode:   'DC6',
              unitsAvailable: 0,
              lots:           null
            }
          ],
          backOrdered:   0,
          price:         0.1,
          supplier:      '',
          kitComponents: null,
          notes:         'test-notes',
          inventoryId:   814676,
          locked:        false,
          quantity:      1
        },
        {
          id:                  814675,
          sku:                 'SKU-0014443',
          title:               'Title',
          availableQuantity:   0,
          description:         'test-desc',
          availableByFacility: [
            {
              facilityName:   'West - Los Angeles, CA',
              facilityCode:   'DC6',
              unitsAvailable: 0,
              lots:           null
            }
          ],
          backOrdered:   0,
          price:         149.99,
          supplier:      'Supplier 1',
          kitComponents: null,
          inventoryId:   814675,
          locked:        false,
          quantity:      1
        }
      ]

      asnItems.value = testProducts

      await wrapper.vm.$nextTick()

      const tables = wrapper.findAllComponents( Table )

      const rows = tables[1].findAllComponents( Row )

      // +1 for header row
      expect( rows.length ).toBe( testProducts.length + 1 )

      testProducts.forEach(( product, index ) => {
        const rowCells = rows[index + 1].findAllComponents( Cell )

        // SKU cell
        expect( rowCells[0].text()).toBe( product.sku )

        // Title cell
        expect( rowCells[1].text()).toBe( product.title )

        // Description cell
        expect( rowCells[2].text()).toBe( product?.notes || '/' )

        // Notes cell
        expect( rowCells[3].text()).toBe( product?.description || '/' )

        // Quantity cell
        expect( rowCells[4].text()).toBe( product.quantity.toString())
      })

    })

  })

  describe( 'editing selected product', () => {

    it( 'has selected products', () => {
      expect( asnItems.value.length ).toBeGreaterThan( 0 )
    })

    it( 'renders the correct fields in the form and enables the Save Product button', async () => {

      const sidebar = wrapper.findComponent( Sidebar )

      wrapper.vm.asnItemModel = {
        id:                  { value: null, valid: true },
        sku:                 { value: 'SKU-0014443', valid: true },
        notes:               { value: 'notes', valid: true },
        title:               { value: 'Title', valid: true },
        price:               { value: null, valid: true },
        quantity:            { value: 1, valid: true },
        supplier:            { value: null, valid: true },
        description:         { value: 'description', valid: true },
        backOrdered:         { value: null, valid: true },
        kitComponents:       { value: null, valid: true },
        availableQuantity:   { value: null, valid: true },
        availableByFacility: { value: null, valid: true }
      }

      wrapper.vm.openEditAsnProduct = true

      await wrapper.vm.$nextTick()

      const allInputs = sidebar.findAllComponents( Input )

      expect( allInputs.length ).toBe( 5 )

      // SKU input
      expect( allInputs[0].props().label ).toBe( 'SKU' )
      expect( allInputs[0].props().modelValue ).toBe( 'SKU-0014443' )
      expect( allInputs[0].props().readonly ).toBe( true )

      // Title input
      expect( allInputs[1].props().label ).toBe( 'Title' )
      expect( allInputs[1].props().modelValue ).toBe( 'Title' )

      // Unit input
      expect( allInputs[2].props().label ).toBe( 'Unit' )
      expect( allInputs[2].props().modelValue ).toBe( 1 )

      // Description input
      expect( allInputs[3].props().label ).toBe( 'Description' )
      expect( allInputs[3].props().modelValue ).toBe( 'description' )

      // Description input
      expect( allInputs[4].props().label ).toBe( 'Notes' )
      expect( allInputs[4].props().modelValue ).toBe( 'notes' )

    })

    it( 'renders the Save Products button disabled', () => {
      const sidebar = wrapper.findComponent( Sidebar )
      const allButtons = sidebar.findAllComponents( Button )

      const saveProductButton = allButtons.find( b => b.attributes()['data-button'] === 'save-product' )

      expect( saveProductButton.text()).toBe( 'Save Product' )
      expect( saveProductButton.props().disabled ).toBe( true )
    })

    it( 'enables the save product button when model is valid', async () => {
      vi.spyOn( await import( '@lib/scripts/inputValidation' ), 'validateModel' ).mockReturnValue( true )

      const newWrapper = mount( AsnProducts, {
        attachTo: document.body
      })

      newWrapper.vm.openEditAsnProduct = true
      await newWrapper.vm.$nextTick()

      const sidebar = newWrapper.findComponent( Sidebar )
      const allButtons = sidebar.findAllComponents( Button )
      const saveProductButton = allButtons.find( b => b.attributes()['data-button'] === 'save-product' )

      expect( saveProductButton.text()).toBe( 'Save Product' )
      expect( saveProductButton.props().disabled ).toBe( false )
    })

  })

})

describe( 'create ASN :: Summary', () => {

  const wrapper = mount( AsnSummary )

  it( 'exists', () => {
    expect( wrapper.exists()).toBe( true )
  })

  describe( 'structure', () => {

    it( 'renders all section titles', () => {
      const productsSectionTitle = wrapper.find( '[data-section="products"] p' )
      expect( productsSectionTitle.text()).toBe( 'ASN Products' )

      const clientSectionTitle = wrapper.find( '[data-section="client-details"] p' )
      expect( clientSectionTitle.text()).toBe( 'Client Details' )

      const shippingSectionTitle = wrapper.find( '[data-section="shipping-details"] p' )
      expect( shippingSectionTitle.text()).toBe( 'Shipping Details' )

      expect( wrapper.find( '[data-section="client-details"]' ).text()).toBe( 'Client Details Client Reference: / Client PO Number: / Instruction Notes: /' )
      expect( wrapper.find( '[data-section="shipping-details"]' ).text()).toBe( 'Shipping Details Shipper Name: testname Expected Date: Jan 22, 2025 Carrier: Unknown Carton/Package Count: 0 Pallet Count: 0 Facility: West - Los Angeles, CA Auto Release: Yes' )
    })

  })

  describe( 'creation', () => {

    it( 'handles multiple rapid clicks correctly', async () => {
      const submitAsnSpy = vi.spyOn( wrapper.vm, 'submitAsn' )
      const allButton = wrapper.findAllComponents( Button )
      const submitButton = allButton.find( b => b.attributes()['data-button'] === 'submit-asn' )

      // Trigger multiple rapid clicks
      await submitButton.trigger( 'click' )
      await submitButton.trigger( 'click' ) // Must click twice for the VUE event system to register the called function

      // Should only call once due to pending state protection
      expect( submitAsnSpy ).toHaveBeenCalledTimes( 1 )
    })

  })

})
