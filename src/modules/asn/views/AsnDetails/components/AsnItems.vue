<script setup lang="ts">

import { confirm } from '@lib/store/confirm'
import { useRouter } from 'vue-router'
import { Guard, guard } from '@/plugins/guard'
import { receiveParams } from '@/modules/asn/routes'
import { computed, ref, watch } from 'vue'
import { productDetailsOptions } from '@/store'
import { setAlertOptions, setNotificationOptions } from '@lib/store/snackbar'
import { blockPageUpdate, checkValue, removeEmptyKeysFromObject, viewSetup } from '@lib/scripts/utils'
import { asn, createAsnItem, deleteAsnItem, getAsnItem, getAsnItems, isCached, isRpc, updateAsnItem } from '@/modules/asn/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Table from '@lib/components/table/Table.vue'
import Button from '@lib/components/buttons/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import EditAsnItem from '@/modules/asn/views/AsnDetails/components/EditAsnItem.vue'

import type { BatchOption } from '@lib/types/tableTypes'
import type { InventoryPanelProduct } from '@/modules/inventory/types'
import type { AsnItem, AsnItemReceive, ReceiveParams } from '@/modules/asn/types'

const props = defineProps<{
  asnId?:      number
  viewParams?: ReceiveParams
}>()

const router              = useRouter()
const params              = defineModel<ReceiveParams>( 'viewParams', { default: receiveParams })
const canEdit             = computed<boolean>(() => asn.value?.asnStatus === 'Pending' )
const rpcTimeout          = ref<NodeJS.Timeout>( null )
const asnItemsData        = ref<PaginatedResponse<'asnItems', Tablify<AsnItem, AsnItemReceive>[]>>( null )
const shouldUpdate        = ref( false )
const itemsPending        = ref<boolean>( false )
const editAsnItemId       = ref<number>( null )
const openAsnItemEdit     = ref( false )
const canConfirmItems     = computed(() => selectedProducts.value.length && selectedProducts.value.every( item => checkValue( item.quantity )))
const addedProductsMap    = ref<AsnItem[]>( [] )
const selectedProducts    = ref<InventoryPanelProduct[]>( [] )

watch( isRpc, ( n ) => {

  // If the isRpc value is set to true, it means that
  // the data was fetched from the cache or the items were updated.

  // Set a timeout to reset the isRpc value after 30 seconds

  if ( n ) {
    clearTimeout( rpcTimeout.value )
    rpcTimeout.value = setTimeout(() => isRpc.value = false, 30000 )
  }

})

function generateFilters( selectedFilters: Partial<any> ) {

  return {
    key:   'receiveId',
    label: 'Receive',
    value: selectedFilters.receiveId ?? null
  }

}

function asnItemOptions( asnItem: AsnItem ): DropListOption[] {

  return [
    {
      id:     1,
      name:   'Delete',
      hidden: !canEdit.value || !guard( 'Asn.Write' ),
      icon:   {
        name:  'delete',
        color: '#FF4343',
        size:  'm'
      },
      action: () => confirm({
        header:      'Delete product from ASN',
        description: `Are you sure you want to remove <span class="text-main font-medium" >${asnItem.sku}</span>?`,
        action:      async () => deleteAsnItem( asn.value.id, asnItem.id ).then( async ( ) => {

          for ( let i = 0; i < asnItemsData.value.asnItems.length; i++ ) {
            if ( asnItemsData.value.asnItems[i].sku === asnItem.sku ) {
              asnItemsData.value.asnItems.splice( i, 1 )
              break
            }
          }

          isRpc.value = true
          setNotificationOptions({ message: `ASN product is deleted successfully.` })
          updateView()

        })
      })
    },
    {
      id:     2,
      name:   'Edit',
      hidden: !canEdit.value || !guard( 'Asn.Write' ),
      icon:   {
        name: 'edit'
      },
      action: () => {
        openAsnItemEdit.value = true
        editAsnItemId.value = asnItem.id
      }
    },
    {
      id:   2,
      name: 'View Details',
      icon: {
        name: 'eye-open',
        size: 'm'
      },
      action: () => {
        productDetailsOptions.id = asnItem.inventoryId
        productDetailsOptions.type = 'Live'
      }
    },
  ]
}

const asnItemsBatchOptions: BatchOption<AsnItem>[] = [
  {
    id:             1,
    type:           'negative',
    icon:           'delete',
    group:          'Bulk Actions',
    action:         selected => deleteSelectedAsnLineItems( selected ),
    actionName:     'Delete',
    pendingMessage: 'Deleting items',
  }
]

function schema( lineItem?: AsnItem ): TableSchema<AsnItem> {

  const expected    = lineItem?.expectedUnits || 0
  const received    = lineItem?.receivedUnits || 0
  const difference  =  received - ( expected + ( lineItem?.quantityDamaged || 0 ))

  return [
    {
      key:       'sku',
      label:     'SKU',
      format:    'custom',
      class:     lineItem?.isReceive ? 'text-main' : '',
      transform: ( value ) => {

        let cellValue = value

        if ( lineItem?.isReceive )
          cellValue = `Receive [${lineItem.sku}]`

        else if ( lineItem?.itemReceives?.length > 0 )
          cellValue = `<div class="flex items-center space-x-3" ><div class="bg-main text-core-10 px-[0.275rem] rounded-[0.063rem] w-fit h-4 text-xs flex items-center">${lineItem?.itemReceives?.length}</div><p class="truncate"> ${lineItem.sku}</p></div>`

        return cellValue

      },
    },
    {
      key:   'title',
      label: 'Title'
    },
    {
      key:   'description',
      label: 'Description'
    },
    {
      key:   'notes',
      label: 'Notes'
    },
    {
      key:       'expectedUnits',
      label:     'Expected Units',
      format:    'custom',
      transform: () => {
        return lineItem?.isReceive ? '' : lineItem.expectedUnits.toString()
      }
    },
    {
      key:   'receivedUnits',
      label: 'Received Units',
      class: 'text-main'
    },
    {
      key:   'quantityDamaged',
      label: 'Damaged',
      class: `${lineItem?.quantityDamaged > 0 && 'text-error'}`,
    },
    {
      key:       null,
      label:     'Difference',
      format:    'custom',
      value:     0,
      class:     `${difference > 0 ? 'text-success' : difference < 0 ? 'text-error' : 'text-core-50'}`,
      transform: ( ) => {
        if ( lineItem?.isReceive )
          return ''

        return difference > 0 ? `+${difference?.toString()}` : difference?.toString()
      }
    }
  ]
}

function openInventory() {

  router.push({
    name:   'Asn Items Inventory',
    params: { asnId: asn.value.id }
  })

}

function closeInventorySidebar() {

  blockPageUpdate( !shouldUpdate.value )

  isRpc.value = shouldUpdate.value

  shouldUpdate.value = false
  selectedProducts.value = []
  addedProductsMap.value = []

  router.push({ name: 'Asn Details', params: { asnId: asn.value.id } })

}

function cancelInventorySidebar() {

  blockPageUpdate()

  shouldUpdate.value = false
  selectedProducts.value = []

  router.push({ name: 'Asn Details', params: { asnId: asn.value.id } })

}

async function deleteSelectedAsnLineItems( selectedRecords: AsnItem[] ) {

  if ( selectedRecords.length === 0 ) {
    setAlertOptions({
      message:  'No items selected.',
      details:  'Please select at least one item to delete.',
      severity: 'warning'
    })
    return
  }

  try {

    await Promise.all(
      selectedRecords.map( item =>
        deleteAsnItem( asn.value.id, item.id )
      )
    )

    for ( const item of selectedRecords ) {

      for ( let i = 0; i < asnItemsData.value.asnItems.length; i++ ) {

        if ( asnItemsData.value.asnItems[i].sku === item.sku ) {
          asnItemsData.value.asnItems.splice( i, 1 )
          break
        }

      }

    }

    isRpc.value = true
    setNotificationOptions({ message: 'All selected products have been successfully deleted from the ASN.' })
    updateView()

  }

  catch {
    setAlertOptions({
      message:  'Failed to remove items.',
      details:  'An error occurred while deleting selected items.',
      severity: 'error'
    })
  }

}

async function addProduct( product: InventoryPanelProduct ): Promise<InventoryPanelProduct> {

  const lineItemData = await getAsnItem( asn.value.id, product.id )
  const lineItem = lineItemData.payload

  shouldUpdate.value = true

  if ( !lineItem )
    return product

  addedProductsMap.value.push( lineItem )

  return {
    id:       lineItem.inventoryId,
    sku:      lineItem.sku,
    title:    lineItem.title,
    quantity: lineItem.expectedUnits
  } as InventoryPanelProduct

}

async function confirmProducts() {

  itemsPending.value = true

  const promises = selectedProducts.value.map( async ( product ) => {

    const lineItem = addedProductsMap.value.find( item => item.sku === product.sku )

    if ( lineItem ) {

      return updateAsnItem( asn.value.id, {
        id:            lineItem.id,
        sku:           product.sku,
        title:         product.title,
        expectedUnits: product.quantity
      })

    }

    else {

      return createAsnItem( asn.value.id, {
        sku:           product.sku,
        title:         product.title || product.sku,
        expectedUnits: product.quantity,
      })

    }

  })

  await Promise.all( promises )

  setNotificationOptions({ message: 'ASN products are updated successfully.' })

  itemsPending.value = false

  closeInventorySidebar()

}

const filterByReceive = computed(() => generateFilters( removeEmptyKeysFromObject( props.viewParams )))

function itemReceivesSchema( item: AsnItemReceive ): TableSchema<AsnItemReceive> {
  return [
    {
      key:   'receiveId',
      label: 'Receive ID'
    },
    {
      key:   'quantityReceived',
      label: 'Received Units',
      class: 'text-main'
    },
    {
      key:   'quantityDamaged',
      label: 'Unusable Units',
      class: `${item?.quantityDamaged > 0 && 'text-error'}`,
    }
  ]
}

async function getAsnItemsData( asnParams: ReceiveParams ) {

  const rpc       = isCached.value || isRpc.value
  const rpcParams = rpc ? { isRpc: true } : {}

  const asnItemsPayload = await getAsnItems( props.asnId, { ...asnParams, ...rpcParams })

  if ( !asnItemsPayload.error ) {

    asnItemsData.value = asnItemsPayload?.payload ?? null

    if ( asnItemsData.value?.asnItems ) {

      asnItemsData.value.asnItems?.forEach(( item ) => {

        if ( item?.itemReceives ) {

          item.nested = {
            name:         'Item Receives',
            type:         'nested',
            schema:       itemReceivesSchema,
            records:      item?.itemReceives,
            recordMapKey: 'id',
          }

        }

      })

    }

  }

}

const { pending, updateView } = viewSetup(
  'Asn Details',
  params.value,
  router,
  getAsnItemsData
)

defineExpose({ openAsnItemEdit, editAsnItemId, props, schema, asnItemsData, params, asnItemOptions, canEdit })

</script>

<template>

  <div class="w-full h-full overflow-hidden grid md:shadow-custom">

    <Table
      v-model:params="params"
      name="ASN Items"
      :schema="schema"
      :pending="pending"
      :records="asnItemsData?.asnItems || []"
      :selectable="canEdit && guard('Asn.Write')"
      :hide-labels="asnItemsData?.asnItems?.length === 0"
      record-map-key="id"
      :batch-options="asnItemsBatchOptions"
      :record-options="(record) => asnItemOptions(record)"
      :pagination="{
        total: asnItemsData?.totalRows,
        maxPages: asnItemsData?.totalPages,
      }"
    >

      <template #table-head>

        <div class="bg-core-20 h-10 pl-4 flex items-center">

          <div class="h-full grow flex items-center gap-3">
            <span class="text-main-70">
              <Icon name="product" size="m" />
            </span>
            <p class="text-sm font-medium">
              Products
            </p>
          </div>

          <Guard scope="Asn.Write">

            <Button
              v-if="canEdit"
              size="auto"
              mode="naked"
              class="h-full px-4 flex items-center space-x-2 border-l border-l-core-30"
              @click="openInventory"
            >
              <p class="text-sm">
                Add Product
              </p>
              <Icon class="text-main" name="add" size="s" />
            </Button>

          </Guard>

        </div>

      </template>

      <template #table-neck>

        <div v-if="params.receiveId" class="py-1 px-2 flex flex-wrap items-center border-b border-core-30">

          <div
            v-show="filterByReceive.value"
            :key="filterByReceive.key"
            class="w-max h-8 pl-2 flex items-center space-x-1 bg-main-20 border border-main-30 rounded-xs mr-2 my-1"
          >

            <p class="text-xs md:text-sm font-light">
              {{ filterByReceive.label }}:  <span class="font-medium">{{ [parseInt(filterByReceive.value)] }}</span>
            </p>

            <Button
              size="auto"
              type="box"
              mode="naked"
              :icon="{
                name: 'close',
                size: 's',
              }"
              class="h-full w-8"
              @click="() => params.receiveId = null"
            />

          </div>

        </div>

      </template>

    </Table>

    <Sidebar
      :strict="shouldUpdate"
      :open="$route.name === 'Asn Items Inventory'"
      custom-tw-offset="top-0 md:top-[3rem] h-full md:h-[calc(100%-3rem)]"
      @close="closeInventorySidebar"
    >

      <div class="w-full md:w-[26rem] transition-[width] h-full grid grid-rows-[1fr_max-content] md:grid-rows-[1fr_max-content] ignore-outside">

        <RouterView v-slot="{ Component }">

          <Component
            :is="Component"
            v-model:selected-products="selectedProducts"
            :allow-price-update="false"
            :disabled="itemsPending"
            :actions="{
              add: addProduct,
            }"
          />

        </RouterView>

        <div
          class="w-full h-12 grid grid-cols-2"
          :class="{ 'md:hidden': !shouldUpdate, 'md:grid': shouldUpdate }"
        >

          <Button
            size="auto"
            mode="secondary"
            :disabled="itemsPending"
            @click="cancelInventorySidebar"
          >
            Cancel
          </Button>

          <Button
            size="auto"
            :disabled="!canConfirmItems"
            :pending="itemsPending"
            @click="confirmProducts"
          >
            Confirm Products
          </Button>

        </div>

      </div>

    </Sidebar>

    <Sidebar
      :dim="true"
      :open="openAsnItemEdit"
      :strict="true"
      @close="openAsnItemEdit = false"
    >

      <EditAsnItem v-model:asn-items-data="asnItemsData.asnItems" :line-item-id="editAsnItemId" @close="openAsnItemEdit = false" />

    </Sidebar>

  </div>

</template>
