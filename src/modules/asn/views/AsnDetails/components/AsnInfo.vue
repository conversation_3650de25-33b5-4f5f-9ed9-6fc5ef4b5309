<script setup lang="ts">

import { ref } from 'vue'
import { asn } from '@/modules/asn/store'
import { Guard } from '@/plugins/guard'
import { booleanToYesNo } from '@/store'
import { checkValue, formatDate } from '@lib/scripts/utils'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import DetailsCard from '@/components/DetailsCard.vue'
import AsnDetailsForm from '@/modules/asn/views/AsnDetails/components/AsnDetailsForm.vue'

defineProps<{
  canEdit:             boolean
  isPartial?:          boolean
  openDesktopActions?: boolean
}>()

const openDesktopActions = defineModel<boolean>( 'openDesktopActions' )
const openEditAsnDetails = ref( false )

defineExpose({
  openEditAsnDetails
})

</script>

<template>

  <div class="h-full md:bg-core-10 grid md:grid-rows-[max-content_1fr_max-content] grid-rows-[max-content_1fr] overflow-hidden">

    <div class="w-full h-10 sticky top-0 z-1 pl-4 flex items-center space-x-3 bg-core-20 border-b border-core-30">

      <Icon name="details" size="m" class="text-main" />

      <p class="text-sm font-medium grow">
        Details
      </p>

      <Guard scope="Asn.Write">

        <Button
          mode="naked"
          size="auto"
          class="h-full px-4 flex items-center space-x-3"
          :disabled="!canEdit"
          @click="openEditAsnDetails = true"
        >

          <p class="text-sm">
            Edit
          </p>

          <span class="text-main">
            <Icon name="edit" size="s" />
          </span>

        </Button>

      </Guard>

    </div>

    <div class="bg-core-10 overflow-y-auto h-full">

      <div class="text-sm grid grid-cols-2 gap-px bg-core-30">

        <DetailsCard
          label="Shipper Name"
          class="col-span-2"
          :content="asn?.shipperName || '/'"
        />

        <DetailsCard
          label="Facility"
          class="col-span-2"
          :content="asn?.facility || '/'"
        />

        <DetailsCard
          label="OWD Reference"
          :content="asn?.id || '/'"
        />

        <DetailsCard
          label="Auto Release"
          :content="booleanToYesNo(asn?.isAutoRelease)"
        />

        <DetailsCard
          label="Expected Date"
          :content="asn?.expectedDate ? formatDate(asn.expectedDate, 'MMM DD, YYYY') : '/'"
        />

        <DetailsCard
          label="Carrier"
          :content="asn?.carrier || '/'"
        />

        <DetailsCard
          label="Carton/Package"
          :content="checkValue(asn?.cartonCount) ? asn.cartonCount : '/'"
        />

        <DetailsCard
          label="Pallet Count"
          :content="checkValue(asn?.palletCount) ? asn.palletCount : '/'"
        />

        <DetailsCard
          label="Client Reference"
          class="col-span-2"
          :content="asn?.clientReference || '/'"
        />

        <DetailsCard
          label="Client PO Number"
          class="col-span-2"
          :content="asn?.clientPo || '/'"
        />

        <DetailsCard
          label="Is Blind"
          class="col-span-2"
          :content="booleanToYesNo(asn?.hasBlind)"
        />

        <DetailsCard
          label="Created"
          class="col-span-2"
          :content="asn?.createdDate ? formatDate(asn?.createdDate, 'MMM DD, YYYY [at] HH:mm') : '/'"
        />

        <DetailsCard
          label="Created By"
          class="col-span-2"
          :content="asn?.createdBy || '/'"
        />

        <DetailsCard
          label="Instruction Notes"
          class="col-span-2 border-b-core-30 border-b"
          :content="asn?.notes || '/'"
        />

      </div>

    </div>

    <Guard scope="Asn.Write">

      <div v-if="canEdit || isPartial" class="w-full hidden h-10 md:inline">

        <Button data-button-desktop="toggle-actions" class="w-full h-full flex items-center justify-between px-6 font-medium" size="auto" @click="openDesktopActions = true">
          <span>Actions</span>
          <Icon name="chevron-right" size="m" />
        </Button>

      </div>

    </Guard>

    <Sidebar
      name="edit-details"
      :dim="true"
      :open="openEditAsnDetails"
      :strict="true"
      @close="openEditAsnDetails = false"
    >
      <AsnDetailsForm @close="openEditAsnDetails = false" />
    </Sidebar>

  </div>

</template>
