<script setup lang="ts">

import { reactive } from 'vue'
import { useRouter } from 'vue-router'
import { formatDate, viewSetup } from '@lib/scripts/utils'
import { asn, asnReceivesData, getAsnReceives, isCached } from '@/modules/asn/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Table from '@lib/components/table/Table.vue'

import type { ReceiveItem, ReceiveParams } from '@/modules/asn/types'

const props = defineProps<{
  asnId:       number
  canEdit?:    boolean
  activeView?: number
}>()

const emits       = defineEmits<{ activeReceiveId: [number] }>()
const router      = useRouter()
const params      = reactive<ReceiveParams>({ page: 1, pageSize: 10 })
const activeView  = defineModel<number>( 'activeView' )

function schema( ): TableSchema<ReceiveItem> {
  return [
    {
      key:       'id',
      label:     'Receive ID',
      format:    'custom',
      class:     'text-main',
      transform: ( value ) => {
        return `Receive [${value}]`
      }
    },
    {
      key:       'receiveDate',
      label:     'Date',
      format:    'custom',
      transform: ( value ) => {
        return formatDate( value, 'MMM DD, YYYY [at] HH:mm' )
      }
    },
    {
      key:   'receiveBy',
      label: 'By'
    },
    {
      key:   'receiveMin',
      label: 'Minutes'
    },
    {
      key:   'receiveCartons',
      label: 'Cartons'
    },
    {
      key:   'receivePallets',
      label: 'Pallets'
    },
    {
      key:   'receiveNotes',
      label: 'Notes'
    }
  ]
}

async function getData( viewParams: ReceiveParams ) {

  if ( isCached.value ) {
    return {
      error:   null,
      status:  200,
      payload: []
    }
  }

  const { error, status, payload } = await getAsnReceives( props.asnId, viewParams )
  asnReceivesData.value = payload

  return {
    error,
    status,
    payload
  }

}

function receiveRowOptions( record: ReceiveItem ): DropListOption[] {
  return [
    {
      id:   1,
      name: 'View Received Products',
      icon: {
        name: 'eye-open',
        size: 'm'
      },
      action: async () => {
        emits( 'activeReceiveId', record.id )
        activeView.value = 2
      }
    }
  ]
}

const { pending } = viewSetup(
  null,
  params,
  router,
  getData,
  null,
  null,
  true
)

</script>

<template>

  <div class="w-full grid overflow-hidden md:shadow-custom">

    <Table
      v-model:params="params"
      :pending="pending"
      name="Receives"
      :custom-empty-message="`${asn?.asnStatus ? `ASN ${asn?.asnStatus}. ` : ''}There are no receives for this ASN.`"
      :records="asnReceivesData?.asnReceives || []"
      :schema="schema"
      record-map-key="receiveMin"
      :hide-labels="!asnReceivesData?.asnReceives"
      :flex="true"
      :record-options="receiveRowOptions"
      :pagination="{
        total: asnReceivesData?.totalRows,
        maxPages: asnReceivesData?.totalPages,
        statePagination: true,
      }"
    >

      <template #table-head>

        <div class="bg-core-20 h-10 pl-4 grow flex items-center gap-3">
          <span class="text-main-70">
            <Icon name="received" size="m" />
          </span>
          <p class="text-sm font-medium">
            Receives
          </p>
        </div>

      </template>

    </Table>

  </div>

</template>
