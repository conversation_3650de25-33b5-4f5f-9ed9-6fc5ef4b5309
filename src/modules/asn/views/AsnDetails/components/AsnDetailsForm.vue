<script setup lang="ts">

import { validateModel } from '@lib/scripts/inputValidation'
import { setNotificationOptions } from '@lib/store/snackbar'
import { computed, reactive, ref } from 'vue'
import { defaultFacility, facilitiesList } from '@/store'
import { asn, asnCarriersList, updateAsnDetails } from '@/modules/asn/store'
import { checkValue, convertObjectToPlain, convertObjectToValidatable } from '@lib/scripts/utils'

import Icon from '@lib/components/blocks/Icon.vue'
import Input from '@lib/components/inputs/Input.vue'
import Select from '@lib/components/inputs/Select.vue'
import Button from '@lib/components/buttons/Button.vue'
import Textbox from '@lib/components/inputs/Textbox.vue'
import Checkbox from '@lib/components/inputs/Checkbox.vue'
import DatePicker from '@lib/components/inputs/DatePicker.vue'

import type { AsnDetails } from '@/modules/asn/types'

const emits = defineEmits<{ close: [] }>()

const asnDetailsInfo: AsnDetails = {
  notes:           asn.value?.notes || null,
  carrier:         asn.value?.carrier || null,
  facility:        asn.value?.facility || null,
  clientPo:        asn.value?.clientPo || null,
  asnStatus:       asn.value?.asnStatus || null,
  palletCount:     checkValue( asn.value?.cartonCount ) ? asn.value.palletCount : null,
  cartonCount:     checkValue( asn.value?.cartonCount ) ? asn.value.cartonCount : null,
  shipperName:     asn.value?.shipperName || null,
  expectedDate:    asn.value?.expectedDate || null,
  facilityCode:    asn.value?.facilityCode || null,
  isAutoRelease:   asn.value?.isAutoRelease || null,
  clientReference: asn.value?.clientReference || null,
}

const pending       = ref<boolean>( false )
const canCreate     = computed(() => validateModel( asnFormModel ))
const asnFormModel  = reactive<Validatable<AsnDetails>>( convertObjectToValidatable( asnDetailsInfo, null, [ 'facility', 'clientReference', 'clientPo', 'isAutoRelease', 'asnStatus' ], ))

async function submitAsn() {

  pending.value = true

  const asnFormData = convertObjectToPlain( asnFormModel )

  const { payload } = await updateAsnDetails(
    asn.value.id,
    {
      ...asnFormData,
      palletCount: checkValue( asnFormData.palletCount ) ? asnFormData.palletCount : null,
      cartonCount: checkValue( asnFormData.cartonCount ) ? asnFormData.cartonCount : null,
    }
  )

  if ( payload ) {

    pending.value = true

    asn.value = payload

    setNotificationOptions({ message: 'ASN details are updated successfully.' })

  }

  emits( 'close' )

}

defineExpose({
  asnFormModel,
  submitAsn,
  canCreate
})

</script>

<template>

  <div class="md:w-[38rem] h-full grid grid-rows-[max-content_1fr]">

    <div class="h-12 sticky top-0 z-1 flex items-center bg-core-20 border-b border-core-30">

      <div class="h-full px-4 flex items-center space-x-3 grow border-r border-core-30">

        <Icon name="edit" size="m" class="text-main" />

        <p data-element="header" class="text-sm font-medium">
          Edit <span class="text-main">[{{ asn?.id }}]</span>
        </p>

      </div>

      <Button
        mode="naked"
        type="box"
        size="auto"
        class="h-full w-12"
        :icon="{
          name: 'close',
          size: 'm',
        }"
        :disabled="pending"
        @click="emits('close')"
      />

    </div>

    <div class="w-full h-full grid grid-rows-[1fr_max-content] overflow-hidden">
      <form
        class="md:p-8 h-full flex flex-col md:grid md:grid-cols-2 md:gap-4 content-start overflow-y-auto"
        :class="{
          'pointer-events-none': pending,
        }"
        @submit.prevent
      >
        <Input
          v-model="asnFormModel.shipperName.value"
          v-model:valid="asnFormModel.shipperName.valid"
          label="Shipper Name"
          :required="true"
        />
        <Select
          v-model="asnFormModel.facilityCode.value"
          v-model:valid="asnFormModel.facilityCode.valid"
          label="Facility"
          :required="true"
          :options="facilitiesList"
          :teleport="false"
          :readonly="defaultFacility !== 'ALL'"
        />
        <DatePicker
          v-model="asnFormModel.expectedDate.value"
          v-model:valid="asnFormModel.expectedDate.valid"
          label="Expected Date"
          :required="true"
          size="l"
          :limit-from="new Date().toISOString().split('T')[0]"
        />
        <Select
          v-model="asnFormModel.carrier.value"
          v-model:valid="asnFormModel.carrier.valid"
          label="Carrier"
          :required="true"
          :nullable="false"
          :options="asnCarriersList"
          :teleport="false"
        />
        <Input
          v-model:valid="asnFormModel.clientReference.valid"
          v-model="asnFormModel.clientReference.value"
          label="Client Reference"
          :required="false"
        />
        <Input
          v-model="asnFormModel.clientPo.value"
          v-model:valid="asnFormModel.clientPo.valid"
          label="Client PO Number"
          :required="false"
        />
        <Input
          v-model="asnFormModel.cartonCount.value"
          v-model:valid="asnFormModel.cartonCount.valid"
          label="Carton/Package Count"
          type="strict-number"
          :min="0"
          :required="false"
        />
        <Input
          v-model="asnFormModel.palletCount.value"
          v-model:valid="asnFormModel.palletCount.valid"
          label="Pallet Count"
          type="strict-number"
          :min="0"
          :required="false"
        />
        <Textbox
          v-model="asnFormModel.notes.value"
          v-model:valid="asnFormModel.notes.valid"
          label="Instruction Notes"
          class="col-span-2"
          :required="false"
        />
        <div class="p-4 md:p-0 flex flex-col gap-y-2">
          <p class="text-xs text-core uppercase">
            Receiving options
          </p>

          <Checkbox
            v-model="asnFormModel.isAutoRelease.value"
            v-model:valid="asnFormModel.isAutoRelease.valid"
            class="col-span-2"
          >

            <span>Auto Release</span>

            <template #suffix>
              <Icon
                v-tooltip="{ content: 'Check the Auto Release to make your inventory immediately available for orders as it is received and counted. Uncheck the box above if you want items in this shipment to not be added to available inventory until after review by you or your Account Manager following receipt.' }"
                name="info"
                size="m"
                class="text-main  ml-2"
              />
            </template>

          </Checkbox>

        </div>
      </form>
      <div class="w-full h-12 sticky top-0 z-1 flex bg-core-20 border-t border-core-30">

        <Button
          data-button="save-changes"
          size="full"
          class="w-full font-medium"
          :disabled="!canCreate"
          :pending="pending"
          @click="submitAsn"
        >
          Save Changes
        </Button>

      </div>
    </div>

  </div>

</template>
