<script lang="ts" setup>

import { Guard } from '@/plugins/guard'
import { confirm } from '@lib/store/confirm'
import { receiveParams } from '@/modules/asn/routes'
import { useRoute, useRouter } from 'vue-router'
import { setNotificationOptions } from '@lib/store/snackbar'
import { computed, reactive, ref } from 'vue'
import { cacheDetails, handleCachedDetails } from '@/store'
import { sanitizeQueryParams, saveFile, viewSetup } from '@lib/scripts/utils'
import { asn, asnReceivesData, cloneAsn, deactivateAsn, exportAsn, getAsn, isCached, resetAsnStore, updateAsnDetails } from '@/modules/asn/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Modal from '@lib/components/blocks/Modal.vue'
import Dialog from '@lib/components/blocks/Dialog.vue'
import Loader from '@lib/components/blocks/Loader.vue'
import Button from '@lib/components/buttons/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import AsnInfo from '@/modules/asn/views/AsnDetails/components/AsnInfo.vue'
import Receives from '@/modules/asn/views/AsnDetails/components/Receives.vue'
import AsnItems from '@/modules/asn/views/AsnDetails/components/AsnItems.vue'
import DatePicker from '@lib/components/inputs/DatePicker.vue'

import type { IconName } from '@lib/store/icon'
import type { ReceiveParams } from '@/modules/asn/types'

interface AsnView {
  id:        number
  name:      string
  icon:      IconName
  disabled?: boolean
}

const route               = useRoute()
const asnId               = computed<number>(() => route.params?.asnId ? Number.parseInt( route.params.asnId as string ) : null )
const params              = reactive<ReceiveParams>({ ...receiveParams, ...sanitizeQueryParams( route?.query ) })
const router              = useRouter()
const canEdit             = ref<boolean>( false )
const isPartial           = ref<boolean> ( false )
const activeView          = ref<number>( 1 )
const storedView          = ref<number>( 1 )
const asnCloneDate        = ref<Date>( null )
const exportPending       = ref<boolean>( false )
const openMobileActions   = ref<boolean>( false )
const cloneAsnModalOpen   = ref<boolean>( false )
const asnCloneDateValid   = ref<boolean>( false )
const openDesktopActions  = ref<boolean>( false )

async function exportAsnExcel( asnId: number ) {

  exportPending.value = true

  const { error, payload } = await exportAsn( asnId )

  if ( !error )
    saveFile( payload, `Asn_${asnId}.xlsx` )

  exportPending.value = false

}

async function getAsnDetails() {

  const { error, payload, status } = await getAsn( asnId.value, false )

  const cachedAsn = handleCachedDetails( status, 'asn', asnId.value )

  asn.value = cachedAsn?.data ?? payload ?? null
  canEdit.value = asn?.value?.asnStatus === 'Pending'
  isCached.value = !!cachedAsn.data
  isPartial.value = asn.value?.asnStatus === 'Partial Receipt'

  return { error, payload, status: cachedAsn.status }

}

const editAsnViews = computed<AsnView[]>(() => [
  {
    id:   1,
    name: 'Details',
    icon: 'details'
  },
  {
    id:   2,
    name: 'Products',
    icon: 'product',
  },
  {
    id:       3,
    name:     'Receives',
    icon:     'received',
    disabled: !asnReceivesData.value?.asnReceives?.length
  }
] )

function globalActionsOptions( ): DropListOption[] {
  return [
    {
      id:     1,
      name:   'Cancel ASN',
      hidden: !canEdit.value,
      color:  'error',
      icon:   {
        name: 'close',
        size: 's'
      },
      action: () => confirm({
        header:      'Cancel ASN',
        description: 'Are you sure you want to cancel this ASN?',
        action:      () => deactivateAsn( asnId.value ).then(( ) => {
          setNotificationOptions({ message: 'ASN is deactivated successfully.' })
          router.push({
            name: 'Asns'
          })
        }),
      }),
    },
    {
      id:     2,
      name:   'Mark ASN as Received',
      hidden: !isPartial.value,
      color:  'error',
      icon:   {
        name: 'close',
        size: 's'
      },
      action: () => confirm({
        header:      'Mark ASN as Received',
        description: 'Are you sure you want to close and mark this partial ASN as received?',
        action:      async () => {

          asn.value.asnStatus = 'Received'

          await updateAsnDetails( asnId.value, asn.value ).then(() => {
            setNotificationOptions({ message: 'ASN is marked as received.' })
            openMobileActions.value = false
            openDesktopActions.value = false
            isPartial.value = false
            canEdit.value = false
          })

        }
      }),

    },
  ]
}

function closeAsnEditDrawer() {
  resetAsnStore()
  router.push({ name: 'Asns' })
}

const { pending } = viewSetup(
  null,
  null,
  router,
  getAsnDetails,
  asnId
)

defineExpose({
  asn,
  asnId,
  route,
  getAsn,
  activeView,
  openDesktopActions,
  openMobileActions,
  canEdit,
  isPartial,
  getAsnDetails,
  globalActionsOptions
})

</script>

<template>

  <div class="w-full h-full grid grid-rows-[max-content_1fr_max-content] md:grid-rows-[max-content_1fr] bg-core-30 overflow-hidden">

    <!-- ASN Details Header -->

    <div class="w-full h-[5.5rem] md:h-12 grid grid-rows-[max-content_max-content] md:grid-rows-1 grid-cols-[1fr_max-content] md:grid-cols-[max-content_1fr_max-content] bg-core-120">

      <!-- ASN Id -->

      <div class="h-12 px-4 flex items-center border-b md:border-r border-core-100">

        <div class="flex gap-2 font-medium">

          <span
            v-if="asn?.hasBlind"
            class="text-warning bg-warning/30 rounded-[0.063rem] flex items-center gap-1 px-[0.375rem]"
          >

            <Icon name="issue-circle" size="s" />
            <span>Blind ASN:</span>

          </span>

          <span v-else class="text-core-10">ASN:</span>
          <span class="text-main-50">[{{ asnId }}]</span>

        </div>

      </div>

      <!-- Other Header Options -->

      <div class="h-10 md:h-12 col-span-2 md:col-span-1 row-start-2 md:row-start-auto flex justify-end">

        <div class="h-full flex items-center px-4 md:border-r border-core-100">
          <p v-if="pending" class="text-core-10">
            Loading ASN Details ...
          </p>
          <p
            v-else
            class="flex items-center font-medium"
            :class="{
              'text-data2-120': asn?.asnStatus === 'Received',
              'text-error': asn?.asnStatus === 'Canceled',
              'text-warning': asn?.asnStatus === 'Pending',
              'text-data1-120': asn?.asnStatus === 'Partial Receipt',
            }"
          >
            <span>
              <span class="text-core-10 hidden md:inline">Status: </span>
              <span>{{ `[${asn?.asnStatus}]` }}</span>
            </span>
          </p>

        </div>

        <div class="grow" />

        <Guard scope="Asn.Write">

          <div class="h-full hidden md:block md:border-l border-core-100">

            <Button
              mode="naked"
              size="auto"
              class="h-full px-4 flex items-center space-x-3 text-core-10"
              @click="cloneAsnModalOpen = true"
            >
              <Icon name="clone" size="m" />
              <p>Clone ASN</p>
            </Button>

          </div>

        </Guard>

        <div class="h-full hidden md:block md:border-l border-core-100">

          <Button
            mode="naked"
            size="auto"
            class="h-full px-4 flex items-center space-x-3 text-core-10"
            :pending="exportPending"
            @click="() => exportAsnExcel(asnId)"
          >
            <Icon name="upload" size="m" />
            <p>Export Excel</p>
          </Button>

        </div>

        <div class="w-10 h-full md:hidden md:border-l border-core-100">

          <Button
            mode="naked"
            type="box"
            size="auto"
            class="w-full h-full text-core-10"
            @click="openMobileActions = true"
          >
            <span class="text-core-10 flex items-center justify-center">
              <Icon name="dots-vertical" size="m" />
            </span>
          </Button>

        </div>

      </div>

      <!-- Close Button -->

      <div class="h-12 border-b md:border-l border-core-100 text-core-10">

        <Button
          mode="naked"
          type="box"
          size="auto"
          class="w-12 h-full text-core-10"
          @click="closeAsnEditDrawer"
        >
          <span
            class="text-core-10"
          >
            <Icon name="close" size="m" />
          </span>
        </Button>

      </div>

    </div>

    <!-- ASN Details :: Desktop -->

    <div v-if="pending" class="w-full h-full hidden md:flex justify-center space-x-4 p-10">
      <Loader />
    </div>

    <div v-else class="w-full h-full hidden md:grid grid-cols-[1fr_max-content] overflow-hidden grid-rows-[minmax(max-content_1fr)_minmax(max-content_1fr)]">

      <div class="w-full h-full grid content-start gap-6 overflow-hidden px-4 pt-6">

        <Receives :asn-id="asnId" @active-receive-id="(id) => params.receiveId = id.toString()" />

        <AsnItems v-model:view-params="params" :asn-id="asnId" />

      </div>

      <div
        class="h-full grid grid-rows-[1fr] overflow-hidden transition-[width]"
        :class="{
          'w-0': pending,
          'w-[26rem]': !pending,
        }"
      >

        <AsnInfo v-if="asn" v-model:open-desktop-actions="openDesktopActions" :can-edit="canEdit" :is-partial="isPartial" />

      </div>

    </div>

    <div v-if="pending" class="w-full h-full flex md:hidden justify-center space-x-4 p-10">
      <Loader />
    </div>

    <!-- ASN Details :: Mobile -->

    <div v-else class="w-full h-full md:hidden grid grid-rows-1 overflow-hidden">

      <Transition :name="activeView > storedView ? 'mobile-view-right' : 'mobile-view-left'" @enter="() => storedView = activeView">

        <Receives
          v-if="activeView === 3"
          v-model:active-view="activeView"
          :asn-id="asnId"
          @active-receive-id="(id) => params.receiveId = id.toString()"
        />

        <AsnItems
          v-else-if="activeView === 2"
          v-model:view-params="params"
          :asn-id="asnId"
        />

        <AsnInfo
          v-else-if="activeView === 1"
          :can-edit="canEdit"
        />

      </Transition>

    </div>

    <div class="w-full h-12 z-1 md:hidden grid grid-cols-3 bg-core-10 border-t border-core-30">

      <Button
        v-for="view in editAsnViews"
        :key="view.id"
        :icon="{
          name: view.icon,
          size: 'm',
        }"
        mode="naked"
        type="box"
        size="auto"
        class="w-full h-full"
        :class="{ 'text-main bg-core-20': activeView === view.id }"
        :disabled="view?.disabled"
        @click="activeView = view.id"
      />

    </div>

    <!-- ASN Mobile Actions -->

    <Sidebar
      name="mobile-actions"
      :dim="true"
      :open="openMobileActions"
      :strict="true"
      position="bottom"
      custom-class="md:hidden"
      @close="openMobileActions = false"
    >

      <div class="w-full bg-core-10">

        <div class="w-full h-12 pl-4 sticky top-0 z-1 flex items-center bg-core-20 border-b border-core-30">

          <p class="text-sm font-medium grow">
            Available Options
          </p>

          <Button
            mode="naked"
            type="box"
            size="auto"
            class="w-12 h-full min-w-[3rem]"
            :icon="{
              name: 'close',
              size: 'm',
            }"
            @click="openMobileActions = false"
          />

        </div>

        <div
          class="grid gap-2 p-4"
        >

          <Guard scope="Asn.Write">

            <Button
              v-for="option in globalActionsOptions().filter(option => !option.hidden)"
              :key="option.id"
              type="pill"
              :mode="option?.color ?? 'primary'"
              class="px-4 flex items-center justify-between"
              @click="option.action"
            >

              <p>{{ option.name }}</p>
              <Icon name="chevron-right" />

            </Button>

            <div v-if="canEdit || isPartial" class="py-2">
              <hr class="border-core-30">
            </div>

            <Button
              type="pill"
              mode="secondary"
              class="px-4 flex items-center justify-between"
              @click="cloneAsnModalOpen = true"
            >

              <p>Clone ASN</p>
              <Icon name="clone" size="m" />

            </Button>

          </Guard>

          <Button
            type="pill"
            mode="secondary"
            class="px-4 flex items-center justify-between"
            :pending="exportPending"
            @click="() => exportAsnExcel(asnId)"
          >

            <p>Export Excel</p>
            <Icon name="upload" size="m" />

          </Button>

        </div>

      </div>

    </Sidebar>

    <!-- ASN Desktop Actions -->

    <Sidebar
      name="desktop-actions"
      :dim="true"
      :open="openDesktopActions"
      :strict="true"
      position="right"
      custom-class="hidden md:block"
      @close="openDesktopActions = false"
    >

      <div class="w-full min-w-[26rem] h-full bg-core-10">

        <div class="w-full h-12 pl-4 sticky top-0 z-1 flex items-center bg-core-20 border-b border-core-30">
          <p class="text-sm font-medium grow">
            Available Options
          </p>

          <Button
            mode="naked"
            type="box"
            size="l"
            class="h-full w-16"
            :icon="{
              name: 'close',
              size: 'm',
            }"
            @click="openDesktopActions = false"
          />

        </div>

        <div class="p-4 grid gap-2">

          <Button
            v-for="option in globalActionsOptions().filter(option => !option.hidden)"
            :key="option.id"
            data-button-desktop="actions"
            type="pill"
            :mode="option?.color ?? 'primary'"
            class="px-4 flex items-center justify-between"
            @click="option.action"
          >

            <p>{{ option.name }}</p>
            <Icon name="chevron-right" />

          </Button>

        </div>

      </div>

    </Sidebar>

    <Modal
      v-if="cloneAsnModalOpen === true"
      :strict="false"
    >

      <Dialog
        mode="naked"
        class="absolute bottom-0 sm:top-1/2 left-1/2 -translate-x-1/2 sm:-translate-y-1/2 bg-core-20"
        cancel-cta-text="Cancel"
        confirm-cta-text="Confirm"
        :disabled="!asnCloneDateValid"
        :action="async () =>

          cloneAsn(asnId, { isBlind: false, createdBy: asn.createdBy, expectedDate: asnCloneDate })
            .then((data) => {

              cacheDetails('asn', data.payload)

              openMobileActions = false
              openDesktopActions = false

              router.push({
                name: 'Asn Details',
                params: { asnId: data.payload.id },
              })

            })
        "
        @close="cloneAsnModalOpen = false"
      >

        <p class="text-lg pb-[0.875rem]">
          Clone ASN
        </p>

        <p class="pb-[0.875rem]">
          Choose an expected date to clone this ASN.
        </p>

        <DatePicker
          v-model="asnCloneDate"
          v-model:valid="asnCloneDateValid"
          label="Expected Date"
          :required="true"
          size="l"
          :limit-from="new Date(new Date().setDate(new Date().getDate() + 1)).toISOString().split('T')[0]"
        />

      </Dialog>

    </Modal>

  </div>

</template>
