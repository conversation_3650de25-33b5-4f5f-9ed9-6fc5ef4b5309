<script  setup lang="ts">

import { Guard } from '@/plugins/guard'
import { asnParams } from '@/modules/asn/routes'
import { useRoute, useRouter } from 'vue-router'
import { computed, reactive, ref, watch } from 'vue'
import { defaultFacility, facilitiesList } from '@/store'
import { AsnStatus, asnStatusList, bulkExportAsns, getAsns, getAsnWidgets } from '@/modules/asn/store'
import { compareObjects, formatDate, removeEmptyKeysFromObject, sanitizeQueryParams, saveFile, viewSetup } from '@lib/scripts/utils'

import Icon from '@lib/components/blocks/Icon.vue'
import Table from '@lib/components/table/Table.vue'
import Input from '@lib/components/inputs/Input.vue'
import Select from '@lib/components/inputs/Select.vue'
import Button from '@lib/components/buttons/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import Textbox from '@lib/components/inputs/Textbox.vue'
import DatePicker from '@lib/components/inputs/DatePicker.vue'
import CounterWidget from '@/components/CounterWidget.vue'
import SearchFilterBadge from '@/components/SearchFilterBadge.vue'

import type { BatchOption } from '@lib/types/tableTypes'
import type { SearchFilter } from '@/types'
import type { Asn, AsnParams, AsnWidgets } from '@/modules/asn/types'

const asns            = ref<PaginatedResponse<'asnList', Asn[]>>( )
const total           = ref<number>( 0 )
const route           = useRoute()
const router          = useRouter()
const params          = reactive<AsnParams>({ ...asnParams, ...sanitizeQueryParams( route.name === 'Asns' ? route.query : {}) })
const pending         = ref<boolean>( false )
const maxPages        = ref<number>( 0 )
const openFilters     = ref( false )
const hasFacility     = ref<boolean>( defaultFacility.value !== 'ALL' )
const asnWidgetsData  = ref<AsnWidgets>( null )

function schema( ): TableSchema<Asn> {
  return [
    {
      key:     'id',
      label:   'OWD Reference',
      sortKey: 'id',
    },
    {
      key:     'asnStatus',
      label:   'Status',
      sortKey: 'status',
    },
    {
      key:     'clientReference',
      label:   'Client Reference',
      sortKey: 'clientReference',
    },
    {
      key:     'clientPo',
      label:   'Client PO Number',
      sortKey: 'clientPurchaseOrder',
    },
    {
      key:     'shipperName',
      label:   'Shipper Name',
      sortKey: 'shipperName',
    },
    {
      key:     'expectedDate',
      label:   'Expected Date',
      format:  'date',
      sortKey: 'expectedDate',
    },
    {
      key:     'stockRelease',
      label:   'Stock Release',
      sortKey: 'stockRelease',
    },
    {
      key:     'lastReceivedDate',
      label:   'Last Received',
      format:  'date',
      sortKey: 'lastReceivedDate',
    },
    {
      key:     'pendingReceiveCount',
      label:   'Pending Receipts',
      sortKey: 'pendingReceiveCount',
    },
    {
      key:     'skuCount',
      label:   'SKUs',
      sortKey: 'skuCount',
    }
  ]
}

const searchAsnsDefaultModel: AsnParams = {
  id:              null,
  sku:             null,
  notes:           null,
  sortBy:          null,
  clientPo:        null,
  itemTitle:       null,
  asnStatus:       null,
  shipperName:     null,
  facilityCode:    null,
  expectedDate:    null,
  sortDirection:   null,
  clientReference: null,
}

const searchAsnsModel = reactive({ ...searchAsnsDefaultModel, ...sanitizeQueryParams( route.name === 'Asns' ? route.query : {}) })

function generateFilters( selectedFilters: Partial<AsnParams> ): SearchFilter<AsnParams>[] {
  return [
    {
      key:   'expectedDate',
      label: 'Expected Date',
      value: selectedFilters?.expectedDate ? formatDate( selectedFilters?.expectedDate, 'MMM, DD YYYY' ) : null
    },
    {
      key:   'facilityCode',
      label: 'Facility',
      value: facilitiesList.value?.find( item => item.id === selectedFilters?.facilityCode )?.name ?? null
    },
    {
      key:   'sku',
      label: 'Product SKU in ASN',
      value: selectedFilters?.sku ?? null
    },
    {
      key:   'asnStatus',
      label: 'Status',
      value: asnStatusList.find( item => item.id === selectedFilters.asnStatus )?.name ?? null
    },
    {
      key:   'itemTitle',
      label: 'Product Title in ASN',
      value: selectedFilters?.itemTitle ?? null
    },
    {
      key:   'shipperName',
      label: 'Shipper Name',
      value: selectedFilters?.shipperName ?? null
    },
    {
      key:   'clientReference',
      label: 'Client Reference',
      value: selectedFilters?.clientReference ?? null
    },
    {
      key:   'clientPo',
      label: 'Client PO Number',
      value: selectedFilters?.clientPo ?? null
    },
    {
      key:   'id',
      label: 'OWD Reference',
      value: selectedFilters?.id ?? null
    },
    {
      key:   'notes',
      label: 'Notes',
      value: selectedFilters?.notes ?? null
    }
  ]
}

const searchAsnsFilters = computed(() => generateFilters( removeEmptyKeysFromObject( params )))
const hasFiltersApplied = computed(() => searchAsnsFilters.value.some( filter => !!filter.value ))

function searchAsns() {
  Object.keys( searchAsnsModel ).forEach(( key ) => {
    params[key] = searchAsnsModel[key]
  })

  openFilters.value = false

}

function filterByStatus( status: number ) {
  searchAsnsModel.asnStatus = status
  Object.keys( searchAsnsModel ).forEach(( key ) => {
    params[key] = searchAsnsModel[key]
  })
}

function resetFilters() {

  Object.keys( searchAsnsDefaultModel ).forEach(( key ) => {
    params[key] = searchAsnsDefaultModel[key]
    searchAsnsModel[key] = searchAsnsDefaultModel[key]
  })

}

function resetSearchModel() {
  Object.keys( searchAsnsDefaultModel ).forEach(( key ) => {
    searchAsnsModel[key] = searchAsnsDefaultModel[key]
  })
}

function removeFilter( key: string ) {
  params[key] = null
  searchAsnsModel[key] = null
}

// Watch for changes in the URL query params
// If the URL params are changed but the state params are not,
// update the filter models to match the URL params.

watch( route, ( n ) => {

  const URLParams   = sanitizeQueryParams( n.query )
  const cleanParams = removeEmptyKeysFromObject( params )

  if ( !compareObjects( URLParams, cleanParams )) {

    // Add new params to the models

    for ( const key in URLParams ) {

      if ( searchAsnsModel.hasOwnProperty( key ))
        searchAsnsModel[key] = URLParams[key]

    }

    // Remove non existing params from the models

    for ( const key in searchAsnsModel ) {

      if ( !URLParams.hasOwnProperty( key ))
        searchAsnsModel[key] = null

    }

  }

})

function asnRowOptions( record: Asn ): DropListOption[] {
  return [
    {
      id:   1,
      name: 'Details',
      icon: {
        name: 'edit',
        size: 'm'
      },
      action: () => {
        router.push({ name: 'Asn Details', params: { asnId: record.id } })
      }
    }
  ]
}

const asnsBatchOptions: BatchOption<Asn>[] = [
  {
    id:     1,
    icon:   'export',
    type:   'neutral',
    group:  'Export',
    action: async ( selected ) => {

      const { payload, error } = await bulkExportAsns( selected.map( item => item.id ))

      if ( !error ) {

        const fileName = `asn_export_${formatDate( new Date(), 'YYYY-MM-DD' )}`

        saveFile(
          payload,
          fileName,
          { type: payload.type }
        )

      }

    },
    actionName: 'Export Excel'
  }
]

async function getAsnWidgetsData() {

  const response = await getAsnWidgets()

  asnWidgetsData.value = response.payload

}

async function getAsnList( viewParams: AsnParams ) {

  pending.value = true

  const { payload } = await getAsns( viewParams )

  asns.value = payload ?? null
  total.value = payload?.totalRows ?? 0
  maxPages.value = payload?.totalPages ?? 0

  pending.value = false

}

function openCreateAsn() {
  router.push({ name: 'Create Asn' })
}

function closeCreateAsn() {
  blockPageUpdate()
  router.push({ name: 'Asns' })
}

const { blockPageUpdate } = viewSetup(
  'Asns',
  params,
  router,
  [
    { callback: getAsnList },
    { callback: getAsnWidgetsData, ignoreParams: 'all' }
  ]
)

</script>

<template>

  <div class="h-full grid grid-rows-[max-content_1fr] overflow-hidden">

    <div class="w-full pl-4 h-9 flex items-center border-b border-core-30 bg-core-20">

      <h3 class="text-sm font-medium">
        Your ASNs
      </h3>

      <div class="grow" />

      <Guard scope="Asn.Write">

        <div class="h-full border-l border-core-30">

          <Button
            size="auto"
            mode="naked"
            class="text-main hover:text-main h-full px-4 flex items-center space-x-2"
            @click="openCreateAsn()"
          >

            <p class="text-sm font-medium">
              New ASN
            </p>

            <Icon name="add" size="s" />

          </Button>

        </div>

      </Guard>

    </div>

    <div class="grid grid-rows-[max-content_1fr] overflow-hidden md:page-background-gradient">

      <div class="w-full p-2 sm:p-4 md:px-[1.625rem] grid grid-cols-4 gap-2 xl:gap-4 sm:grid-cols-2 xl:grid-cols-4 bg-core-10 md:bg-transparent">

        <CounterWidget :disabled="pending" class="bg-core-10" title="Pending" :count="asnWidgetsData?.pending" icon="pending" :action="() => filterByStatus(AsnStatus.PENDING)" color="data1-120" />
        <CounterWidget :disabled="pending" class="bg-core-10" title="Received" :count="asnWidgetsData?.received" icon="received" :action="() => filterByStatus(AsnStatus.RECEIVED)" color="success" />
        <CounterWidget :disabled="pending" class="bg-core-10" title="Partial Receipt" :count="asnWidgetsData?.partialReceipt" icon="in-progress" :action="() => filterByStatus(AsnStatus.PARTIAL_RECEIPT)" color="main" />
        <CounterWidget :disabled="pending" class="bg-core-10" title="Canceled" :count="asnWidgetsData?.cancelled" icon="canceled" :action="() => filterByStatus(AsnStatus.CANCELED)" color="error" />

      </div>

      <div class="h-full grid md:px-[1.625rem] md:pb-0 overflow-hidden">

        <Table
          v-model:params="params"
          name="ASNs"
          class="md:shadow-custom md:border md:border-core-30"
          :flex="true"
          :schema="schema"
          :records="asns?.asnList || []"
          :pending="pending"
          record-map-key="id"
          :selectable="true"
          resource-name="ASN"
          :batch-options="asnsBatchOptions"
          :record-options="asnRowOptions"
          :enable-column-chooser="true"
          :pagination="{
            total,
            maxPages,
          }"
        >

          <template #table-head>

            <div class="flex items-center">

              <div class="border-r border-core-30">

                <Button
                  mode="naked"
                  size="m"
                  class="px-3 lg:px-4 flex items-center lg:space-x-2"
                  :is-active="openFilters"
                  @click="openFilters = !openFilters"
                >
                  <Icon size="m" name="search" />
                  <p class="hidden lg:block text-sm font-medium">
                    {{ $t('global.label.search', { name: 'ASNs' }) }}
                  </p>
                </Button>

              </div>

              <div v-if="hasFiltersApplied" class="border-r border-core-30">

                <Button
                  size="m"
                  type="box"
                  mode="naked"
                  :icon="{
                    name: 'reset',
                    size: 'm',
                  }"
                  @click="resetFilters"
                />

              </div>

            </div>

          </template>

          <template #table-neck>

            <div v-if="hasFiltersApplied" class="py-1 px-2 flex flex-wrap items-center border-b border-core-30">

              <SearchFilterBadge
                v-for="filter in searchAsnsFilters"
                v-show="filter.value"
                :key="filter.key"
                :filter="filter"
                @remove-filter="removeFilter"
              />

            </div>

          </template>

        </Table>

      </div>

    </div>

    <Sidebar
      :open="openFilters"
      :strict="false"
      :dim="true"
      @close="openFilters = false"
    >
      <div class="w-full h-full flex flex-col">

        <!-- Search Header -->
        <div class="w-full h-12 sticky top-0 z-1 flex shrink-0 items-center bg-core-20 border-b border-core-30">
          <div class="h-full px-4 flex items-center space-x-3 grow border-r border-core-30">
            <p class="text-sm font-medium">
              {{ $t('global.label.search', { name: 'ASNs' }) }}
            </p>
          </div>
          <div class="h-full border-r border-core-30">
            <Button mode="naked" size="auto" class="h-full px-4 flex items-center space-x-2" @click="resetSearchModel">
              <p class="text-sm">
                Reset Filters
              </p>
              <Icon name="reset" size="s" class="text-main" />
            </Button>
          </div>
          <Button
            type="box"
            size="auto"
            mode="naked"
            class="w-12 h-full min-w-[3rem]"
            :icon="{
              name: 'close',
              size: 'm',
            }"
            @click="openFilters = false"
          />
        </div>

        <!-- Search Options -->
        <div class="w-full h-full overflow-hidden overflow-y-auto">
          <form class="w-full md:w-[46rem] md:max-w-[46rem] md:px-6 grid md:gap-4" @submit.prevent>
            <!-- Search By -->
            <section>
              <div class="h-12 w-full sticky top-0 z-1 px-4 md:px-2 flex items-center bg-core-20 border-b md:border-b-0 border-core-30">
                <p class="text-xs text-core uppercase">
                  {{ $t('global.label.search', { name: 'By' }) }}
                </p>
              </div>
              <div class="grid md:grid-cols-2 md:gap-4 md:pb-4">
                <Select
                  v-show="!hasFacility"
                  v-model="searchAsnsModel.facilityCode"
                  :disabled="hasFacility"
                  label="Facility"
                  class="hidden md:block col-span-2"
                  :required="false"
                  :options="facilitiesList"
                  :teleport="false"
                />

                <Select
                  v-show="!hasFacility"
                  v-model="searchAsnsModel.facilityCode"
                  :disabled="hasFacility"
                  label="Facility"
                  class="md:hidden"
                  :required="false"
                  :options="facilitiesList"
                  :teleport="false"
                />

                <Select
                  v-model="searchAsnsModel.asnStatus"
                  label="Status"
                  class="hidden md:block"
                  :required="false"
                  :options="asnStatusList"
                  :teleport="false"
                />

                <Select
                  v-model="searchAsnsModel.asnStatus"
                  label="Status"
                  class="md:hidden"
                  :required="false"
                  :options="asnStatusList"
                  :teleport="false"
                />

                <Input
                  v-model="searchAsnsModel.sku"
                  label="Product SKU in ASN"
                  :required="false"
                />

                <Input
                  v-model="searchAsnsModel.itemTitle"
                  label="Product Title in ASN"
                  :required="false"
                />

                <Input
                  v-model="searchAsnsModel.shipperName"
                  label="Shipper Name"
                  :required="false"
                />

                <Input
                  v-model="searchAsnsModel.clientReference"
                  label="Client Reference"
                  :required="false"
                />

                <Input
                  v-model="searchAsnsModel.clientPo"
                  label="Client PO Number"
                  :required="false"
                />

                <Input
                  v-model="searchAsnsModel.id"
                  label="OWD Reference"
                  :required="false"
                />

                <DatePicker
                  v-model="searchAsnsModel.expectedDate"
                  label="Expected Date"
                  :required="false"
                  return-type="UTC-date-time"
                />

                <Textbox
                  v-model="searchAsnsModel.notes"
                  label="Notes"
                  class="md:col-span-2"
                  :required="false"
                />
              </div>
            </section>
          </form>
        </div>

        <!-- Search Buttons -->
        <div class="shrink-0 w-full h-12 sticky top-0 z-1 grid grid-cols-2 bg-core-20 border-t border-core-30">
          <Button
            size="auto"
            mode="naked"
            @click="openFilters = false"
          >
            {{ $t('global.button.cancel') }}
          </Button>
          <Button
            size="auto"
            @click="searchAsns"
          >
            Search
          </Button>
        </div>
      </div>
    </Sidebar>

    <Sidebar
      :open="['Asn Details', 'Asn Items Inventory'].includes(String($route.name))"
      :fit-content="false"
    >

      <div class="w-full h-full">
        <RouterView />
      </div>

    </Sidebar>

    <Sidebar
      :open="['Create Asn', 'Create Asn Inventory'].includes(String($route.name))"
      :strict="true"
      :fit-content="false"
      @close="closeCreateAsn"
    >

      <div class="w-full h-full">
        <RouterView @close="closeCreateAsn" />
      </div>

    </Sidebar>

  </div>

</template>
