<script setup lang="ts">

import { Flow } from '@/modules/asn/views/CreateAsn/types'
import { confirm } from '@lib/store/confirm'
import { computed } from 'vue'
import { blockPageUpdate } from '@lib/scripts/utils'

import {
  activeStep,
  asnGeneralDetailsStepValid,
  asnProductsListStepValid,
  createAsnInProgress,
  resetAsnCreateStore
} from '@/modules/asn/views/CreateAsn/store'

import Button from '@lib/components/buttons/Button.vue'
import FlowBar from '@lib/components/utils/FlowBar.vue'
import AsnSummary from '@/modules/asn/views/CreateAsn/components/AsnSummary.vue'
import AsnProducts from '@/modules/asn/views/CreateAsn/components/AsnProducts.vue'
import GeneralDetails from '@/modules/asn/views/CreateAsn/components/GeneralDetails.vue'

import type { FlowStep } from '@lib/types/flowBarTypes'

const steps = computed<FlowStep<Flow>[]>(() => [
  {
    id:            Flow.GENERAL,
    isGroup:       true,
    stepName:      'General Details',
    groupName:     'General Details',
    inProgress:    activeStep.value === Flow.GENERAL,
    stepComplete:  asnGeneralDetailsStepValid.value,
    groupComplete: asnGeneralDetailsStepValid.value,
  },
  {
    id:            Flow.PRODUCTS,
    isGroup:       true,
    disabled:      !asnGeneralDetailsStepValid.value,
    stepName:      'ASN Products',
    groupName:     'ASN Products',
    inProgress:    activeStep.value === Flow.PRODUCTS,
    stepComplete:  asnGeneralDetailsStepValid.value && asnProductsListStepValid.value,
    groupComplete: asnGeneralDetailsStepValid.value && asnProductsListStepValid.value,
  },
  {
    id:            Flow.SUMMARY,
    isGroup:       true,
    disabled:      !asnGeneralDetailsStepValid.value || !asnProductsListStepValid.value,
    stepName:      'Summary',
    groupName:     'Summary',
    inProgress:    activeStep.value === Flow.SUMMARY,
    stepComplete:  asnGeneralDetailsStepValid.value && asnProductsListStepValid.value && activeStep.value === Flow.SUMMARY,
    groupComplete: asnGeneralDetailsStepValid.value && asnProductsListStepValid.value && activeStep.value === Flow.SUMMARY,
  }
] )

</script>

<template>

  <div
    class="w-full h-full grid grid-rows-[max-content_1fr] overflow-hidden bg-core-30"
    :class="{
      'pointer-events-none': createAsnInProgress,
    }"
  >

    <div class="w-full h-12 grid grid-cols-[max-content_1fr_max-content] place-items-center bg-core-120">

      <div class="w-[3rem] md:w-[8rem] h-full pl-4 md:px-4 flex items-center">
        <p class="text-core-10 font-medium hidden md:inline-block">
          Create ASN
        </p>
      </div>

      <FlowBar
        :steps="steps"
        @set-active-step="step => activeStep = step.id"
      />

      <div class="w-[3rem] md:w-[8rem] h-full flex justify-end">

        <Button
          type="box"
          mode="naked"
          size="auto"
          class="w-12 h-full text-core-20 hover:text-core-20"
          :icon="{ name: 'close', size: 'm' }"
          @click="() => confirm({
            header: 'Are you sure you want to exit ASN creation?',
            description: 'Exiting now will result in the loss of all entered data.',
            action: () => {
              blockPageUpdate()
              $router.push('/asn')
              resetAsnCreateStore()
            },
          })"
        />

      </div>

    </div>

    <Transition name="route" mode="out-in">

      <GeneralDetails v-if="activeStep === Flow.GENERAL" />
      <AsnProducts v-else-if="activeStep === Flow.PRODUCTS" />
      <AsnSummary v-else-if="activeStep === Flow.SUMMARY" />

    </Transition>

  </div>

</template>
