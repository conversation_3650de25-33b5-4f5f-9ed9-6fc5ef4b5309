<script setup lang="ts">

import { Flow } from '@/modules/asn/views/CreateAsn/types'
import { useRouter } from 'vue-router'
import { setNotificationOptions } from '@lib/store/snackbar'
import { cacheDetails, facilitiesList } from '@/store'
import { createAsn, createAsnItem, isRpc } from '@/modules/asn/store'
import { checkValue, convertObjectToPlain, formatDate } from '@lib/scripts/utils'
import { activeStep, asnGeneralInfo, asnItems, createAsnInProgress, getTotalUnits, resetAsnCreateStore } from '@/modules/asn/views/CreateAsn/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'
import AsnItemsTable from '@/modules/asn/views/CreateAsn/components/AsnItemsTable.vue'

const router = useRouter()

async function submitAsn() {

  createAsnInProgress.value = true

  const asnDataPlain = convertObjectToPlain( asnGeneralInfo.value )

  asnDataPlain.asnStatus = 'Pending'
  asnDataPlain.createdBy = 'portal'
  asnDataPlain.palletCount = checkValue( asnDataPlain.palletCount ) ? asnDataPlain.palletCount : null
  asnDataPlain.cartonCount = checkValue( asnDataPlain.cartonCount ) ? asnDataPlain.cartonCount : null

  const { error, payload } = await createAsn( asnDataPlain )

  let asnId: number

  if ( !error ) {

    asnId = payload.id

    const itemsRequests = []

    for ( const i in asnItems.value ) {

      itemsRequests.push( createAsnItem(
        asnId,
        {
          sku:           asnItems.value[i].sku,
          notes:         asnItems.value[i].notes,
          title:         asnItems.value[i].title || asnItems.value[i].sku,
          description:   asnItems.value[i].description,
          expectedUnits: asnItems.value[i].quantity
        }
      ))

    }

    await Promise.all( itemsRequests )

    setNotificationOptions({ message: 'ASN is created successfully.' })
    cacheDetails( 'asn', payload )
    isRpc.value = true

    await router.push({ name: 'Asn Details', params: { asnId } })
    resetAsnCreateStore()

  }

  createAsnInProgress.value = false

}

function getAsnFacility( code: string ) {

  return facilitiesList.value.find( facility => code === facility.id )?.name

}

defineExpose({
  submitAsn
})

</script>

<template>

  <div class="w-full h-full grid grid-rows-[1fr_max-content] overflow-hidden bg-core-10 md:bg-transparent">

    <div class="w-full h-full lg:p-8 lg:pr-4 lg:pb-0 md:grid md:grid-cols-2 overflow-y-auto flex flex-col">

      <div class="w-full h-full lg:h-fit lg:max-h-full md:grid md:grid-rows-[max-content_1fr] md:overflow-y-hidden lg:shadow-custom border-r border-core-30 lg:border-r-0 md:order-1 order-2">

        <div data-section="products" class="h-10 pl-4 flex justify-between overflow-hidden items-center border-t border-t-core-30 md:border-t-0 bg-core-20">
          <span class="text-sm font-medium flex items-center gap-3">
            <span class="text-main-70"><Icon name="product" /></span>
            <p>ASN Products</p>
          </span>
          <div class="border-l border-l-core-30">
            <Button class="w-10" mode="naked" size="m" @click="activeStep = Flow.PRODUCTS">
              <div class="w-full flex justify-center">
                <span class="text-main-70"><Icon class="color-main-70" name="edit" size="s" /></span>
              </div>
            </Button>
          </div>
        </div>

        <div class="md:overflow-y-hidden md:grid md:grid-rows-[1fr] md:bg-core-20 lg:bg-transparent">

          <AsnItemsTable
            table-name="Asn Items Summary"
            :enable-batch-actions="false"
          />

          <div class="md:hidden grid grid-cols-[1fr_max-content_1fr] place-items-center bg-core-10 h-10 border-t border-b border-core-30">

            <div class="text-sm font-medium">
              <span class="text-core-70">Products:</span> {{ asnItems.length }}
            </div>

            <div class="h-full w-[1px] bg-core-30" />

            <div class="text-sm font-medium">
              <span class="text-core-70">Units:</span> {{ getTotalUnits() }}
            </div>

          </div>

        </div>

      </div>

      <div class="flex flex-col lg:gap-4 md:order-2 order-1 md:overflow-y-auto lg:pl-8 lg:pr-4 md:bg-core-20 lg:bg-transparent">

        <div data-section="client-details" class="w-full h-fit lg:max-h-full grid grid-rows-[1fr] lg:shadow-custom border-r border-core-30 lg:border-r-0">
          <div class="h-10 pl-4 flex justify-between items-center border-y border-y-core-30 md:border-t-0 bg-core-20 sticky md:static top-0 z-1">
            <span class="text-sm font-medium flex items-center gap-3">
              <span class="text-main-70"><Icon name="bill" size="m" /></span>
              <p>Client Details</p>
            </span>
            <div class="border-l border-l-core-30">
              <Button class="w-10" mode="naked" size="m" @click="activeStep = Flow.GENERAL">
                <div class="w-full flex justify-center">
                  <span class="text-main-70"><Icon class="color-main-70" name="edit" size="s" /></span>
                </div>
              </Button>
            </div>
          </div>

          <div class="h-fit w-full bg-core-10 px-4 py-3 md:p-[1.625rem] flex flex-col gap-1 md:inline">
            <div class="w-full flex flex-col md:flex-row">
              <div class="w-40 text-sm text-core-70">
                Client Reference:
              </div><span class="text-main-70">{{ asnGeneralInfo?.clientReference.value || '/' }}</span>
            </div>
            <div class="w-full flex flex-col md:flex-row">
              <div class="w-40 text-sm text-core-70">
                Client PO Number:
              </div><span class="text-main-70">{{ asnGeneralInfo?.clientPo.value || '/' }}</span>
            </div>
            <div class="w-full flex flex-col md:flex-row">
              <div class="w-40 text-sm text-core-70">
                Instruction Notes:
              </div><span class="text-main-70">{{ asnGeneralInfo?.notes.value || '/' }}</span>
            </div>

          </div>
        </div>

        <div data-section="shipping-details" class="w-full h-fit lg:max-h-full grid grid-rows-[1fr] lg:shadow-custom border-r md:border-t lg:border-t-0 border-core-30 lg:border-r-0">
          <div class="h-10 pl-4 flex justify-between items-center border-y border-y-core-30 md:border-t-0 bg-core-20 sticky md:static top-0 z-1">
            <span class="text-sm font-medium flex items-center gap-3">
              <span class="text-main-70"><Icon name="shipped" size="m" /></span>
              <p>Shipping Details</p>
            </span>
            <div class="border-l border-l-core-30">
              <Button class="w-10" mode="naked" size="m" @click="activeStep = Flow.GENERAL">
                <div class="w-full flex justify-center">
                  <span class="text-main-70"><Icon class="color-main-70" name="edit" size="s" /></span>
                </div>
              </Button>
            </div>
          </div>
          <div class="h-fit w-full bg-core-10 px-4 py-3 md:p-[1.625rem] flex flex-col gap-1 md:inline">
            <div class="w-full flex flex-col md:flex-row">
              <div class="w-40 text-sm text-core-70">
                Shipper Name:
              </div><span class="text-main-70">{{ asnGeneralInfo?.shipperName.value || '/' }}</span>
            </div>
            <div class="w-full flex flex-col md:flex-row">
              <div class="w-40 text-sm text-core-70">
                Expected Date:
              </div><span class="text-main-70">{{ asnGeneralInfo?.expectedDate.value ? formatDate(asnGeneralInfo?.expectedDate.value, 'MMM DD, YYYY') : '/' }}</span>
            </div>
            <div class="w-full flex flex-col md:flex-row">
              <div class="w-40 text-sm text-core-70">
                Carrier:
              </div><span class="text-main-70">{{ asnGeneralInfo?.carrier.value || '/' }}</span>
            </div>
            <div class="w-full flex flex-col md:flex-row">
              <div class="w-40 text-sm text-core-70">
                Carton/Package Count:
              </div><span class="text-main-70">{{ checkValue(asnGeneralInfo?.cartonCount.value) ? asnGeneralInfo?.cartonCount.value : '/' }}</span>
            </div>
            <div class="w-full flex flex-col md:flex-row">
              <div class="w-40 text-sm text-core-70">
                Pallet Count:
              </div><span class="text-main-70">{{ checkValue(asnGeneralInfo?.palletCount.value) ? asnGeneralInfo?.palletCount.value : '/' }}</span>
            </div>
            <div class="w-full flex flex-col md:flex-row">
              <div class="w-40 text-sm text-core-70">
                Facility:
              </div><span class="text-main-70">{{ getAsnFacility(asnGeneralInfo?.facilityCode.value) || '/' }}</span>
            </div>
            <div class="w-full flex flex-col md:flex-row">
              <div class="w-40 text-sm text-core-70">
                Auto Release:
              </div><span class="text-main-70">{{ asnGeneralInfo?.isAutoRelease.value ? 'Yes' : 'No' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="w-full bg-core-10 flex justify-end md:border-t md:border-t-core-30 ">
      <div class="hidden md:grid grid-cols-[1fr_max-content_1fr] place-items-center bg-core-10 h-full border-l border-core-30">
        <div class="text-sm font-medium w-[8.25rem] text-center">
          <span class="text-core-70">Products:</span> {{ asnItems.length }}
        </div>
        <div class="h-full w-[1px] bg-core-30" />
        <div class="text-sm font-medium w-[8.25rem] text-center">
          <span class="text-core-70">Units:</span> {{ getTotalUnits() }}
        </div>
      </div>
      <Button
        data-button="submit-asn"
        class="w-full h-full md:px-12 md:w-auto"
        :pending="createAsnInProgress"
        @click="submitAsn"
      >
        Submit ASN
      </Button>
    </div>

  </div>

</template>
