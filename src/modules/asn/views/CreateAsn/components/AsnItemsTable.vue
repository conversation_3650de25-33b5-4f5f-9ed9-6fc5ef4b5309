<script setup lang="ts">

import { searchModel } from '@lib/scripts/utils'
import { computed, ref } from 'vue'
import { asnItems, selectedItems } from '@/modules/asn/views/CreateAsn/store'

import Table from '@lib/components/table/Table.vue'
import TableSearchBar from '@lib/components/utils/TableSearchBar.vue'

import type { BatchOption } from '@lib/types/tableTypes'
import type { EditableAsnItem } from '@/modules/asn/views/CreateAsn/types'

interface AddedItemsTable {
  tableName:           string
  asnItemModel?:       Validatable<EditableAsnItem>
  enableBatchActions?: boolean
}

const props = withDefaults( defineProps<AddedItemsTable>(), { enableBatchActions: true })
const emits = defineEmits<{ openAsnProductEdit: [] }>()

const query         = ref<string>( '' )
const asnItemModel  = defineModel<Validatable<EditableAsnItem>>( 'asnItemModel' )
const searchedItems = computed<EditableAsnItem[]>(() => searchModel( asnItems.value, query.value, [ 'sku' ] ))

function batchRemoveAsnItems( batchItems: EditableAsnItem [] ) {

  batchItems.forEach(( item ) => {
    asnItems.value = asnItems.value.filter( addedItem => addedItem.sku !== item.sku )
  })

}

function asnItemOptions( item: EditableAsnItem ): DropListOption[] {

  if ( !props.enableBatchActions )
    return

  return [
    {
      id:   1,
      name: 'Edit',
      icon: {
        name: 'edit'
      },
      action: ( ) => {
        asnItemModel.value.sku.value = item.sku
        asnItemModel.value.title.value = item.title
        asnItemModel.value.notes.value = item.notes
        asnItemModel.value.description.value = item.description
        asnItemModel.value.quantity.value = item.quantity
        emits( 'openAsnProductEdit' )
      }
    },
    {
      id:   2,
      name: 'Delete',
      icon: {
        name:  'delete',
        color: 'red'
      },
      action: ( ) => {
        asnItems.value = asnItems.value.filter( product => product.sku !== item.sku )
      }
    }
  ]
}

const asnItemsBatchOptions: BatchOption<EditableAsnItem>[] = [
  {
    id:         1,
    icon:       'delete',
    type:       'negative',
    group:      'Bulk Actions',
    action:     batchRemoveAsnItems,
    actionName: 'Delete',
  }
]

function schema( ): TableSchema<EditableAsnItem> {
  return [
    {
      key:   'sku',
      label: 'SKU',
    },
    {
      key:   'title',
      label: 'Title',
    },
    {
      key:   'notes',
      label: 'Notes'
    },
    {
      key:   'description',
      label: 'Description',
    },
    {
      key:   'quantity',
      label: 'Expected Units',
    }
  ]
}

</script>

<template>

  <Table
    v-model:selected-records="selectedItems"
    class="lg:shadow-custom"
    :params="{}"
    :name="tableName"
    :schema="schema"
    :records="searchedItems"
    record-map-key="id"
    :selectable="enableBatchActions"
    :batch-options="asnItemsBatchOptions"
    :record-options="asnItemOptions"
    custom-empty-message="No ASN Products Found."
  >

    <template #table-head>

      <div v-if="enableBatchActions" class="h-10">

        <TableSearchBar
          v-model="query"
          name="Items"
          class="h-10"
          :debounce="0"
          :expanded-search="true"
        />

      </div>

    </template>

  </Table>

</template>
