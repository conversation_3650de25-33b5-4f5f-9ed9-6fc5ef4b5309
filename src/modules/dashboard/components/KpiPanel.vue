<script setup lang="ts">

import { formatDate } from '@lib/scripts/utils'
import { getAsnWidgets } from '@/modules/asn/store'
import { getInventoryWidgets } from '@/modules/inventory/store'
import { onMounted, ref, watch } from 'vue'
import { hardcodeCallCenterData, mapCardData } from '@/modules/orders/store'

import TrendCard from '@/modules/orders/views/summary/components/TrendCard.vue'

import type { OrdersByStatusPayload, StackedAreaChartData } from '@/modules/orders/types'

const props = defineProps<{ ordersByStatus?: OrdersByStatusPayload }>()

const pending = ref( false )

// Fulfillment KPIs
const arrivedAsnsCardData          = ref<StackedAreaChartData>( null )
const todaysOrdersCardData         = ref<StackedAreaChartData>( null )
const outOfStockInventoryCardData  = ref<StackedAreaChartData>( null )

// Call Center KPIs & P2C
const p2cCardData        = ref<StackedAreaChartData>( null )
const chatsCardData      = ref<StackedAreaChartData>( null )
const emailsCardData     = ref<StackedAreaChartData>( null )
const handleTimeCardData = ref<StackedAreaChartData>( null )

async function getSummaryData() {

  pending.value = true

  const [ inventoryWidget, asnWidgets ] = await Promise.all( [

    getInventoryWidgets(),
    getAsnWidgets(),

  ] )

  pending.value = false

  arrivedAsnsCardData.value = [ { group: 'Dataset 1', date: null, value: asnWidgets.payload?.received } ]
  outOfStockInventoryCardData.value = [ { group: 'Dataset 1', date: null, value: inventoryWidget.payload?.outOfStock } ]

}

onMounted( getSummaryData )

watch(() => props.ordersByStatus, () => {

  todaysOrdersCardData.value = mapCardData( props.ordersByStatus?.shipped, 'shippingDate' )

  // Hardcoded Call Center KPIs
  p2cCardData.value = mapCardData( hardcodeCallCenterData.backordered, 'orderDate' )
  chatsCardData.value = mapCardData( hardcodeCallCenterData.backordered, 'orderDate' )
  emailsCardData.value = mapCardData( hardcodeCallCenterData.backordered, 'orderDate' )
  handleTimeCardData.value = mapCardData( hardcodeCallCenterData.backordered, 'orderDate' )

}, { immediate: true, deep: true })

</script>

<template>

  <div class="w-full lg:h-full md:px-8 md:py-4 lg:overflow-y-auto bg-core-10">

    <!-- Fulfillment KPIs -->

    <div class="md:pb-6 md:pt-2">

      <div class="w-full h-10 px-3 md:px-0 md:h-14 flex items-center">

        <p class="text-sm md:text-lg">
          Fulfillment KPIs
        </p>

      </div>

      <div class="md:mt-0.5 grid xl:grid-cols-2 gap-px">

        <TrendCard
          name="Today's Shipped Orders"
          chart-type="Bar"
          :data="todaysOrdersCardData"
          :pending="pending"
          @click="$router.push(`/orders/live?page=1&pageSize=25&isShipped=true&shippedStartDate=${formatDate(new Date(), 'UTC-date-time')}`)"
        />

        <TrendCard
          name="Received ASNs"
          chart-type="Bar"
          unit=""
          :data="arrivedAsnsCardData"
          :pending="pending"
          @click="$router.push('/asn?page=1&pageSize=25&asnStatus=1')"
        />

        <TrendCard
          name="Out Of Stock Products"
          chart-type="Line"
          :data="outOfStockInventoryCardData"
          unit=""
          :pending="pending"
          :is-warning="true"
          @click="$router.push('/inventory/live?page=1&pageSize=25&outOfStock=true')"
        />
      </div>

    </div>

    <!-- P2C KPIs -->

    <div class="md:pb-6 md:pt-2">

      <div class="w-full h-10 px-3 md:px-0 md:h-14 flex items-center">

        <p class="text-sm md:text-lg">
          P2C KPIs
        </p>

      </div>

      <div class="md:mt-0.5 grid xl:grid-cols-2">

        <TrendCard
          class="border-x-0 md:border-x"
          name="Today’s P2C Orders"
          unit="Coming Soon"
          chart-type="Bar"
          :is-coming-soon="true"
          :data="p2cCardData"
        />

      </div>

    </div>

    <!-- Call Center KPIs -->
    <div class="md:pb-6 md:pt-2">

      <div class="w-full h-10 px-3 md:px-0 md:h-14 flex items-center">

        <p class="text-sm md:text-lg">
          Call Center KPIs
        </p>

      </div>

      <div class="md:mt-0.5 grid xl:grid-cols-2">

        <TrendCard
          class="border-x-0 md:border-x"
          name="Today’s Emails"
          unit="Coming Soon"
          chart-type="Bar"
          :is-coming-soon="true"
          :data="emailsCardData"
        />

        <TrendCard
          class="border-t-0 border-x-0 md:border-x xl:border-t xl:border-l-0"
          name="Today’s Handle Time"
          unit="Coming Soon"
          chart-type="Bar"
          :is-coming-soon="true"
          :data="handleTimeCardData"
        />

        <TrendCard
          class="border-t-0 border-x-0 md:border-x"
          name="Today’s Chats"
          unit="Coming Soon"
          chart-type="Bar"
          :is-coming-soon="true"
          :data="chatsCardData"
        />
      </div>

    </div>

  </div>

</template>
