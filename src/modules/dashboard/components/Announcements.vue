<script setup lang="ts">

import { computed, ref } from 'vue'
import { announcementsList, openAnnouncement } from '@/store'

import Button from '@lib/components/buttons/Button.vue'

const index         = ref<number>( 0 )
const announcement  = computed<any>(() => announcementsList.value[dynamicIndex.value] )

const dynamicIndex = computed(() =>
  Math.abs( index.value ) < announcementsList.value?.length
    ? Math.abs( index.value )
    : Math.abs(( announcementsList.value?.length - index.value ) % announcementsList.value?.length ))

</script>

<template>

  <div class="p-4 md:p-6 grid grid-rows-[max-content_1fr] gap-y-4 bg-core-10 rounded-xs overflow-hidden">

    <div class="flex items-center space-x-2">

      <p class="grow text-lg md:text-2xl">
        {{ $t("dashboard.newsAndAnnouncements.header") }}
      </p>

      <Button
        type="box"
        size="xs"
        mode="naked"
        class="text-main hover:text-main"
        :icon="{
          name: 'chevron-left',
          size: 'm',
        }"
        @click="index--"
      />

      <Button
        type="box"
        size="xs"
        mode="naked"
        class="text-main hover:text-main"
        :icon="{
          name: 'chevron-right',
          size: 'm',
        }"
        @click="index++"
      />

    </div>

    <Transition appear mode="out-in" name="source-form">

      <div
        :key="index"
        class="w-full h-full grid grid-rows-[12rem_10rem] md:grid-rows-1 md:grid-cols-[2fr_1fr] gap-4 overflow-hidden"
      >

        <div class="row-start-2 md:row-start-auto grid grid-rows-[max-content_1fr_max-content] place-items-start gap-y-4 overflow-hidden">

          <p class="text-lg text-main font-medium" v-html="announcement?.title?.rendered" />

          <div class="w-full h-full overflow-hidden">
            <p class="text-sm text-core" v-html="announcement?.content?.rendered" />
          </div>

          <Button
            size="xs"
            class="px-4 text-sm"
            @click="openAnnouncement(announcement)"
          >
            {{ $t("dashboard.newsAndAnnouncements.actionButton") }}
          </Button>

        </div>

        <div class="w-full h-full row-start-1 md:row-start-auto overflow-hidden bg-core-30">
          <img class="w-full h-full object-cover object-center" :src="announcement?.yoast_head_json?.og_image?.[0]?.url" alt="Announcement Image">
        </div>

      </div>

    </Transition>

  </div>

</template>
