<script setup lang="ts">

import Alert from '@lib/components/blocks/Alert.vue'
import Loader from '@lib/components/blocks/Loader.vue'
import Confirm from '@lib/components/blocks/Confirm.vue'
import Notification from '@lib/components/blocks/Notification.vue'

</script>

<template>

  <main class="w-full h-full bg-core-20 relative">

    <RouterView v-slot="{ Component }">

      <Transition mode="out-in" name="route">

        <Component :is="Component" />

      </Transition>

    </RouterView>

    <div class="fixed w-full h-full flex items-center justify-center space-x-2">
      <Loader name="" />
    </div>

    <Alert />
    <Confirm />
    <Notification />

  </main>

</template>
