import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'

import InventoryPanel from '@/components/InventoryPanel.vue'
import InventoryProduct from '@/components/InventoryProduct.vue'
import Table from '@lib/components/table/Table.vue'
import Button from '@lib/components/buttons/Button.vue'

import SimpleBadge from '@/components/SimpleBadge.vue'
import Input from '@lib/components/inputs/Input.vue'

vi.mock( '@/modules/inventory/store', async ( importOriginal ) => {
  const actual = await importOriginal() as any

  return {
    ...actual,
    getProducts: vi.fn().mockResolvedValue({
      error:   null,
      status:  200,
      headers: {
        'content-encoding':           'gzip',
        'content-type':               'application/json; charset=utf-8',
        'date':                       'Thu, 16 Jan 2025 14:42:03 GMT',
        'request-context':            'appId=cid-v1:271575a9-4199-40c2-afa4-66b574cb6e2f',
        'transfer-encoding':          'chunked',
        'vary':                       'Accept-Encoding',
        'x-ms-middleware-request-id': '00000000-0000-0000-0000-000000000000'
      },
      payload: {
        items: [
          {
            id:                  814676,
            sku:                 'testbob1',
            title:               'portal review1',
            availableQuantity:   0,
            availableByFacility: [
              {
                facilityName:   'West - Los Angeles, CA',
                facilityCode:   'DC6',
                unitsAvailable: 0,
                lots:           null
              }
            ],
            backOrdered:   0,
            price:         50,
            supplier:      '',
            isActive:      true,
            kitComponents: null
          },
          {
            id:                  814675,
            sku:                 'SKU-0014443',
            title:               'Title',
            availableQuantity:   0,
            availableByFacility: [
              {
                facilityName:   'West - Los Angeles, CA',
                facilityCode:   'DC6',
                unitsAvailable: 0,
                lots:           null
              }
            ],
            backOrdered:   0,
            price:         149.99,
            supplier:      'Supplier 1',
            isActive:      true,
            kitComponents: null
          },
          {
            id:                  814674,
            sku:                 '12',
            title:               '12',
            availableQuantity:   99999,
            availableByFacility: [
              {
                facilityName:   'Midwest - Mobridge, SD',
                facilityCode:   'DC1',
                unitsAvailable: 99999,
                lots:           null
              },
              {
                facilityName:   'West - Los Angeles, CA',
                facilityCode:   'DC6',
                unitsAvailable: 99999,
                lots:           null
              }
            ],
            backOrdered:   0,
            price:         0,
            supplier:      '',
            isActive:      true,
            kitComponents: [
              {
                sku:                 'TestAvailableByFacility2',
                quantity:            1,
                availableByFacility: [
                  {
                    facilityName:   'Midwest - Mobridge, SD',
                    facilityCode:   'DC1',
                    unitsAvailable: 0,
                    lots:           null
                  },
                  {
                    facilityName:   'West - Los Angeles, CA',
                    facilityCode:   'DC6',
                    unitsAvailable: 0,
                    lots:           null
                  },
                  {
                    facilityName:   'East - Wilmington, OH',
                    facilityCode:   'DC7',
                    unitsAvailable: 0,
                    lots:           null
                  }
                ]
              }
            ]
          },
          {
            id:                  814673,
            sku:                 'Test-Timi',
            title:               'Test Timi',
            availableQuantity:   99999,
            availableByFacility: [
              {
                facilityName:   'Midwest - Mobridge, SD',
                facilityCode:   'DC1',
                unitsAvailable: 99999,
                lots:           null
              },
              {
                facilityName:   'West - Los Angeles, CA',
                facilityCode:   'DC6',
                unitsAvailable: 99999,
                lots:           null
              }
            ],
            backOrdered:   0,
            price:         0,
            supplier:      '',
            isActive:      true,
            kitComponents: [
              {
                sku:                 '8675309',
                quantity:            1,
                availableByFacility: [
                  {
                    facilityName:   'Midwest - Mobridge, SD',
                    facilityCode:   'DC1',
                    unitsAvailable: 0,
                    lots:           null
                  },
                  {
                    facilityName:   'West - Los Angeles, CA',
                    facilityCode:   'DC6',
                    unitsAvailable: 0,
                    lots:           null
                  }
                ]
              }
            ]
          },
          {
            id:                  814670,
            sku:                 'TestAvailableByFacility2',
            title:               'TestAvailableByFacility2',
            availableQuantity:   0,
            availableByFacility: [
              {
                facilityName:   'Midwest - Mobridge, SD',
                facilityCode:   'DC1',
                unitsAvailable: 0,
                lots:           null
              },
              {
                facilityName:   'West - Los Angeles, CA',
                facilityCode:   'DC6',
                unitsAvailable: 0,
                lots:           null
              },
              {
                facilityName:   'East - Wilmington, OH',
                facilityCode:   'DC7',
                unitsAvailable: 0,
                lots:           null
              }
            ],
            backOrdered:   0,
            price:         0,
            supplier:      '',
            isActive:      true,
            kitComponents: null
          }
        ],
        currentPage: 1,
        pageSize:    50,
        nextPage:    2,
        totalRows:   236,
        totalPages:  5,
        isLastPage:  false
      }
    })
  }
})

const regularItem = {
  id:                  810334,
  sku:                 'ind.v.4(1)KnockKit',
  title:               'Indigo B+ v.4 knocking Kitted',
  availableQuantity:   868,
  availableByFacility: [
    {
      facilityName:   'West - Los Angeles, CA',
      facilityCode:   'DC6',
      unitsAvailable: 0,
      lots:           null
    },
    {
      facilityName:   'East - Wilmington, OH',
      facilityCode:   'DC7',
      unitsAvailable: 868,
      lots:           null
    }
  ],
  backOrdered:   0,
  price:         0,
  supplier:      '',
  isActive:      true,
  kitComponents: null,
  inventoryId:   810334,
  locked:        false,
  quantity:      1,
}

const kitItem = {
  id:                  814673,
  sku:                 'Test-Timi',
  title:               'Test Timi',
  availableQuantity:   99999,
  availableByFacility: [
    {
      facilityName:   'Midwest - Mobridge, SD',
      facilityCode:   'DC1',
      unitsAvailable: 99999,
      lots:           null
    },
    {
      facilityName:   'West - Los Angeles, CA',
      facilityCode:   'DC6',
      unitsAvailable: 99999,
      lots:           null
    }
  ],
  backOrdered:   0,
  price:         0,
  supplier:      '',
  isActive:      true,
  kitComponents: [
    {
      sku:                 '8675309',
      quantity:            1,
      availableByFacility: [
        {
          facilityName:   'Midwest - Mobridge, SD',
          facilityCode:   'DC1',
          unitsAvailable: 0,
          lots:           null
        },
        {
          facilityName:   'West - Los Angeles, CA',
          facilityCode:   'DC6',
          unitsAvailable: 0,
          lots:           null
        }
      ]
    }
  ],
  inventoryId: 814673,
  locked:      false,
  quantity:    1
}

const regularItemProp = {
  itemPrice:        0,
  itemQuantity:     1,
  item:             regularItem,
  locked:           false,
  addPending:       false,
  isSelected:       false,
  allowKitItems:    true,
  allowPriceUpdate: false,
  action:           vi.fn(),
  editItemAction:   vi.fn()
}

const kitItemProp = {
  itemPrice:        0,
  itemQuantity:     1,
  item:             kitItem,
  locked:           false,
  addPending:       false,
  isSelected:       false,
  allowKitItems:    true,
  allowPriceUpdate: false
}

describe( 'components::InventoryPanel', () => {
  const wrapper = mount( InventoryPanel )

  // Basic rendering tests
  describe( 'rendering', () => {

    it( 'renders successfully', () => {
      expect( wrapper.exists()).toBe( true )
    })

    it( 'renders correct number of products initially', () => {
      const products = wrapper.findAllComponents( InventoryProduct )
      expect( products.length ).toBe( 5 )
    })

    it( 'renders the table component', () => {
      expect( wrapper.findComponent( Table ).exists()).toBe( true )
    })

  })

  // Facility handling tests
  describe( 'facility handling', () => {
    it( 'correctly maps available quantities by facility', async () => {
      await wrapper.setProps({ facility: 'DC6' })
      const products = wrapper.findAllComponents( InventoryProduct )

      // Check LA facility quantities
      expect( products[0].props( 'item' ).availableQuantity ).toBe( 0 ) // First product in LA
      expect( products[2].props( 'item' ).availableQuantity ).toBe( 99999 ) // Third product in LA
    })
  })

  // Selection handling tests
  describe( 'selection handling', () => {
    it( 'starts with no selected products', () => {
      expect( wrapper.vm.selected.length ).toBe( 0 )
    })

    it( 'allows adding products to selection', async () => {
      const firstProduct = wrapper.findComponent( InventoryProduct )
      await firstProduct.vm.$emit( 'addProduct', firstProduct.props( 'item' ))

      await wrapper.vm.$nextTick()
      expect( wrapper.vm.selected.length ).toBe( 1 )
    })

    it( 'allows removing products from selection', async () => {
      const firstProduct = wrapper.findComponent( InventoryProduct )
      await firstProduct.vm.$emit( 'removeProduct', firstProduct.props( 'item' ))

      await wrapper.vm.$nextTick()
      expect( wrapper.vm.selected.length ).toBe( 0 )
    })
  })

  // Props validation tests
  describe( 'props validation', () => {
    it( 'respects disabled prop', async () => {
      await wrapper.setProps({ disabled: true })
      expect( wrapper.classes()).toContain( 'pointer-events-none' )
    })

    it( 'handles allowKitItems prop', async () => {
      await wrapper.setProps({ allowKitItems: false })
      const products = wrapper.findAllComponents( InventoryProduct )
      products.forEach(( product ) => {
        expect( product.props( 'allowKitItems' )).toBe( false )
      })
    })
  })

  // Events tests
  describe( 'events', () => {
    it( 'emits addProduct event when product is added', async () => {
      const product = wrapper.findComponent( InventoryProduct )
      await product.vm.$emit( 'addProduct', product.props( 'item' ))

      expect( wrapper.emitted( 'addProduct' )).toBeTruthy()
      expect( wrapper.emitted( 'addProduct' )[0][0] ).toEqual( product.props( 'item' ))
    })

    it( 'emits updateProduct event when product is updated', async () => {
      const product = wrapper.findComponent( InventoryProduct )
      await product.vm.$emit( 'updateProduct', product.props( 'item' ))

      expect( wrapper.emitted( 'updateProduct' )).toBeTruthy()
      expect( wrapper.emitted( 'updateProduct' )[0][0] ).toEqual( product.props( 'item' ))
    })

    it( 'emits removeProduct event when product is removed', async () => {
      // First add a product to be able to remove it
      const product = wrapper.findComponent( InventoryProduct )
      await product.vm.$emit( 'addProduct', product.props( 'item' ))

      // Then remove it
      await product.vm.$emit( 'removeProduct', product.props( 'item' ))

      expect( wrapper.emitted( 'removeProduct' )).toBeTruthy()
      expect( wrapper.emitted( 'removeProduct' )[0][0] ).toEqual( product.props( 'item' ))
    })

    it( 'handles multiple product additions correctly', async () => {
      const products = wrapper.findAllComponents( InventoryProduct )

      // Add two products
      await products[0].vm.$emit( 'addProduct', products[0].props( 'item' ))
      await products[1].vm.$emit( 'addProduct', products[1].props( 'item' ))

      expect( wrapper.vm.selected.length ).toBe( 2 )
      expect( wrapper.emitted( 'addProduct' )).toHaveLength( 5 )
    })

  })

})

describe( 'components::InventoryProduct', () => {

  const wrapper = mount( InventoryProduct, {
    props: regularItemProp
  })

  it( 'renders correctly', () => {
    expect( wrapper.exists()).toBe( true )
  })

  it( 'renders a button component when item has inventoryId and calls action on click', async () => {
    const containerButton = wrapper.find( '[data-element="container"]' )

    // Check button exists and is correct element
    expect( containerButton.exists()).toBe( true )
    expect( containerButton.element.tagName ).toBe( 'BUTTON' )

    // Trigger click and verify action was called
    await containerButton.trigger( 'click' )
    expect( regularItemProp.action ).toHaveBeenCalledTimes( 1 )
    expect( regularItemProp.action ).toHaveBeenCalledWith( regularItem )
  })

  it( 'renders a div element when item has inventoryId', () => {
    const newWrapper = mount( InventoryProduct, {
      props: {
        ...regularItemProp,
        item: {
          ...regularItem,
          inventoryId: null
        }
      }
    })

    const containerButton = newWrapper.find( '[data-element="container"]' )

    expect( containerButton.exists()).toBe( true )
    expect( containerButton.element.tagName ).toBe( 'DIV' )
  })

  it( 'sets the price input to readonly by default', () => {
    const allInputs = wrapper.findAllComponents( Input )
    const priceInput = allInputs.find( i => i.attributes()['data-input'] === 'edit-price' )

    expect( priceInput.props().readonly ).toBe( true )
    expect( wrapper.vm.enablePriceInput ).toBe( false )
  })

  it( 'sets the price input readonly to false when allowPriceUpdate is true', () => {
    const newWrapper = mount( InventoryProduct, {
      props: { ...regularItemProp, allowPriceUpdate: true, isSelected: true }
    })

    const allInputs = newWrapper.findAllComponents( Input )
    const priceInput = allInputs.find( i => i.attributes()['data-input'] === 'edit-price' )

    expect( priceInput.props().readonly ).toBe( false )
    expect( newWrapper.vm.enablePriceInput ).toBe( true )
  })

  it( 'renders the SKU correctly', () => {
    const sku = wrapper.find( '[data-element="sku"]' )

    expect( sku.exists()).toBe( true )
    expect( sku.text()).toBe( 'ind.v.4(1)KnockKit' )
  })

  it( 'renders the add button by default and hides the kit badge', () => {
    const allButtons = wrapper.findAllComponents( Button )
    const addItemButton = allButtons.find( b => b.attributes()['data-button'] === 'add-item' )
    const kitBadge = wrapper.find( '[data-element="not-kit-allowed"]' )

    expect( addItemButton.exists()).toBe( true )
    expect( addItemButton.text()).toBe( 'Add' )
    expect( kitBadge.exists()).toBe( false )
  })

  it( 'renders kit badge instead of add button when adding kit is disabled', () => {
    const newWrapper = mount( InventoryProduct, {
      props: { ...kitItemProp, allowKitItems: false }
    })

    const allButtons = newWrapper.findAllComponents( Button )
    const addItemButton = allButtons.find( b => b.attributes()['data-button'] === 'add-item' )
    const kitBadge = newWrapper.find( '[data-element="not-kit-allowed"]' )

    expect( kitBadge.text()).toBe( 'KIT' )
    expect( addItemButton ).toBeUndefined()
    expect( kitBadge.exists()).toBe( true )
  })

  it( 'calls addItem function and emit addProduct event with correct payload when the add button is clicked', async () => {
    const allButtons = wrapper.findAllComponents( Button )
    const addItemButton = allButtons.find( b => b.attributes()['data-button'] === 'add-item' )

    await addItemButton.trigger( 'click' )

    expect( wrapper.emitted( 'addProduct' )).toBeTruthy()
    expect( wrapper.emitted().addProduct[0][0] ).toEqual( wrapper.props().item )
  })

  it( 'hides the add button for selected item', () => {
    const newWrapper = mount( InventoryProduct, {
      props: { ...regularItemProp, isSelected: true }
    })

    const allButtons = newWrapper.findAllComponents( Button )
    const addItemButton = allButtons.find( b => b.attributes()['data-button'] === 'add-item' )

    expect( addItemButton ).toBeUndefined()
  })

  it( 'shows the add and remove qty buttons and they behave correctly', async () => {
    const newWrapper = mount( InventoryProduct, {
      props: { ...regularItemProp, isSelected: true }
    })

    const allButtons = newWrapper.findAllComponents( Button )
    const addQtyButton = allButtons.find( b => b.attributes()['data-button'] === 'add-qty' )
    const removeQtyButton = allButtons.find( b => b.attributes()['data-button'] === 'remove-qty' )

    const allInputs = newWrapper.findAllComponents( Input )
    const qtyInput = allInputs.find( i => i.attributes()['data-input'] === 'qty' )

    expect( addQtyButton.exists()).toBe( true )
    expect( removeQtyButton.exists()).toBe( true )
    expect( qtyInput.exists()).toBe( true )

    // Click the addQty button twice to increase the quantity
    await addQtyButton.trigger( 'click' )
    await addQtyButton.trigger( 'click' )

    expect( newWrapper.vm.quantity ).toBe( 3 )
    expect( qtyInput.props().modelValue ).toBe( 3 )

    // Click the removeQty button twice to reduce the quantity
    await removeQtyButton.trigger( 'click' )
    await removeQtyButton.trigger( 'click' )

    await wrapper.vm.$nextTick()

    expect( newWrapper.vm.quantity ).toBe( 1 )
    expect( qtyInput.props().modelValue ).toBe( 1 )

    // Click one more time to emit remove product event
    await removeQtyButton.trigger( 'click' )
    expect( newWrapper.emitted( 'removeProduct' )).toBeTruthy()

  })

  it( 'renders the product title correctly', () => {
    const sku = wrapper.find( '[data-element="title"]' )

    expect( sku.exists()).toBe( true )
    expect( sku.text()).toBe( 'Indigo B+ v.4 knocking Kitted' )
  })

  it( 'renders the stock field correctly', () => {
    const stock = wrapper.find( '[data-element="stock"]' )

    expect( stock.text()).toBe( 'Stock: 868' )
  })

  it( 'renders the kit badge instead of stock field for kit item', () => {
    const newWrapper = mount( InventoryProduct, {
      props: { ...kitItemProp, isSelected: true }
    })

    const stock = newWrapper.find( '[data-element="stock"]' )
    const allBadges = newWrapper.findAllComponents( SimpleBadge )

    const kitBadge = allBadges.find( b => b.attributes()['data-element'] === 'kit-badge' )

    expect( stock.exists()).toBe( false )
    expect( kitBadge.exists()).toBe( true )
    expect( kitBadge.text()).toBe( 'KIT' )
  })

  it( 'renders the edit button when the item is selected and editItemAction is a function', async () => {
    const newWrapper = mount( InventoryProduct, {
      props: { ...regularItemProp, isSelected: true }
    })

    const allButtons = newWrapper.findAllComponents( Button )
    const editButton = allButtons.find( b => b.attributes()['data-button'] === 'edit' )

    expect( editButton.exists()).toBe( true )
    expect( editButton.text()).toBe( 'ind.v.4(1)KnockKit' )

    await editButton.trigger( 'click' )

    expect( regularItemProp.editItemAction ).toHaveBeenCalledTimes( 1 )
    expect( regularItemProp.editItemAction ).toHaveBeenCalledWith( regularItem )
  })

  it( 'does not render the edit button when the item is selected and editItemAction is undefined', async () => {
    const newWrapper = mount( InventoryProduct, {
      props: { ...regularItemProp, isSelected: true, editItemAction: null }
    })

    const allButtons = newWrapper.findAllComponents( Button )
    const editButton = allButtons.find( b => b.attributes()['data-button'] === 'edit' )

    expect( editButton ).toBeUndefined()
  })

  it( 'does not render the edit button when the item is NOT selected and editItemAction is a function', async () => {
    const newWrapper = mount( InventoryProduct, {
      props: { ...regularItemProp, isSelected: false }
    })

    const allButtons = newWrapper.findAllComponents( Button )
    const editButton = allButtons.find( b => b.attributes()['data-button'] === 'edit' )

    expect( editButton ).toBeUndefined()
  })

})
