import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'

import Button from '@lib/components/buttons/Button.vue'
import Upload from '@lib/components/inputs/Upload.vue'

import BulkUploadDrawer from '@/components/BulkUploadDrawer.vue'

// Mock the prop functions
const downloadExampleCsv = vi.fn()
const downloadExampleExcel = vi.fn()
const downloadInstructionsPdf = vi.fn()
const bulkUploadItems = vi.fn(() =>
  new Promise<void>(( resolve ) => {
    setTimeout(() => resolve(), 1000 ) // Resolves after 1 second
  })
)

describe( 'components::bulkUploadDrawer', () => {

  const wrapper = mount( BulkUploadDrawer, {
    props: {
      drawerName: 'Products',
      downloadExampleCsv,
      downloadExampleExcel,
      downloadInstructionsPdf,
      bulkUploadItems,
    },
  })

  it( 'renders the component', () => {
    expect( wrapper.exists()).toBe( true )
  })

  it( 'displays the correct drawer name', () => {
    const title = wrapper.find( '[data-title="name"]' )
    expect( title.text()).toBe( 'Bulk Upload [Products]' )
  })

  it( 'emits close event when close button is clicked', async () => {
    await wrapper.find( 'button[data-button="close"]' ).trigger( 'click' )
    expect( wrapper.emitted( 'close' )).toBeTruthy()
  })

  it( 'calls downloadExampleCsv when "Get CSV Example" button is clicked', async () => {
    await wrapper.find( '[data-button="download-csv"]' ).trigger( 'click' )
    await wrapper.vm.$nextTick()
    expect( downloadExampleCsv ).toHaveBeenCalled()
  })

  it( 'calls downloadExampleExcel when "Get Excel Example" button is clicked', async () => {
    await wrapper.find( '[data-button="download-excel"]' ).trigger( 'click' )
    await wrapper.vm.$nextTick()
    expect( downloadExampleExcel ).toHaveBeenCalled()
  })

  it( 'calls downloadInstructionsPdf when "Get Detailed Instructions" button is clicked', async () => {
    await wrapper.find( '[data-button="download-pdf"]' ).trigger( 'click' )
    await wrapper.vm.$nextTick()
    expect( downloadInstructionsPdf ).toHaveBeenCalled()
  })

  it( 'sets the Upload component file-type prop to .csv and .tsv', () => {
    const uploadComponent = wrapper.findComponent( Upload )
    expect( uploadComponent.exists()).toBe( true )
    expect( uploadComponent.props().fileType ).toBe( '.csv,.tsv' )
  })

  describe( 'submit button', () => {

    const allButtons = wrapper.findAllComponents( Button )
    const submitButton = allButtons.find( b => b.attributes()['data-button'] === 'submit' )

    it( 'disables the submit button if no document is uploaded', () => {
      expect( submitButton.props().disabled ).toBe( true )
    })

    it( 'enables the submit button when a document is uploaded', async () => {
      const file = new File( [ 'content' ], 'test.csv', { type: 'text/csv' })

      wrapper.vm.bulkCsv.document = file
      await wrapper.vm.$nextTick()

      expect( submitButton.props().disabled ).toBe( false )

    })

    it( 'displays pending state when bulk upload is in progress and calls submitBulkFile and bulkUploadItems with a csv file', async () => {

      const allButtons = wrapper.findAllComponents( Button )
      const submitButton = allButtons.find( b => b.attributes()['data-button'] === 'submit' )
      const submitBulkFileSpy = vi.spyOn( wrapper.vm, 'submitBulkFile' )
      const file = new File( [ 'content' ], 'test.csv', { type: 'text/csv' })

      const uploadComponent = wrapper.findComponent( Upload )

      // Set the document to the file
      wrapper.vm.bulkCsv.document = file

      // Ensure pending is false before uploading process starts
      expect( wrapper.vm.pending ).toBe( false )
      expect( uploadComponent.props().pending ).toBe( false )

      // Trigger the button click to initiate the upload
      await submitButton.trigger( 'click' )

      // Ensure pending is true initially
      expect( wrapper.vm.pending ).toBe( true )

      expect( submitBulkFileSpy ).toHaveBeenCalledWith( file )
      expect( bulkUploadItems ).toHaveBeenCalledWith( file )
      expect( uploadComponent.props().pending ).toBe( true )

    })

  })

})
