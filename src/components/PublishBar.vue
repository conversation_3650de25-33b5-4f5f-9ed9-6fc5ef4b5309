<script setup lang="ts">
import { confirm } from '@lib/store/confirm'
import { capitalize, ref } from 'vue'
import { publishDraftOrders } from '@/modules/orders/store'
import { publishDraftProducts } from '@/modules/inventory/store'
import { setNotificationOptions } from '@lib/store/snackbar'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

interface Props {
  id:          number
  type:        'order' | 'product'
  isEmpty?:    boolean
  canPublish?: boolean
}

const props = withDefaults( defineProps<Props>(), { canPublish: true })
const emits = defineEmits<{ update: [] }>()

const pending     = ref( false )
const showConfirm = ref( false )

async function publishOrder() {

  pending.value = true

  const { error } = await publishDraftOrders( [ props?.id ] )

  if ( !error ) {
    setNotificationOptions({ message: 'Order has been published and is now pending.' })
    emits( 'update' )
  }

  pending.value = false

}

async function publishProduct() {

  pending.value = true

  const { error } = await publishDraftProducts( [ props?.id ] )

  if ( !error ) {
    setNotificationOptions({ message: 'Product has been published and is now pending.' })
    emits( 'update' )
  }

  pending.value = false

}

function publish() {

  if ( props.type === 'order' )
    publishOrder()

  if ( props.type === 'product' )
    publishProduct()

}

</script>

<template>

  <div
    class="w-full md:h-10 md:flex md:justify-between md:items-center relative "
    :class="{
      'bg-main': !isEmpty && !showConfirm,
      'bg-warning': isEmpty && !showConfirm,
      'bg-green-300': !isEmpty && showConfirm,
    }"
  >

    <div class="h-10 px-4 grow flex items-center space-x-2 e">

      <div
        class="w-5 h-5 grid place-content-center rounded-full"
        :class="{
          'bg-core-10': !showConfirm,
          'bg-green-700': showConfirm,
        }"
      >

        <Icon v-if="isEmpty" name="warning-sign" size="s" class="text-warning" />
        <Icon
          v-else
          name="checkmark"
          size="s"
          :class="{
            'text-main': !showConfirm,
            'text-green-100': showConfirm,
          }"
        />

      </div>

      <p v-if="type === 'order'" data-element="order-title" class="text-sm font-medium text-core-10">
        <span v-if="isEmpty">This order is empty. Add products to go live.</span>
        <span
          v-else
          :class="{
            'text-green-700': showConfirm,
          }"
        >This order is ready to go live.</span>
      </p>

      <p v-else data-element="product-title" class="text-sm font-medium text-core-10">
        <span v-if="isEmpty">This kit is empty. Add components to go live.</span>
        <span
          v-else
          :class="{
            'text-green-700': showConfirm,
          }"
        >
          This product is ready to go live.</span>
      </p>

    </div>

    <Transition name="view" mode="out-in">

      <div
        v-if="canPublish && !isEmpty && !showConfirm"
        class="h-10 pl-4 grid md:items-center"
        :class="{
          'pr-4': canPublish && !showConfirm,
        }"
      >

        <Button
          v-if="canPublish"
          mode="secondary"
          type="pill"
          size="xxs"
          class="px-4 items-center space-x-2 hidden md:flex"
          @click="showConfirm = true"
        >

          <p class="text-xs md:text-sm font-medium">
            Publish {{ capitalize(props.type) }}
          </p>

          <Icon name="chevron-right" size="s" class="text-main" />

        </Button>

        <Button
          v-if="canPublish"
          mode="secondary"
          type="pill"
          size="xxs"
          class="px-4 items-center justify-center space-x-2 md:hidden flex"
          @click="confirm({
            header: `Publish ${capitalize(props.type)}`,
            description: `Are you sure you want to publish this ${props.type}?`,
            action: async () => props.type === 'order' ? await publishOrder() : props.type === 'product' ? await publishProduct() : null,
          })"
        >

          <p class="text-sm font-medium">
            Publish {{ capitalize(props.type) }}
          </p>

          <Icon name="chevron-right" size="s" class="text-main" />

        </Button>

      </div>

      <div
        v-else-if="showConfirm"
        class="h-full flex items-center"
        :class="{
          'text-core-10': !showConfirm,
          'text-green-700': showConfirm,
        }"
      >

        <Button
          v-if="showConfirm && !pending"
          data-button="confirm"
          mode="naked"
          size="auto"
          class="h-full px-2 md:px-4 text-xs md:text-sm"
          @click="publish"
        >
          Confirm
        </Button>

        <div
          v-if="!pending"
          class="w-px h-4"
          :class="{
            'bg-core-10': !showConfirm,
            'bg-green-700': showConfirm,
          }"
        />

        <Button
          v-if="showConfirm && !pending"
          data-button="cancel"
          mode="naked"
          size="auto"
          class="w-8 h-full flex items-center justify-center"
          @click="showConfirm = false"
        >
          <Icon name="close" size="m" />
        </Button>

        <div v-if="pending" class="flex md:text-sm text-xs items-center justify-center gap-2 mr-4">
          Publishing <Icon name="loading" size="m" />
        </div>

      </div>

    </Transition>

  </div>

</template>
