<script setup lang="ts">

import { ref } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

import type { IconName } from '@lib/store/icon'

interface Props {
  label:          string
  level?:         number
  iconName?:      IconName
  initOpen?:      boolean
  underlined?:    boolean
  labelTextSize?: 'xs' | 'sm' | 'm' | 'l'
}
const props = withDefaults( defineProps<Props>(), {
  labelTextSize: 'sm',
  initOpen:      true
})

const isOpen  = ref<boolean>( props?.initOpen )

</script>

<template>

  <div
    class="w-full overflow-hidden grid grid-rows-[fit-content_1fr] transition-all bg-core-110"
    :class="{
      'h-[2rem]': !isOpen && level !== 1,
      'h-[1.5rem]': !isOpen && level === 1,
    }"
  >

    <Button
      data-button="toggle"
      mode="naked"
      size="auto"
      class="w-full px-4 flex items-start"
      :class="{
        'h-[2rem]': level !== 1,
        'h-[1.5rem]': level === 1,
        'mb-[0.5rem]': isOpen && level === 1,
      }"
      @click="() => isOpen = !isOpen"
    >
      <div class="flex h-full items-center">
        <Icon v-if="iconName" data-icon="icon" :name="iconName" size="m" class="text-core-10" />

        <p
          data-element="label"
          class="text-sm text-left text-core-50 pr-1.5 font-medium"
          :class="{
            'pl-3 ': iconName,
            'hover:text-core-10': !iconName && level !== 2,
          }"
        >
          {{ label }}
        </p>

        <Icon data-icon="toggle-chevron" name="chevron-up" size="s" class="text-core-70 transition" :class="{ 'rotate-180': !isOpen }" />
      </div>
    </Button>

    <div
      data-element="underline"
      class="w-full relative"
      :class="{
        'pb-4 mb-[0.188rem]': level === 1,
        'after:absolute after:left-4 after:w-[calc(100%-2rem)] after:h-[1.125rem] after:border-b after:border-core-100 after:bottom-[0.188rem]': underlined && level !== 2,
      }"
    >
      <slot />
    </div>

  </div>

</template>
