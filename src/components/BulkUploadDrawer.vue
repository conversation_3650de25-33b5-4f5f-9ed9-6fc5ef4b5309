<script setup lang="ts">

import { ref } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Upload from '@lib/components/inputs/Upload.vue'
import Button from '@lib/components/buttons/Button.vue'

const props = defineProps<{
  drawerName:              string
  bulkUploadItems:         ( file: File ) => Promise<void>
  downloadExampleCsv:      () => void
  downloadExampleExcel:    () => void
  downloadInstructionsPdf: () => void
}>()

const emits = defineEmits<{
  ( event: 'close' ): void
}>()

interface BulkCsv {
  document: File
}

const pending  = ref( false )
const bulkCsv  = ref<BulkCsv>({ document: null })

async function submitBulkFile( file: File ) {

  pending.value = true

  await props.bulkUploadItems( file )

  pending.value = false

}

defineExpose({
  bulkCsv,
  pending,
  submitBulkFile
})

</script>

<template>
  <div class="md:w-[26.75rem] w-full flex flex-col overflow-hidden h-full">

    <div class="w-full h-12 flex items-center justify-between border-b border-core-30">

      <div class="h-full px-4 flex gap-3 items-center">
        <span class="text-main">
          <Icon name="upload" size="m" />
        </span>
        <p class="text-sm font-medium" data-title="name">
          Bulk Upload <span class="text-main truncate">[{{ drawerName }}]</span>
        </p>
      </div>

      <Button
        data-button="close"
        mode="naked"
        type="box"
        size="l"
        class="h-full w-16"
        :icon="{
          name: 'close',
          size: 'm',
        }"
        @click="emits('close')"
      />

    </div>

    <div class="p-4 overflow-y-auto h-full">

      <span class="text-sm font-medium">Instructions</span>
      <div class="w-full">
        <div class="flex flex-col text-core-100 text-xs pt-[0.875rem]">
          <div class="flex gap-1">
            <span>1. </span> <span>Download a sample file in the correct format using the provided links below to help you get started. Both CSV and Excel sample files are available for download.</span>
          </div>
          <div class="flex gap-1">
            <span>2. </span><span> Open the file and edit the data using Excel or another preferred method.</span>
          </div>
          <div class="flex gap-1">
            <span>3. </span><span> Once your edits are complete, save the file in a ".csv" or ".tsv" format.</span>
          </div>
          <div class="flex gap-1">
            <span>4. </span><span>Before uploading, double-check that your file has one of the following extensions: ".csv" or ".tsv".</span>
          </div>
        </div>
      </div>

      <div class="flex gap-3 pt-[0.875rem]">
        <Button data-button="download-pdf" type="pill" mode="secondary" size="xxs" class="px-3 text-xs font-medium" @click="downloadInstructionsPdf">
          Get Detailed Instructions
        </Button>
      </div>

      <div class="flex gap-3 pt-2">
        <Button data-button="download-csv" type="pill" mode="ghost" size="xxs" class="px-3 text-main text-xs font-medium" @click="downloadExampleCsv">
          Get CSV Example
        </Button>
        <Button data-button="download-excel" type="pill" mode="secondary" size="xxs" class="px-3 text-xs font-medium" @click="downloadExampleExcel">
          Get Excel Example
        </Button>
      </div>

      <div class="py-6">
        <hr class="border-core-30">
      </div>

      <Upload v-model="bulkCsv.document" file-type=".csv,.tsv" :pending="pending">
        <template #prefix>
          <p class="-mr-2 hidden md:block">
            Drag & Drop <span class="text-core-60">or</span>
          </p>
        </template>
      </Upload>

    </div>

    <div>
      <Button data-button="submit" class="w-full" :disabled="!bulkCsv.document || pending" @click="submitBulkFile(bulkCsv.document)">
        Submit Bulk {{ drawerName }}
      </Button>
    </div>

  </div>
</template>
