<script setup lang="ts" generic="T">

import Button from '@lib/components/buttons/Button.vue'

import type { SearchFilter } from '@/types'

defineProps<{ filter: SearchFilter<T> }>()
defineEmits<{ removeFilter: [ payload: keyof T ] }>()

</script>

<template>

  <div class="w-max h-8 pl-2 flex items-center space-x-1 bg-main-20 border border-main-30 rounded-xs mr-2 my-1">

    <p class="text-xs md:text-sm font-light">
      {{ filter.label }} : <span class="font-medium">{{ filter.value }}</span>
    </p>

    <Button
      size="auto"
      type="box"
      mode="naked"
      :icon="{
        name: 'close',
        size: 's',
      }"
      class="h-full w-8"
      @click="$emit('removeFilter', filter.key)"
    />

  </div>

</template>
