<script setup lang="ts">

import { closeAnnouncement, currentAnnouncement } from '@/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

</script>

<template>

  <div class="w-full md:w-[30.25rem] h-full bg-core-20 grid grid-rows-[max-content_1fr] ">

    <div class="w-full flex items-center justify-between pl-4 border-b border-b-core-30">

      <div class="flex h-full items-center">
        <div class="flex gap-3">
          <Icon name="details" size="m" class="text-main" />
          <span class="text-sm font-medium">Blog Post - <span class="text-warning">[{{ currentAnnouncement?.id }}]</span></span>
        </div>
      </div>

      <Button
        mode="naked"
        type="box"
        size="l"
        class="h-full w-16"
        :icon="{
          name: 'close',
          size: 'm',
        }"
        @click="closeAnnouncement"
      />

    </div>

    <div class="h-full overflow-y-auto">
      <img v-if="currentAnnouncement?.yoast_head_json?.og_image?.[0]?.url" :src="currentAnnouncement?.yoast_head_json?.og_image?.[0]?.url" alt="Announcement Image">

      <div class="flex flex-col gap-4 md:px-[1.625rem] md:py-8 p-4">
        <h3 class=" text-lg text-core-100" v-html="currentAnnouncement?.title?.rendered" />
        <p class="text-sm text-core-80" v-html="currentAnnouncement?.content?.rendered" />
      </div>
    </div>
  </div>

</template>
