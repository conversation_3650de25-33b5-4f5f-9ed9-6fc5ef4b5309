<script setup lang="ts">

import { announcementsList, openAnnouncement } from '@/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

interface Emits {
  ( eventName: 'close' ): void
}

const emits = defineEmits<Emits>()

function timeAgo( input: string | Date ): string {

  const date = new Date( input )
  const now = new Date()

  const secondsPast = Math.floor(( now.getTime() - date.getTime()) / 1000 )

  if ( secondsPast < 60 )
    return `${secondsPast} second${secondsPast === 1 ? '' : 's'} ago`

  if ( secondsPast < 3600 ) {
    const minutes = Math.floor( secondsPast / 60 )
    return `${minutes} minute${minutes === 1 ? '' : 's'} ago`
  }
  if ( secondsPast < 86400 ) {
    const hours = Math.floor( secondsPast / 3600 )
    return `${hours} hour${hours === 1 ? '' : 's'} ago`
  }
  if ( secondsPast < 2592000 ) {
    const days = Math.floor( secondsPast / 86400 )
    return `${days} day${days === 1 ? '' : 's'} ago`
  }
  if ( secondsPast < 31536000 ) {
    const months = Math.floor( secondsPast / 2592000 )
    return `${months} month${months === 1 ? '' : 's'} ago`
  }

  const years = Math.floor( secondsPast / 31536000 )
  return `${years} year${years === 1 ? '' : 's'} ago`

}

</script>

<template>

  <div class="w-full h-full md:w-[21rem] grid grid-rows-[max-content_1fr]">

    <div class="w-full flex items-center justify-between pl-4 border-b border-b-core-30">

      <div class="flex h-full items-center">
        <span class="text-sm font-medium">News and Announcements</span>
      </div>

      <Button
        mode="naked"
        type="box"
        size="auto"
        class="h-9 w-9"
        :icon="{
          name: 'close',
          size: 's',
        }"
        @click="emits('close')"
      />

    </div>

    <div class="overflow-y-auto">

      <div v-if="announcementsList?.length">

        <div
          v-for="announcement in announcementsList"
          :key="announcement.id"
          class="flex items-center justify-between gap-2 py-3 px-4 hover:bg-core-120/[0.03] cursor-pointer border-b border-core-30 bg-core-10"
          @click="openAnnouncement(announcement)"
        >

          <div class="text-sm">

            <p class="line-clamp-2" v-html="announcement?.title?.rendered" />

            <span class="text-[0.625rem] text-core-70">{{ timeAgo(`${announcement.date_gmt}Z`) }}</span>

          </div>

          <img v-if="announcement?.yoast_head_json?.og_image?.[0]?.url" class="object-cover min-w-16 w-16 h-16" :src="announcement?.yoast_head_json?.og_image?.[0]?.url">

          <div v-else class=" min-w-16 h-16 flex items-center justify-center rounded-xs border border-core-30">
            <Icon class="overflow-visible" name="shaded-product" size="m" />
          </div>

        </div>

      </div>

      <div v-else>

        <div class="flex flex-col items-center mt-16 text-core/80 px-6">
          <Icon size="l" name="portal-announcements" /><span class="text-lg text-center ">{{ $t('announcements.drawer.noAnnouncements') }}</span>
        </div>

      </div>

    </div>

  </div>

</template>
