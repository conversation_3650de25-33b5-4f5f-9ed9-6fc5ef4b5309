<script setup lang="ts">

import { userName } from '@/modules/auth/store'
import { computed } from 'vue'
import { openAnnouncementsDrawer } from '@/store'

import Tab from '@lib/components/buttons/Tab.vue'
import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import AnnouncementsDrawer from '@/components/AnnouncementsDrawer.vue'

const props = defineProps<{
  pending?:  boolean
  openMenu?: boolean
}>()

const emits = defineEmits<{
  ( eventName: 'update:openMenu', value: boolean ): void
}>()

const toggleMenu = computed({
  get: () => props.openMenu,
  set: value => emits( 'update:openMenu', value )
})

const environmentFlag = import.meta.env.VITE_ENV ? import.meta.env.VITE_ENV : null

function toggleAnnouncementsDrawer() {
  openAnnouncementsDrawer.value = !openAnnouncementsDrawer.value
}

function generateUserName( name: string ) {

  if ( !name )
    return '/'

  const n = name.split( ' ' )

  if ( n.length > 1 )
    return `${n[0]} ${n[1][0]}.`

  else
    return name

}

defineExpose({
  generateUserName
})

</script>

<template>

  <div class="w-full h-12 flex items-center border-t border-core-30 md:border-t-0">

    <div class=" h-full lg:hidden border-core-30" :class="{ 'border-b': !toggleMenu }">

      <Button
        type="box"
        size="auto"
        mode="naked"
        class="w-12 h-full relative"
        :is-active="toggleMenu"
        @click="toggleMenu = !toggleMenu"
      >

        <div
          class="w-4 h-0.5 absolute left-[0.95rem] bg-core-120 transition-transform"
          :class="{
            'top-[1.2rem]': !toggleMenu,
            'rotate-45 top-[1.4rem]': toggleMenu,
          }"
        />

        <div
          class="w-4 h-0.5 absolute left-[0.95rem] bg-core-120 transition-transform"
          :class="{
            'top-[1.5rem]': !toggleMenu,
            '-rotate-45 top-[1.4rem]': toggleMenu,
          }"
        />

      </Button>

    </div>

    <div class="h-full flex items-center grow lg:pl-4 pl-2 border-b border-core-30">

      <svg class="hidden md:block" width="250" height="28" viewBox="0 0 250 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M133.12 0H127.414V27.4822H133.12V0ZM22.0131 4.93886C19.5073 2.31013 16.4617 0.995767 12.9534 0.995767C10.6403 0.995767 8.48142 1.59321 6.47672 2.78808C4.47202 3.98296 2.89139 5.61596 1.73484 7.68708C0.578279 9.7582 0 11.9886 0 14.4581C0 18.1223 1.23366 21.2689 3.70098 23.9772C6.16831 26.6458 9.25246 27.9602 12.992 27.9602C16.5773 27.9602 19.5844 26.6458 22.0903 24.0569C24.5961 21.468 25.8298 18.2817 25.8298 14.5377C25.8298 10.7539 24.5576 7.56759 22.0131 4.93886ZM7.55618 8.72264C8.9826 7.1693 10.7945 6.41254 12.9149 6.41254C15.0353 6.41254 16.8086 7.1693 18.3507 8.8023C19.8157 10.3556 20.5482 12.2674 20.5482 14.5377C20.5482 16.808 19.8157 18.7198 18.3507 20.2731C16.8857 21.8265 15.1124 22.5832 13.0306 22.5832C11.1801 22.5832 9.56088 21.9858 8.13446 20.7909C6.32252 19.2376 5.39727 17.1664 5.39727 14.5377C5.39727 12.1878 6.0912 10.2362 7.55618 8.72264ZM168.241 1.15504C165.349 1.15504 162.612 1.39402 160.492 1.75248H160.453V27.5618C161.88 27.761 164.038 27.9601 167.007 27.9601C172.019 27.9601 176.105 26.8847 178.804 24.5746C181.233 22.4239 183.006 18.9189 183.006 13.9004C183.006 9.24038 181.348 6.01421 178.688 3.98292C176.298 2.11095 173.098 1.15504 168.241 1.15504ZM168.241 23.3001C167.547 23.3001 166.737 23.3001 166.236 23.1806H166.197V5.97438C166.699 5.8549 167.585 5.73541 168.819 5.73541C173.831 5.73541 176.915 8.64294 176.915 14.0995C176.915 20.3527 173.484 23.3399 168.241 23.3001ZM104.36 8.04554C99.0399 8.04554 94.7221 12.5064 94.7221 18.0029C94.7221 23.4993 99.0399 27.9602 104.36 27.9602C109.68 27.9602 113.998 23.4993 113.998 18.0029C113.998 12.5064 109.68 8.04554 104.36 8.04554ZM104.36 23.1807C101.97 23.1807 100.042 20.8706 100.042 18.0029C100.042 15.1351 101.97 12.8251 104.36 12.8251C106.75 12.8251 108.678 15.1351 108.678 18.0029C108.678 20.8706 106.75 23.1807 104.36 23.1807ZM190.871 8.60307C190.986 10.2361 191.025 12.0682 191.025 14.8563H190.986V27.5618H196.692V17.9629C196.692 17.485 196.73 17.0469 196.808 16.6486C197.193 14.8164 198.62 13.6614 200.701 13.6614C201.322 13.6614 201.735 13.7329 202.17 13.8081L202.243 13.8207V8.28443C201.819 8.20477 201.588 8.16495 201.048 8.16495C199.275 8.16495 197.039 9.31999 196.114 12.108H195.959L195.728 8.60307H190.871ZM207.448 19.9544C207.602 22.384 209.954 23.539 212.614 23.539C214.58 23.539 216.122 23.2602 217.664 22.7425L218.397 26.7254C216.508 27.522 214.233 27.9203 211.766 27.9203C205.559 27.9203 201.974 24.2161 201.974 18.2816C201.974 13.4622 204.865 8.16495 211.226 8.16495C217.163 8.16495 219.399 12.9445 219.399 17.6045C219.399 18.6002 219.283 19.5163 219.206 19.9146H207.448V19.9544ZM214.156 15.9316C214.156 14.4978 213.539 12.108 210.918 12.108C208.527 12.108 207.525 14.3783 207.409 15.9316H214.156ZM229.962 27.9203C232.121 27.9203 234.049 27.5618 235.051 27.044L234.396 22.623C233.509 23.0213 232.468 23.2602 231.119 23.2602C228.343 23.2602 226.107 21.4281 226.107 17.9629C226.068 14.8563 227.996 12.6657 231.003 12.6657C232.507 12.6657 233.548 12.9445 234.203 13.2631L235.09 8.88187C233.894 8.44375 232.237 8.16495 230.733 8.16495C223.91 8.16495 220.247 12.7055 220.247 18.2417C220.247 24.1763 224.025 27.9203 229.962 27.9203ZM243.995 3.50501V8.60315H249.161L247.85 12.9445H243.995V19.7952C243.995 22.0654 244.573 23.1408 246.231 23.1408C247.002 23.1408 247.388 23.101 247.927 22.9815L247.966 27.4424C247.272 27.7212 245.884 27.9602 244.304 27.9602C242.453 27.9602 240.911 27.2831 239.986 26.3272C238.945 25.2119 238.405 23.3798 238.405 20.7112V12.9445H235.167L235.938 8.60315H238.367V5.09818L243.995 3.50501ZM26.8322 14.6174C26.8322 12.2674 26.7551 10.276 26.6779 8.5633H31.6126L31.8825 11.192H31.9981C32.7306 9.95733 34.5811 8.12518 37.6652 8.12518C41.4048 8.12518 44.2191 10.7141 44.2191 16.2902V27.4822H38.5519V17.0071C38.5519 14.5775 37.7423 12.9047 35.6605 12.9047C34.0799 12.9047 33.1547 14.0199 32.7306 15.0953C32.5764 15.4936 32.5378 16.0114 32.5378 16.569V27.4822H26.8322V14.6174ZM55.9773 23.6188C53.3173 23.6188 50.9656 22.4637 50.8114 20.0342V19.9943H62.5697C62.6468 19.596 62.7625 18.68 62.7625 17.6842C62.7625 13.0242 60.5265 8.2447 54.5895 8.2447C48.2284 8.2447 45.337 13.542 45.337 18.3613C45.337 24.2959 48.9223 28 55.1292 28C57.5965 28 59.8711 27.6017 61.7601 26.8051L61.0276 22.8222C59.4856 23.34 57.9435 23.6188 55.9773 23.6188ZM54.2811 12.1878C56.9026 12.1878 57.5194 14.5775 57.5194 16.0114H50.7728C50.927 14.4581 51.8908 12.1878 54.2811 12.1878ZM71.2825 27.5221L65.2684 1.39407H71.3596L73.2872 12.1081C73.5263 13.409 73.7522 14.5396 73.9649 15.6042L73.9653 15.6062L73.9654 15.6068L73.9655 15.6072C74.2671 17.1165 74.5421 18.4932 74.7907 20.0342H74.8678C75.1323 18.2703 75.4569 16.8474 75.8226 15.2434L75.8227 15.2432L75.8229 15.2424C76.0434 14.2751 76.2791 13.242 76.5256 12.0285L78.6459 1.39407H84.6986L86.7418 12.3869C86.8998 13.2246 87.0434 13.9523 87.1765 14.6269L87.177 14.6297L87.1779 14.6345L87.1782 14.6355C87.5316 16.4267 87.8114 17.8445 88.0911 19.9545H88.1683C88.3958 18.2384 88.6771 16.883 88.9804 15.4216L88.9804 15.4215L88.9804 15.4212L88.9805 15.4209L88.9805 15.4208C89.191 14.4064 89.4121 13.341 89.6332 12.0683L91.6765 1.4339H97.4978L90.9825 27.5221H84.8142L82.6939 16.2902C82.5513 15.5425 82.4181 14.9076 82.2934 14.313C81.9796 12.8173 81.7195 11.5771 81.4988 9.43958H81.4217C81.065 11.7116 80.7542 12.9895 80.348 14.66C80.2272 15.1566 80.098 15.6879 79.9567 16.2902L77.5665 27.5221H71.2825ZM153.167 21.8264V0H147.461V10.1963H147.384C146.574 8.92175 144.84 8.08534 142.565 8.08534C138.17 8.08534 134.315 11.8293 134.353 18.202C134.353 24.0967 137.823 27.9203 142.179 27.9203C144.531 27.9203 146.806 26.8051 147.924 24.734H148.039L148.271 27.4822H153.321C153.244 26.2076 153.167 23.9374 153.167 21.8264ZM147.423 15.4936C147.5 15.852 147.5 16.2503 147.5 16.569V19.0384C147.5 19.5163 147.461 19.9545 147.384 20.3926C147.037 22.0654 145.688 23.2204 144.03 23.2204C141.678 23.2204 140.136 21.229 140.136 18.0028C140.136 15.0156 141.447 12.6259 144.069 12.6259C145.842 12.6259 147.076 13.9402 147.423 15.4936ZM189.714 3.34567C189.714 4.97867 188.557 6.29303 186.63 6.29303C184.818 6.29303 183.661 4.97867 183.661 3.34567C183.661 1.67284 184.856 0.398307 186.707 0.398307C188.557 0.398307 189.675 1.67284 189.714 3.34567ZM183.854 27.522V8.60313H189.56V27.522H183.854ZM115.077 8.52349C115.193 10.1565 115.232 11.9886 115.232 14.7767H115.193V27.4822H120.899V17.8435C120.899 17.3656 120.937 16.9275 121.014 16.5292C121.4 14.697 122.826 13.542 124.908 13.542C125.529 13.542 125.942 13.6135 126.377 13.6887L126.45 13.7013V8.16503C126.026 8.08537 125.795 8.04554 125.255 8.04554C123.482 8.04554 121.246 9.20059 120.321 11.9886H120.166L119.935 8.52349H115.077Z" fill="#161616" />
        <path d="M12.9149 15.0157L16.1147 20.552L15.5364 8.12524L6.93933 16.808L12.9149 15.0157Z" fill="#FF832B" />
      </svg>

      <svg class="md:hidden" width="75" height="24" viewBox="0 0 75 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M11.9457 0C15.181 0 17.9896 1.16987 20.3006 3.5096C22.647 5.84934 23.8203 8.68538 23.8203 12.0532C23.8203 15.3855 22.6826 18.2216 20.3717 20.5259C18.0607 22.8301 15.2876 24 11.9812 24C8.53263 24 5.68842 22.8301 3.41305 20.4549C1.13768 18.0798 0 15.2437 0 11.9823C0 9.78434 0.533289 7.79911 1.59987 5.95569C2.66645 4.11226 4.1241 2.65879 5.97284 1.59527C7.82158 0.531758 9.81253 0 11.9457 0ZM11.9101 4.82127C9.95474 4.82127 8.28376 5.49483 6.96832 6.8774C5.61732 8.22452 4.97737 9.9616 4.97737 12.0532C4.97737 14.3929 5.83063 16.2363 7.50161 17.6189C8.81705 18.6824 10.3103 19.2142 12.0168 19.2142C13.9366 19.2142 15.5721 18.5406 16.9231 17.1581C18.2741 15.7755 18.9496 14.0739 18.9496 12.0532C18.9496 10.0325 18.2741 8.33087 16.9231 6.9483C15.5009 5.53028 13.8655 4.82127 11.9101 4.82127Z" fill="#161616" />
        <path d="M53.8977 0.88625C55.8175 0.567195 58.3418 0.354492 60.9727 0.354492C65.4167 0.354492 68.3321 1.16985 70.5363 2.83603C72.9539 4.644 74.4827 7.48005 74.4827 11.5923C74.4827 16.0236 72.8472 19.1078 70.643 20.9867C68.1899 23.0074 64.4213 24 59.8705 24C57.133 24 55.1776 23.8227 53.8977 23.6455V0.88625V0.88625ZM59.1239 19.7814C59.5861 19.8877 60.3327 19.8877 60.9371 19.8877C65.7367 19.9232 68.8654 17.2998 68.8654 11.7696C68.8654 6.94829 66.0211 4.39585 61.4704 4.39585C60.3327 4.39585 59.5506 4.5022 59.0884 4.60855V19.7814H59.1239Z" fill="#161616" />
        <path d="M28.371 23.5037L22.8248 0.283569H28.4421L30.2198 9.81976C30.7531 12.6204 31.2153 14.5347 31.6063 16.8744H31.6774C32.0685 14.3574 32.6018 12.6204 33.2062 9.74886L35.1971 0.283569H40.7789L42.6632 10.0679C43.1965 12.7976 43.552 14.2156 43.9075 16.8035H43.9786C44.3342 14.2156 44.8319 12.5494 45.3296 9.78431L47.2139 0.31902H52.5824L46.574 23.5391H40.8856L38.9302 13.5421C38.468 11.2023 38.1125 10.1034 37.828 7.44457H37.7569C37.3303 10.0679 36.9748 11.2023 36.4059 13.5421L34.1306 23.5037H28.371Z" fill="#161616" />
        <path d="M11.9101 12.4786L14.8609 17.4063L14.3277 6.3457L6.39941 14.0739L11.9101 12.4786Z" fill="#FF832B" />
      </svg>

      <span v-if="environmentFlag" class="text-white px-2 py-0.5 ml-2 bg-error rounded-sm">{{ environmentFlag }}</span>

    </div>

    <Tab size="auto" class="w-12 h-full grid place-content-center border-l border-core-30" @click="toggleAnnouncementsDrawer">
      <Icon size="l" name="portal-announcements" />
    </Tab>

    <Tab
      size="auto"
      class="h-full md:pl-6 flex items-center md:space-x-2 border-l border-core-30"
      :is-active="$route.path === '/profile'"
      @click="$router.push('/profile')"
    >

      <div class="text-right hidden md:block">
        <p class="text-orange-600 font-bold">
          My OWD
        </p>
        <p class="text-sm">
          {{ generateUserName(userName) }}
        </p>
      </div>

      <div class="w-16 h-full grid place-content-center">

        <div class="text-main-100 w-10 h-10 grid place-content-center bg-main-40 rounded-full">
          <Icon name="user" size="s" />
        </div>

      </div>

    </Tab>

    <Sidebar :dim="true" :open="openAnnouncementsDrawer" @close="toggleAnnouncementsDrawer">
      <AnnouncementsDrawer @close="toggleAnnouncementsDrawer" />
    </Sidebar>

  </div>

</template>
