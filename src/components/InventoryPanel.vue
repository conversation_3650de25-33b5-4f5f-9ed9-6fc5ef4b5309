<script setup lang="ts">

import { getProducts } from '@/modules/inventory/store'
import { facilitiesList } from '@/store'
import { inventoryParams } from '@/modules/inventory/routes'
import { useRoute, useRouter } from 'vue-router'
import { computed, reactive, ref, watch } from 'vue'
import { checkValue, sanitizeQueryParams, viewSetup } from '@lib/scripts/utils'

import Icon from '@lib/components/blocks/Icon.vue'
import Table from '@lib/components/table/Table.vue'
import Button from '@lib/components/buttons/Button.vue'
import InventoryProduct from '@/components/InventoryProduct.vue'

import type { InventoryPanelProduct, InventoryParams } from '@/modules/inventory/types'

const props = withDefaults( defineProps<{
  facility?:                string
  disabled?:                boolean
  openSelected?:            boolean
  allowLocking?:            boolean
  allowKitItems?:           boolean
  selectedProducts:         InventoryPanelProduct[]
  allowPriceUpdate?:        boolean
  matchSelectedProductsBy?: 'id' | 'sku'
  actions?: {
    add?:            ( product: InventoryPanelProduct ) => ( Promise<InventoryPanelProduct | void> | void )
    itemAction?:     ( product: InventoryPanelProduct ) => ( Promise<InventoryPanelProduct | void> | void )
    editItemAction?: ( product: InventoryPanelProduct ) => ( Promise<InventoryPanelProduct | void> | void )
  }
}>(), {
  showEdit:                false,
  openSelected:            false,
  allowLocking:            true,
  allowKitItems:           true,
  matchSelectedProductsBy: 'id'
})

const emits = defineEmits<{
  ( event: 'addProduct', data: InventoryPanelProduct ): void
  ( event: 'removeProduct', data: InventoryPanelProduct ): void
  ( event: 'updateProduct', data: InventoryPanelProduct ): void
}>()

const total             = ref( 0 )
const route             = useRoute()
const router            = useRouter()
const params            = reactive<InventoryParams>({ ...inventoryParams, ...sanitizeQueryParams( route.query ) })
const pending           = ref( false )
const maxPages          = ref( 0 )
const products          = ref<InventoryPanelProduct[]>( [] )
const selected          = defineModel<InventoryPanelProduct[]>( 'selectedProducts', { default: [] })
const addPending        = ref<string>( null )
const hasSelected       = computed(() => selected.value.length > 0 )
const facilityName      = computed(() => facilitiesList.value.find( f => f.id === props.facility )?.name )
const toggleSelected    = ref<boolean>( props.openSelected )
const inventoryTotal    = computed(() => toggleSelected.value ? 0 : total.value )
const inventoryMaxPages = computed(() => toggleSelected.value ? 0 : maxPages.value )

function schema(): TableSchema<InventoryPanelProduct> {

  return [
    {
      key:   'sku',
      label: 'Product',
    },
    {
      key:      'id',
      label:    'Reference',
      lockFlex: true
    },
    {
      key:      'availableQuantity',
      label:    'Available',
      sortKey:  'available',
      align:    'right',
      lockFlex: true
    },
    {
      key:      'price',
      label:    'Price',
      format:   'currency',
      lockFlex: true
    },
    {
      key:       null,
      label:     null,
      fixedSize: 80
    }
  ]

}

/**
 * Maps the available quantity of each product by facility, if facility is sent, else is mapping availableQuantity.
 *
 * @param products - The array of inventory items.
 * @param facility - The facility name.
 * @returns The array of inventory items with mapped available quantity.
 */
function mapAvailableByFacility( products: InventoryPanelProduct[], facility: string ) {

  const facilityProducts: InventoryPanelProduct[] = []

  products.forEach(( product ) => {

    const availableByFacility = product.availableByFacility?.find( f => f.facilityCode === facility )
    const finalQuantity = facility ? availableByFacility?.unitsAvailable ?? 0 : product.availableQuantity
    const locked = props?.allowLocking ? ( !!facility && !availableByFacility ) : false

    facilityProducts.push({
      ...product,
      locked,
      quantity:          1,
      availableQuantity: finalQuantity,
    })

  })

  return facilityProducts

}

function mapFromSelectedProduct( selectedProduct: InventoryPanelProduct ) {

  const product = products.value.find( p => p[props.matchSelectedProductsBy] === selectedProduct[props.matchSelectedProductsBy] )

  if ( !product )
    return

  if ( checkValue( selectedProduct.price ))
    product.price = selectedProduct.price

  product.locked = selectedProduct.locked
  product.quantity = selectedProduct.quantity

  selectedProduct.inventoryId = product.inventoryId

  emits( 'updateProduct', product )

}

function mapToSelectedProduct( product: InventoryPanelProduct ) {

  const selectedProduct = selected.value.find( s => s[props.matchSelectedProductsBy] === product[props.matchSelectedProductsBy] )

  if ( !selectedProduct ) {
    selected.value = [ ...selected.value, product ]
  }

  else {
    selectedProduct.price = product.price
    selectedProduct.locked = product.locked
    selectedProduct.quantity = product.quantity
    selectedProduct.inventoryId = product.inventoryId
  }

  emits( 'updateProduct', product )

}

async function getInventoryData( viewParams: InventoryParams ) {

  pending.value = true

  const { payload } = await getProducts({ ...viewParams, pageSize: 50 })

  total.value = payload.totalRows
  maxPages.value = payload.totalPages
  products.value = payload.items || []

  // Explicitly set the inventoryId to the product id.
  // This is required for the selected products to be correctly mapped.
  // The InventoryId is used to open the product details.

  products.value = products.value.map( p => ({ ...p, inventoryId: p.id }))
  products.value = mapAvailableByFacility( products.value, props.facility )

  selected.value.forEach(( s ) => {
    products.value.forEach(() => mapFromSelectedProduct( s ))
  })

  pending.value = false

}

function isAddedItem( productId: number, sku: string ) {

  if ( props.matchSelectedProductsBy === 'sku' )
    return selected.value.some( p => p.sku === sku )

  return selected.value.some( p => p.id === productId )
}

async function addItem( product: InventoryPanelProduct ) {

  addPending.value = product.sku

  let addedProduct: InventoryPanelProduct | void

  if ( props?.actions?.add )
    addedProduct = await props.actions.add( product )

  const selectedProduct = addedProduct ? { ...product, ...addedProduct } : product

  selected.value = [ ...selected.value, selectedProduct ]
  products.value = products.value.map( p => p[props.matchSelectedProductsBy] === selectedProduct[props.matchSelectedProductsBy] ? { ...p, ...selectedProduct } : p )

  emits( 'addProduct', product )

  addPending.value = null

}

function removeItem( product: InventoryPanelProduct ) {

  selected.value = selected.value.filter( p => p[props.matchSelectedProductsBy] !== product[props.matchSelectedProductsBy] )
  emits( 'removeProduct', product )

}

watch(() => props.facility, n => products.value = mapAvailableByFacility( products.value, n ))

watch( hasSelected, ( n ) => {

  if ( !n )
    toggleSelected.value = false

})

watch( selected, ( n ) => {

  n.forEach( s => products.value.forEach(() => mapFromSelectedProduct( s )))

}, { deep: true })

viewSetup( route.name as string, params, router, getInventoryData )

defineExpose({
  selected,
  toggleSelected
})

</script>

<template>

  <div
    class="w-full h-full overflow-hidden"
    :class="{
      'pointer-events-none': disabled,
    }"
  >

    <Table
      v-model:params="params"
      name="Products"
      :flex="true"
      :schema="schema"
      :pending="pending"
      :records="products"
      :hide-labels="true"
      record-map-key="id"
      :enable-search="!toggleSelected"
      :pagination="{
        total: inventoryTotal,
        compact: true,
        maxPages: inventoryMaxPages,
      }"
      :search="{
        enabled: !toggleSelected,
        searchKey: 'sku',
      }"
    >

      <template #table-head>

        <div class="h-10 pl-4 flex items-center space-x-3">

          <p class="text-sm font-medium grow">
            {{ facilityName ?? 'Your Products' }}
          </p>

        </div>

      </template>

      <template #table-neck>

        <div
          v-if="selected.length > 0"
          class="w-full h-10 border-b border-core-30"
        >

          <Button
            size="auto"
            mode="naked"
            class="w-full h-full px-4 flex items-center space-x-2"
            @click="toggleSelected = !toggleSelected"
          >

            <div class="grow flex items-center space-x-2">
              <p class="text-sm text-main">
                Selected Items
              </p>
              <span class="bg-main text-xs text-core-10 px-1.5 py-0.5 rounded-xs">{{ selected.length }}</span>
            </div>

            <Icon
              name="chevron-right"
              size="m"
              class="text-main"
              :class="{
                'rotate-180': toggleSelected,
              }"
            />

            <p v-if="toggleSelected" class="text-sm">
              All Products
            </p>

          </Button>

        </div>

      </template>

      <Transition name="source-form" mode="out-in">

        <div v-if="toggleSelected" class="w-full">

          <InventoryProduct
            v-for="product in selected"
            :key="product.id"
            v-model:item-price="product.price"
            v-model:item-quantity="product.quantity"
            :item="product"
            :locked="product.locked"
            :add-pending="addPending === product.sku"
            :is-selected="true"
            :allow-price-update="allowPriceUpdate"
            :allow-kit-items="allowKitItems"
            :action="props.actions?.itemAction"
            :edit-item-action="props.actions?.editItemAction"
            @remove-product="() => removeItem(product)"
            @update-product="() => mapFromSelectedProduct(product)"
          />

        </div>

        <div v-else class="w-full">

          <InventoryProduct
            v-for="product in products"
            :key="product.id"
            v-model:item-price="product.price"
            v-model:item-quantity="product.quantity"
            :item="product"
            :locked="product.locked"
            :add-pending="addPending === product.sku"
            :is-selected="isAddedItem(product.id, product.sku)"
            :allow-price-update="allowPriceUpdate"
            :allow-kit-items="allowKitItems"
            :action="props.actions?.itemAction"
            :edit-item-action="props.actions?.editItemAction"
            @add-product="addItem(product)"
            @remove-product="() => removeItem(product)"
            @update-product="() => mapToSelectedProduct(product)"
          />

        </div>

      </Transition>

    </Table>

  </div>

</template>
