<script setup lang="ts">

import type { ImportStatus } from '@/types'

defineProps<{
  status: ImportStatus
}>()

function remapStatusName( s: ImportStatus ) {

  if ( s === 'Processing' )
    return 'In Progress'

  return s

}

</script>

<template>

  <div
    class="h-5 p-2 flex items-center space-x-1 rounded-full"
    :class="{
      'bg-core-40': status === 'Draft',
      'bg-main-30': status === 'Processing',
      'bg-error-20': status === 'Failed',
      'bg-data1-110': status === 'Pending',
      'bg-success-20': status === 'Processed',
    }"
  >

    <div
      class="w-2 h-2 rounded-full"
      :class="{
        'bg-main': status === 'Processing',
        'bg-error': status === 'Failed',
        'bg-core-60': status === 'Draft',
        'bg-success': status === 'Processed',
        'bg-data1-120': status === 'Pending',
      }"
    />

    <p class="text-xs text-core-90">
      {{ remapStatusName(status) }}
    </p>

  </div>

</template>
