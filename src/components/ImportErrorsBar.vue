<script setup lang="ts">

import { capitalize } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

interface Props {
  type:       'order' | 'product'
  count:      number
  canResolve: boolean
}

defineProps<Props>()
defineEmits<{ resolve: [] }>()

</script>

<template>

  <div class="w-full md:h-10 flex justify-between bg-error">

    <div class="h-10 px-4 flex items-center space-x-2">

      <div class="w-4 h-4 grid place-content-center rounded-full">
        <Icon name="issue-circle" size="m" class="text-core-10" />
      </div>

      <p class="text-xs md:text-sm font-medium text-core-10">
        {{ capitalize(type) }} Errors [{{ count }}]
      </p>

    </div>

    <div v-if="canResolve" class="h-10 flex items-center pr-4">

      <Button
        mode="secondary"
        type="pill"
        size="xxs"
        class="px-4 flex items-center justify-center space-x-2"
        @click="$emit('resolve')"
      >

        <p class="text-sm text-error font-medium">
          Resolve Errors
        </p>

        <Icon name="chevron-right" size="s" class="text-error" />

      </Button>

    </div>

  </div>

</template>
