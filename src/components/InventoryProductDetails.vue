<script setup lang="ts">

import { checkValue } from '@lib/scripts/utils'
import { facilitiesList } from '@/store'
import { computed, onMounted, ref } from 'vue'
import { getDraftProduct, getProduct } from '@/modules/inventory/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'
import InventoryProductDetailsRow from '@/components/InventoryProductDetailsRow.vue'

import type { DraftProduct, LiveProduct } from '@/modules/inventory/types'

const props = defineProps<{ itemId: number, type?: 'Live' | 'Draft' }>()

defineEmits<{ close: [] }>()

const product       = ref<LiveProduct | DraftProduct>( null )
const pending       = ref<boolean>( false )
const liveProduct   = ref<LiveProduct>( null )
const productType   = computed(() => props?.type || 'Live' )

async function getProductData() {

  pending.value = true

  if ( productType.value === 'Live' ) {

    const { payload } = await getProduct( Number.parseInt( props.itemId.toString()))

    facilitiesList.value.forEach(( f ) => {

      const payloadFacility = payload.availableStockLevels?.find( e => e.facilityCode === f.id )

      if ( !payloadFacility ) {

        payload.availableStockLevels?.push({
          lots:           [],
          facilityCode:   f.id,
          facilityName:   f.name,
          unitsAvailable: null
        })

      }

    })

    liveProduct.value = payload
    product.value = payload

  }

  else {

    const { payload } = await getDraftProduct( Number.parseInt( props.itemId.toString()))

    product.value = payload

  }

  pending.value = false

}

function isLowInventory( unitsAvailable: number, reorderAlertQuantity: number ) {
  return unitsAvailable > 0 && unitsAvailable <= reorderAlertQuantity
}

function isOutOfStock( unitsAvailable: number ) {
  return unitsAvailable === 0
}

function isSufficientInventory( unitsAvailable: number, reorderAlertQuantity: number ) {
  return unitsAvailable > 0 && unitsAvailable > reorderAlertQuantity
}

function isNotAvailable( unitsAvailable: number ) {
  return !checkValue( unitsAvailable )
}

onMounted(() => getProductData())

</script>

<template>

  <div data-element="product-details-modal" class="md:w-[48rem] h-full bg-core-20 grid grid-rows-[max-content_1fr_max-content] overflow-hidden overflow-y-auto">

    <div data-element="modal-header" class="w-full h-12 sticky top-0 z-1 grid grid-cols-[1fr_max-content] items-center bg-core-20 border-b border-core-30">

      <div class="text-sm font-medium px-4 flex gap-x-3 items-center">

        <Icon data-element="product-icon" class="text-base text-main" name="product" />

        <span class="flex items-center">
          <span class="text-lg pr-2">Product:</span>
          <span v-if="product" data-element="product-sku" class="text-main">[{{ product?.sku }}]</span>
          <Icon v-else name="loading" />
        </span>

      </div>

      <Button
        data-button="close"
        mode="naked"
        type="box"
        size="l"
        :icon="{
          name: 'close',
          size: 'm',
        }"
        @click="$emit('close')"
      />

    </div>

    <Transition name="source-form" mode="out-in">

      <div v-if="pending" data-element="loading-state" class="p-10 h-full flex items-center justify-center space-x-4">
        <p class="text-sm">
          Loading Product Data
        </p>
        <Icon name="loading" />
      </div>

      <div v-else-if="product" data-element="product-content" class="w-full h-full bg-core-10">

        <!-- Inventory Section -->
        <div data-element="inventory-section" class="px-4 py-8 md:p-8 bg-core-20 border-b-1 border-b-core-30">

          <h2 data-element="inventory-heading" class="text-lg pb-[1.375rem]">
            Inventory
          </h2>

          <div
            v-if="!product.kitComponents"
            data-element="standard-inventory"
            class="w-full overflow-x-auto hide-scroll-bar"
            :class="{
              'md:grid md:grid-cols-3 gap-3 flex flex-col gap-x-3': liveProduct?.availableStockLevels,
            }"
          >

            <div v-if="!liveProduct?.availableStockLevels" class="bg-tag-background-blue px-4 py-2 rounded-[0.313rem] border-tag-border-blue text-tag-color-blue flex items-center gap-3 text-sm">
              <span><Icon name="info" size="m" /></span> Inventory information for this product is currently unavailable.
            </div>

            <div
              v-for="distributor in liveProduct?.availableStockLevels"
              v-else
              :key="distributor.facilityCode"
              :data-element="`facility-${distributor.facilityCode}`"
              class="md:w-full w-full px-3 py-2 h-auto flex flex-col justify-between rounded-md"
              :class="{
                'bg-tag-background-green border border-tag-border-green': isSufficientInventory(distributor.unitsAvailable, product.reorderAlertQuantity),
                'bg-tag-background-orange border border-tag-border-orange': isLowInventory(distributor.unitsAvailable, product.reorderAlertQuantity),
                'bg-tag-background-red border border-tag-border-red': isOutOfStock(distributor.unitsAvailable),
                'bg-tag-background-gray border border-tag-border-gray': isNotAvailable(distributor.unitsAvailable),
              }"
            >

              <div class="flex justify-between w-auto">

                <p class="text-xs font-medium flex">
                  <span
                    v-if="isSufficientInventory(distributor.unitsAvailable, product.reorderAlertQuantity)"
                    :data-element="`facility-name-${distributor.facilityCode}`"
                    class="text-tag-color-green text-ellipsis line-clamp-1 flex items-center"
                  >{{ distributor.facilityName }}</span>
                  <span
                    v-if="isLowInventory(distributor.unitsAvailable, product.reorderAlertQuantity)"
                    :data-element="`facility-name-${distributor.facilityCode}`"
                    class="text-tag-color-orange text-ellipsis line-clamp-1 flex items-center"
                  >{{ distributor.facilityName }}</span>
                  <span
                    v-if="isOutOfStock(distributor.unitsAvailable)"
                    :data-element="`facility-name-${distributor.facilityCode}`"
                    class="text-tag-color-red text-ellipsis line-clamp-1 flex items-center"
                  >{{ distributor.facilityName }}</span>
                  <span
                    v-if="isNotAvailable(distributor.unitsAvailable)"
                    :data-element="`facility-name-${distributor.facilityCode}`"
                    class="text-tag-color-gray text-ellipsis line-clamp-1 flex items-center"
                  >{{ distributor.facilityName }}</span>
                </p>

                <p
                  :data-element="`facility-stock-${distributor.facilityCode}`"
                  class="flex" :class="{
                    'text-tag-color-green font-medium': isSufficientInventory(distributor.unitsAvailable, product.reorderAlertQuantity),
                    'text-tag-color-orange font-medium': isLowInventory(distributor.unitsAvailable, product.reorderAlertQuantity),
                    'text-tag-color-red font-medium': isOutOfStock(distributor.unitsAvailable),
                    'text-tag-color-gray text-xs': isNotAvailable(distributor.unitsAvailable),
                  }"
                >
                  {{ checkValue(distributor.unitsAvailable) ? distributor.unitsAvailable : '/' }}
                </p>

              </div>

              <p
                :data-element="`facility-code-${distributor.facilityCode}`"
                class="flex items-start text-xs" :class="{
                  'text-tag-color-green': isSufficientInventory(distributor.unitsAvailable, product.reorderAlertQuantity),
                  'text-tag-color-orange': isLowInventory(distributor.unitsAvailable, product.reorderAlertQuantity),
                  'text-tag-color-red': isOutOfStock(distributor.unitsAvailable),
                }"
              >
                {{ distributor.facilityCode }}
              </p>

            </div>

          </div>

          <div v-else data-element="kit-inventory" class="w-full gap-3 flex flex-col">

            <div v-for="facility in facilitiesList" :key="facility.id" :data-element="`kit-facility-${facility.id}`">

              <div data-element="facility-header" class="px-4 py-2 bg-tag-background-purple border border-tag-border-purple rounded-[0.313rem] text-tag-color-purple text-xs">
                <span data-element="facility-name" class="font-medium pr-[0.375rem]">{{ facility.name }}</span>
                <span data-element="facility-id">{{ facility.id }}</span>
              </div>

              <div class="py-[0.625rem]">
                <div
                  v-for="(kit, index) in product.kitComponents"
                  :key="kit.id"
                  :data-element="`kit-component-${kit.id}`"
                  class="h-[2.188rem] px-4  border-subtle-00 border-dashed flex items-center justify-between text-xs"
                  :class="{
                    'border-b-none': index === product.kitComponents.length - 1,
                    'border-b': index !== product.kitComponents.length - 1,
                  }"
                >
                  <span data-element="kit-sku">{{ kit.sku }}</span>
                  <span data-element="kit-stock"> Stock:
                    <span
                      :data-element="`kit-stock-value-${kit.id}-${facility.id}`"
                      :class="{
                        'text-error': isOutOfStock(kit?.availableByFacility?.find(f => f.facilityCode === facility.id)?.unitsAvailable) || isNotAvailable(kit?.availableByFacility?.find(f => f.facilityCode === facility.id)?.unitsAvailable),
                        'text-success': !isOutOfStock(kit?.availableByFacility?.find(f => f.facilityCode === facility.id)?.unitsAvailable) && !isNotAvailable(kit?.availableByFacility?.find(f => f.facilityCode === facility.id)?.unitsAvailable),
                      }"
                    >{{ checkValue(kit?.availableByFacility?.find(f => f.facilityCode === facility.id)?.unitsAvailable) ? kit?.availableByFacility?.find(f => f.facilityCode === facility.id)?.unitsAvailable : '/' }}</span>
                  </span>
                </div>
              </div>

            </div>

          </div>

        </div>

        <!-- Bottom Section -->
        <div data-element="details-container" class="px-4 py-8 md:py-0 md:px-8">

          <!-- Details section -->
          <div data-element="product-details-section" class="md:pt-8">

            <h2 data-element="details-heading" class="text-lg pb-[1.375rem]">
              Details
            </h2>

            <InventoryProductDetailsRow attribute="SKU Name" :value="product.sku" />

            <InventoryProductDetailsRow attribute="Title" :value="product.title" />

            <InventoryProductDetailsRow attribute="Description" :value="product.description" />

            <InventoryProductDetailsRow attribute="Supplier" :value="product.supplier" :hide-border="true" />

          </div>

          <!-- Divider -->
          <div data-element="divider" class="w-full h-px bg-core-30 my-[1.625rem]" />

          <!-- Attributes section -->
          <div data-element="attributes-section" class="md:py-8 md:pt-0">

            <h2 data-element="attributes-heading" class="text-lg pb-[1.375rem]">
              Attributes
            </h2>

            <InventoryProductDetailsRow data-element="height-row" attribute="Height" :value="product.height" />

            <InventoryProductDetailsRow data-element="width-row" attribute="Width" :value="product.width" />

            <InventoryProductDetailsRow data-element="length-row" attribute="Length" :value="product.length" />

            <InventoryProductDetailsRow data-element="cubic-feet-row" attribute="Cubic Feet" :value="product.cubicFeet" />

            <InventoryProductDetailsRow data-element="size-row" attribute="Size" :value="product.size" />

            <InventoryProductDetailsRow data-element="color-row" attribute="Color" :value="product.color" :hide-border="true" />

          </div>

        </div>

      </div>

    </Transition>

    <div data-element="modal-footer" class="w-full h-12 sticky bottom-0 z-1 md:hidden flex justify-end bg-core-20 border-t border-core-30">

      <Button
        data-button="cancel"
        size="auto"
        mode="naked"
        class="w-[9.375rem]"
        @click="$emit('close')"
      >
        Cancel
      </Button>

    </div>

  </div>

</template>
