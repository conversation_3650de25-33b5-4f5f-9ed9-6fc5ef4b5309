<script setup lang="ts">

import { useRouter } from 'vue-router'
import { msalInstance } from '@lib/auth/scripts/authConfig'
import { onMounted, ref } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

const router          = useRouter()
const errorMessage    = ref<string | null>( null )
const errorDetails    = ref<string | null>( null )
const pendingRedirect = ref( false )

enum AuthError {
  ExpiredToken = 'AADB2C90208'
}

onMounted( async () => {

  const hashParams       = new URLSearchParams( window.location.hash.replace( /^#/, '' ))
  const error            = hashParams.get( 'error' )
  const errorDescription = decodeURIComponent( hashParams.get( 'error_description' ) ?? '' )
  const errorCode        = errorDescription.split( ':' )[0]

  if ( error ) {

    switch ( errorCode ) {

      case AuthError.ExpiredToken:

        errorMessage.value = 'Your session has expired. Please log in again.'
        break

      default:

        errorMessage.value = 'An authentication error occurred. Please try logging in again.'

    }

    errorDetails.value = errorDescription
    return

  }

  try {
    await msalInstance.handleRedirectPromise()
    router.push( import.meta.env.VITE_B2C_AFTER_LOGIN_REDIRECT_ROUTE )
  }

  catch ( error ) {
    console.error( 'ERROR', error )
    errorMessage.value = 'An unexpected error occurred. Please try again.'
  }

})

async function retryLogin() {

  pendingRedirect.value = true

  await router.push( import.meta.env.VITE_B2C_AFTER_LOGIN_REDIRECT_ROUTE )

  pendingRedirect.value = false

}

</script>

<template>

  <div class="h-full grid place-items-center p-4">

    <div class="flex flex-col items-center justify-center space-y-4">

      <Icon v-if="!errorMessage" name="loading" />

      <p v-if="!errorMessage">
        Redirecting...
      </p>

      <div v-if="errorMessage" class="flex flex-col items-center gap-y-4">

        <p class="text-lg font-medium text-error">
          {{ errorMessage }}
        </p>

        <p v-if="errorDetails" class="text-sm text-core-70">
          {{ errorDetails }}
        </p>

        <Button class="px-4 w-fit" :pending="pendingRedirect" @click="retryLogin">
          Retry Login
        </Button>

      </div>

    </div>

  </div>

</template>
