import { toRaw } from 'vue'
import { blockPageUpdate, viewSetup } from '@lib/scripts/viewSetup'
import { useDateFormat, useMediaQuery } from '@vueuse/core'

import type { DateFormat } from '@lib/types/inputTypes'
import type { LocationQuery } from 'vue-router'
import type { AxiosResponseHeaders } from 'axios'

/**
 * Use a breakpoint to determine the current screen size.
 * @param {LooseAutoComplete<TwMediaQueries>} breakpoint - The breakpoint to use.
 * @returns A boolean indicating whether the current screen size matches the breakpoint.
 */
export function useBreakpoint( breakpoint: LooseAutoComplete<TwMediaQueries> ) {

  const breakpoints: Record<TwMediaQueries, string> = { // --------------------- Define the breakpoints for the media queries
    'sm':  '640px',
    'md':  '768px',
    'lg':  '1024px',
    'xl':  '1280px',
    '2xl': '1536px',
  }

  const key = breakpoints[breakpoint as TwMediaQueries] // -------------------- Get the key for the breakpoint from the breakpoints map.
  const mediaQuery = key ? `(min-width: ${key})` : String( breakpoint ) // ---- If there is no key, use the breakpoint param as a whole string. ex: '(max-width: 640px)'

  return useMediaQuery( mediaQuery ).value

}

/**
 * Saves a document file.
 *
 * @param stream - The file data as an ArrayBuffer.
 * @param fileName - The name of the file.
 * @param fileOptions - The options for the file.
 */
export function saveFile( stream: ArrayBuffer | Blob, fileName: string, fileOptions?: FilePropertyBag ) {

  // Create a new File object with the given data and name

  const file = new File( [ stream ], fileName, fileOptions || null )

  // Create a new anchor element to simulate a download link

  const anchorElement = document.createElement( 'a' )

  // Create a URL for the file data

  const fileUrl = window.URL.createObjectURL( file )

  // Set the href and download attributes of the anchor element

  anchorElement.href = fileUrl
  anchorElement.target = '_blank'
  anchorElement.download = fileName

  // Simulate a click on the anchor element to trigger the download

  anchorElement.click()

  // Revoke the URL object to free up memory

  window.URL.revokeObjectURL( fileUrl )

}

/**
 * Throttles the execution of a given function
 * @param fn - The function to be throttled
 * @param wait - The time to wait before allowing the function to be called again
 * @returns A new function that will throttle the execution of the original function
 */
export function throttle( fn: () => void, wait: number = 300 ) {

  // Declare variables to keep track of throttling state and timing

  let inThrottle: boolean
  let lastFn: ReturnType<typeof setTimeout>
  let lastTime: number

  // Return a new function

  return function ( this: any ) {

    // Save the context and arguments of the function

    const args = arguments // eslint-disable-line prefer-rest-params
    const context = this // eslint-disable-line ts/no-this-alias

    // If not in throttled state, execute the function and update timing

    if ( !inThrottle ) {
      fn.apply( context, args )
      lastTime = Date.now()
      inThrottle = true
    }

    // If in throttled state, clear the last timeout and set a new one based on the remaining time

    else {

      clearTimeout( lastFn )

      lastFn = setTimeout(() => {

        if ( Date.now() - lastTime >= wait ) {
          fn.apply( context, args )
          lastTime = Date.now()
        }

      }, Math.max( wait - ( Date.now() - lastTime ), 0 ))

    }
  }
}

/**
 * Pluralizes a string based on a count
 * @param entry - The string to pluralize
 * @param count - The count to determine the pluralization
 * @returns The pluralized string
 */
export function pluralize( entry: string, count: number ): string {

  const pEntry  = entry[entry.at( -1 )] === 's' ? entry : `${entry}s`
  const sEntry  = entry[entry.at( -1 )] === 's' ? entry.slice( 0, -1 ) : entry

  return count === 1 ? sEntry : pEntry

}

/**
 * Capitalizes string
 * @param string
 * @returns string
 */
export function capitalize( string: string ): string {
  if ( string )
    return string.charAt( 0 ).toUpperCase() + string.slice( 1 )
}

/**
 * Decapitalises string
 * @param string
 * @returns string
 */
export function decapitalize( string: string ): string {
  if ( string )
    return string.charAt( 0 ).toLowerCase() + string.slice( 1 )
}

/**
 * Decapitalises Object
 * @param entry Object
 * @returns string
 */
export function decapitalizeObject<T>( entry: T ) {

  const newObject = {} as DecapitalizeKeys<T>

  Object.keys( entry ).forEach(( key ) => {
    const lowerCaseKey = decapitalize( key )
    newObject[lowerCaseKey] = entry[key]
  })

  return newObject
}

/**
 * Converts a hexadecimal color string to its RGB equivalent.
 * @param {string} hex The hexadecimal color string in the format of #RRGGBB.
 * @returns {{red: number, green: number, blue: number}} The RGB equivalent of the hexadecimal color string.
 */
export function hexToRgb( hex: string ): { red: number, green: number, blue: number } {

  /**
   * The regular expression is used to match a hexadecimal color string of the format #RRGGBB
   * where RR, GG, and BB can be any hexadecimal digit (case-insensitive).
   * The match is stored in the variable `m`.
   */

  const match = hex.replace(
    /^#?([a-f\d])([a-f\d])([a-f\d])$/i, // --------------- The regex to match the hex color
    ( _m, r, g, b ) => `#${r}${r}${g}${g}${b}${b}` // ---- The replacement string
  ).substring( 1 ).match( /.{2}/g ) // ------------------- Split the hex color string into an array of strings

  /**
   * The function `map` transforms the array of strings into an array of numbers.
   * The function `parseInt` parses each string as a base 16 (hexadecimal) integer.
   */

  const rgb = match.map( x => Number.parseInt( x, 16 ))

  /**
   * The function `reduce` is used to create an object with the keys `red`, `green`, and `blue`.
   * The function `Object.assign` is used to merge the `rgb` array with the object `{ red: 0, green: 1, blue: 2 }`
   * which provides the correct indices for each key in the resulting object.
   */

  return Object.keys({ red: 0, green: 1, blue: 2 }).reduce(
    ( result, key, index ) => Object.assign( result, { [key]: rgb[index] }), // ---- Merge the object with the key-value pair
    { red: 0, green: 1, blue: 2 } // ----------------------------------------------- The initial value of the accumulator
  )
}

/**
 * Converts an RGB color value to a hexadecimal color string.
 *
 * @param {number} r - The red component of the RGB color to be converted.
 * @param {number} g - The green component of the RGB color to be converted.
 * @param {number} b - The blue component of the RGB color to be converted.
 * @returns {string} The hexadecimal color string in the format of #RRGGBB.
 *
 * The function `map` is used to transform the three RGB components into an array
 * of strings. Each string is generated using the `toString(16)` method which
 * converts a number to a string in base 16 (hexadecimal).
 *
 * The function `padStart(2, '0')` is used to pad each string with leading zeros
 * so that each string is two characters long. This is done so that each string
 * in the resulting array has a consistent length and can be joined together
 * into a single hexadecimal color string using the `join` method.
 *
 * The function `join('')` is used to join the array of strings into a single
 * string using an empty string as the separator between each string.
 */
export function rgbToHex( r: number, g: number, b: number ): string {
  return `#${[ r, g, b ].map( x => x.toString( 16 ).padStart( 2, '0' )).join( '' )}`
}

/**
 * Beautifies a string by capitalizing the first letter of each word
 * and removing any leading or trailing white spaces.
 *
 * @param {string} string - The input string to be beautified.
 * @returns {string} - The beautified string.
 */
export function beautifyString( string: string ): string {

  // Replace each uppercase letter with a space followed by the uppercase letter.

  const spacedString = string.replace( /([A-Z])/g, ' $1' )

  // Remove any leading or trailing white spaces.

  const trimmedString = spacedString.trim()

  // Capitalize the first letter of each word.

  const beautifiedString = capitalize( trimmedString )

  return beautifiedString

}

/**
 * Grows a number to a target value and calls a callback with the current value.
 * @param target - The target value to grow the number to.
 * @param callback - The callback function to be called with the current value.
 */
export function growNumberToTarget( target: number, callback: ( value: number ) => void ): void {

  // If the target is 0, immediately call the callback with 0 and return

  if ( !target ) {
    callback( 0 )
    return
  }

  const duration = 1800 // Set the duration for the growth animation

  let counter = 0 // Initialize the counter

  const timeStart = Date.now() // Record the start time

  // Define the function to update the counter value and call the callback

  const count = () => {

    let elapsed = Date.now() - timeStart // Calculate the elapsed time

    // Limit the elapsed time to the duration

    if ( elapsed > duration )
      elapsed = duration

    // Calculate the fraction of time elapsed and the step towards the target

    const fraction = elapsed / duration
    const step = fraction * target

    counter = Math.trunc( step ) // Update the counter value

    callback( counter ) // Call the callback with the updated counter value

    // If the animation is not finished, request the next frame for the update

    if ( elapsed < duration )
      requestAnimationFrame( count )

  }

  count() // Start the growth animation

}

/**
 * Formats a number value as a currency string.
 *
 * @param value - The number value to format.
 * @param options - The formatting options.
 * @returns The formatted currency string.
 */
export function formatCurrency( value: number, options?: Intl.NumberFormatOptions ): string {

  const formatCurrencyDefaultOptions: Intl.NumberFormatOptions = {
    style:                 'currency',
    currency:              'USD',
    maximumFractionDigits: 2,
    minimumFractionDigits: 2
  }

  options = { ...formatCurrencyDefaultOptions, ...options }

  return Number( value ).toLocaleString( 'en-US', options )

}

/**
 * Sanitizes a date by reformating the date or removing the offset information.
 * @param {Date | string} date - The date to be formatted.
 * @returns {Date | string} - Sanitized date.
 */
export function sanitizeDate( date: Date | string ): Date | string {

  if ( !date )
    return date

  if ( date instanceof Date )
    return date

  // If the date is a string, sanitize it by removing the offset information.
  // The date string is expected to be in the format 'YYYY-MM-DDTHH:MM:SS[-+]00:00'.
  // The date string is split into two parts: the date and the offset.
  // The date part is returned without the offset.

  if ( date.match( /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}/ ))
    return date.split( /^(.+)([+-]\d{2}:\d{2}|Z)$/ )[1]

  // If the date is a string, sanitize it by reformatting the date.
  // The date string is expected to be in the format 'YYYY-MM-DD'.
  // The date string is returned with the '-' replaced by '.', ex: YYYY.MM.DD.

  if ( date.match( /\d{4}-\d{2}-\d{2}$/g ))
    return date.replace( /-/g, '.' )

  return date

}

/**
 * Formats the given Date object or date string into a custom date format.
 * @param {Date | string} date - The Date object or date string to be formatted.
 * @param {DateFormat} format - The custom date format to use.
 * @param {boolean} sanitize - If true, the date is sanitized before formatting. default is true.
 * @returns {string} - Date object or the formatted date string.
 */
export function formatDate( date: Date | string, format: LooseAutoComplete<DateFormat>, sanitize: boolean = true ): string {

  const sanitizedDate = sanitize
    ? sanitizeDate( date )
    : date

  let formattedDate: string = useDateFormat( new Date( sanitizedDate ), String( format )).value

  switch ( format ) {

    case 'ISO-string': // -------------- Formats the date to ISO string with time and offset included.

      formattedDate = new Date( date ).toISOString()
      break

    case 'UTC-date': // ---------------- Formats the date to ISO string with date only: YYYY-MM-DD

      formattedDate = `${new Date( date ).toISOString().split( 'T' )[0]}`
      break

    case 'UTC-date-time': // ----------- Formats the date to ISO string with date and fixed time only: YYYY-MM-DDT00:00:00

      formattedDate = `${new Date( date ).toISOString().split( 'T' )[0]}T00:00:00`
      break

  }

  // If no case matches the format, returns the
  // formatted string with the custom format provided.

  return formattedDate

}

/**
 * Describes the 24 hour time options.
 */
interface TimeOptions24Hours {
  h:  number /** -- from 0 to 24 */
  m:  number /**  - from 0 to 60 */
  s?: number /** - from 0 to 60 */
}

/**
 * Describes the 12 hour time details.
 */
interface TimePayload {
  h:        number /** ----------- The hours - from 1 to 12 */
  m:        number /** ----------- The minutes */
  s:        number /** ----------- The seconds */
  date:     Date /** ---------- The date object */
  period:   'AM' | 'PM' /** - The period of the time: AM or PM */
  dateTime: string /** ---- The ISO string of the date object */
}

/**
 * Converts a Date object or 24 hour time options to a 12 hour time details.
 *
 * @param {Date | TimeOptions} entry - The Date object or time options to be formatted.
 * @returns {TimePayload} - The 12 hour time details.
 */
export function get12HourTimeDetails( entry: Date | TimeOptions24Hours ): TimePayload {

  if ( !entry )
    return null

  if ( entry instanceof Date ) {

    const h = entry.getHours()
    const m = entry.getMinutes()
    const s = entry.getSeconds()
    const period = h >= 12 ? 'PM' : 'AM'

    return {
      h:        h % 12 || 12,
      m,
      s,
      date:     entry,
      period,
      dateTime: entry.toISOString()
    }

  }

  const { h, m, s } = entry

  const date = new Date()

  date.setHours( h )
  date.setMinutes( m )
  date.setSeconds( s || 0 )

  return {
    h:        h % 12 || 12,
    m,
    s:        s || 0,
    date,
    period:   date.getHours() === 0 ? 'AM' : h >= 12 ? 'PM' : 'AM',
    dateTime: date.toISOString()
  }

}

/**
 * Describes the 12 hour time options.
 */
interface TimeOptions12Hours {
  h:      number /** ----------- from 0 to 12 */
  m:      number /**  ---------- from 0 to 60 */
  s?:     number /** ---------- from 0 to 60 */
  period: 'AM' | 'PM' /** - The period of the time: AM or PM */
}

export function get24HourTimeDetails( entry: TimeOptions12Hours ): { h: number, m: number, s: number } {

  if ( !entry )
    return

  const { h, m, s, period } = entry

  const hours = h === 12 ? period === 'AM' ? 0 : 12 : period === 'AM' ? h : h + 12

  return { h: hours, m, s: s || 0 }

}

/**
 * Checks if a value is valid based on certain criteria.
 * @param value - The value to be checked.
 * @param options - Optional parameters for the check.
 * @param options.partial - If true, allows partial values for objects.
 * @param options.checkBoolean - If true, allows boolean values.
 * @returns True if the value is valid, false otherwise.
 */
export function checkValue<T extends any[] | Record<string, any> | string | number | boolean>( value: T, options?: { partial?: boolean, checkBoolean?: boolean }): boolean {

  const checkBool = typeof options?.checkBoolean === 'boolean'
    ? options.checkBoolean
    : true

  const isBool = typeof value === 'boolean' // ------------------------------------------------------- Check if the value is a boolean

  const isArray = Array.isArray( value ) // ---------------------------------------------------------- Check if the value is an array

  const isString = typeof value === 'string' // ------------------------------------------------------ Check if the value is a string

  const isObject = typeof value === 'object' && value !== null // ------------------------------------ Check if the value is an object

  return ( isArray || isString )
    ? value.length > 0 // ---------------------------------------------------------------------------- Check if the array or string has a length greater than 0
    : isBool // -------------------------------------------------------------------------------------- Check if the value is a boolean
      ? checkBool // --------------------------------------------------------------------------------- If checkBoolean is true, return the value as is, else return true
        ? value
        : true
      : isObject // ---------------------------------------------------------------------------------- Check if the value is an object
        ? ( options?.partial // ---------------------------------------------------------------------- Check if the partial option is true
          ? Object.values( value ).length > 0 && Object.values( value ).some( key => !!key ) // ------ Check if some value is truthy
          : Object.values( value ).length > 0 && Object.values( value ).every( key => !!key )) // ---- Check if all values are truthy
        : ( value !== null && value !== undefined ) // ----------------------------------------------- Check if the value is not null or undefined

}

/**
 * Check if an element is scrollable.
 * @param element - The element to check.
 * @returns True if the element is scrollable, false otherwise.
 */
export function isScrollable( element: HTMLElement ): boolean {

  // Check if the element has scrollable content

  const hasScrollableContent = element.scrollHeight > element.clientHeight

  // Check if the element has overflow hidden

  const overflowYStyle = window.getComputedStyle( element ).overflowY
  const isOverflowHidden = overflowYStyle.includes( 'hidden' )

  // Return true if the element has scrollable content and does not have overflow hidden

  return hasScrollableContent && !isOverflowHidden

}

/**
 * Recursive function that returns the scrollable parent element of a given element.
 * If the element is null or the document body, returns the document body itself.
 * If the element is scrollable, returns the element.
 * Otherwise, recursively calls itself with the parent element until a scrollable parent is found.
 * @param element - The element to find the scrollable parent of.
 * @returns The scrollable parent element or the document body if no scrollable parent is found.
 */
export function getScrollableParent( element: HTMLElement ): HTMLElement {

  // If the element is null or the document body, return the document body itself

  if ( !element || element === document.body )
    return document.body

  // If the element is scrollable, return the element

  if ( isScrollable( element ))
    return element

  // Recursively call the function with the parent element

  return getScrollableParent( element.parentNode as HTMLElement )

}

/**
 * Finds the first focusable element within a given element and sets the focus on it.
 * @param containerElement - The element to search within.
 */
export function findAndFocusElement( containerElement: HTMLElement ) {

  // Return early if the element is null or undefined

  if ( !containerElement )
    return

  // Find all focusable inputs within the element

  const focusableInputs = containerElement.querySelectorAll(
    'label:not([disabled="true"]), input:not([disabled="true"]), textarea:not([disabled="true"]), select:not([disabled="true"])'
  )

  // Find all focusable non-input elements within the element

  const focusableNonInputs = containerElement.querySelectorAll(
    'button:not([disabled="true"]), [href]:not([disabled="true"]), [tabindex]:not([tabindex="-1"])'
  )

  // Combine the focusable inputs and focusable non-inputs

  const focusable = [ ...focusableInputs, ...focusableNonInputs ]

  // If there are focusable elements, set the focus on the first one

  if ( focusable.length > 0 ) {
    const firstInput = focusable[0] as HTMLElement
    firstInput.focus()
  }

}

/**
 * Generates a unique ID consisting of random symbols and a random number.
 * @returns {string} The generated unique ID.
 */
export function generateUniqueId(): string {

  // Define an array of symbols to choose from

  const symbols = [
    'a',
    'A',
    'b',
    'B',
    'c',
    'C',
    'd',
    'D',
    'e',
    'E',
    'f',
    'F',
    'g',
    'G',
    'h',
    'H',
    'i',
    'I',
    'j',
    'J',
    'k',
    'K',
    'l',
    'L',
    'm',
    'M',
    'n',
    'N',
    'o',
    'O',
    'p',
    'P',
    'q',
    'Q',
    'r',
    'R',
    's',
    'S',
    't',
    'T',
    'u',
    'U',
    'v',
    'V',
    'w',
    'W',
    'x',
    'X',
    'y',
    'Y',
    'z',
    'Z',
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9'
  ]

  // Initialize an empty string for the random symbols

  let randomString: string = ''

  // Generate a random symbol and add it to the random string for each symbol in the array

  for ( let i = 0; i < symbols.length; i++ ) {

    const randomSymbol = symbols[Math.floor( Math.random() * symbols.length )]

    if ( randomSymbol )
      randomString += randomSymbol

  }

  // Create a random number by multiplying two random numbers and converting it to a string

  const randomNumber = Math.floor( Math.random() * Math.floor( Math.random() * Date.now())).toString()

  // Remove any whitespace from the random string and concatenate it with the random number

  const uniqueId = `${randomString.replace( /\s/g, '' )}-${randomNumber}`

  return uniqueId

}

/**
 * Generate a random number within the specified range.
 *
 * @param min - The minimum value of the range (inclusive).
 * @param max - The maximum value of the range (inclusive).
 * @returns A random number within the specified range.
 */
export function generateRandomNumber( min: number, max: number ) {
  return Math.floor( Math.random() * ( max - min + 1 )) + min
}

/**
 * Universal search util
 * @param data - Array of objects to search
 * @param query - Search query
 * @param matchProperty - Property to match the query against
 * @returns - Filtered array of objects
 */
export function searchModel<T extends Record<string, any>>( data: T[], query: string, matchProperty: keyof T | Array<keyof T> ) {

  if ( !query )
    return data

  // If the matchProperty is an array, match the
  // query against all properties in the array.

  if ( Array.isArray( matchProperty )) {

    return data.filter(( item: T ) => {

      const properties = []

      matchProperty.forEach( prop => properties.push( String( item[prop] )))

      if ( properties.some( p => p.match( new RegExp( query.replace( /[.*+?^${}()|[\]\\]/g, '\\$&' ), 'i' ))))
        return item

      return undefined

    })
  }

  // If the matchProperty is a string, match the query

  else { return data.filter(( item: T ) => item[matchProperty].match( new RegExp( query.replace( /[.*+?^${}()|[\]\\]/g, '\\$&' ), 'i' ))) }

}

interface OnSwipeOptions {
  threshold?:   number
  direction?:   'left' | 'right'
  allowedTime?: number
}

/**
 * Handles the swipe event on the given element.
 *
 * @param {HTMLDivElement} element - The element to handle the swipe event on.
 * @param {() => void} callback - The callback function to execute when the swipe event occurs.
 * @param {OnSwipeOptions} options - The options for the swipe event.
 * @param {number} options.threshold - The threshold distance for a valid swipe event (default: 100).
 * @param {string} options.direction - The direction of the valid swipe event ('left' or 'right') (default: 'left').
 * @param {number} options.allowedTime - The allowed time for a valid swipe event (default: 750).
 */
export function onSwipe( element: HTMLDivElement, callback: () => void, options?: OnSwipeOptions ) {

  // Return if element is falsy

  if ( !element )
    return

  // Initialize variables

  let startX: number = 0
  let distance: number = 0
  let startTime: number = 0
  let elapsedTime: number = 0

  // Get options or default values

  const threshold: number = options?.threshold ?? 100
  const direction: OnSwipeOptions['direction'] = options?.direction ?? 'left'
  const allowedTime: number = options?.allowedTime ?? 750

  // Add touchstart event listener

  const handleTouchStart = ( e: TouchEvent ) => {

    startX = e.touches[0].clientX
    distance = 0
    startTime = new Date().getTime()
    element.style.transition = 'none'

  }

  element.addEventListener( 'touchstart', handleTouchStart )

  // Add touchmove event listener

  const handleTouchMove = ( e: TouchEvent ) => {

    distance = e.changedTouches[0].pageX - startX

    // Check if swipe direction matches the specified direction

    const isRight = distance > 1

    if (( direction === 'left' && isRight ) || ( direction === 'right' && !isRight ))
      return

    element.style.transform = `translateX(${e.touches[0].clientX - startX}px)`

  }

  element.addEventListener( 'touchmove', handleTouchMove )

  // Add touchend event listener

  const handleTouchEnd = ( e: TouchEvent ) => {

    // Apply transition and reset transform

    element.style.transition = 'all 0.3s ease'
    element.style.transform = 'translateX(0px)'

    distance = e.changedTouches[0].pageX - startX
    elapsedTime = new Date().getTime() - startTime

    // Check if elapsed time and distance meet the criteria for a valid swipe event

    const timeOk = elapsedTime <= allowedTime
    const distOk = direction === 'right' ? distance >= threshold : Math.abs( distance ) >= threshold

    // Check if swipe direction matches the specified direction

    const isRight = distance > 1

    if (( direction === 'left' && isRight ) || ( direction === 'right' && !isRight )) {

      element.removeEventListener( 'touchstart', handleTouchStart )
      element.removeEventListener( 'touchmove', handleTouchMove )
      element.removeEventListener( 'touchend', handleTouchEnd )

      return

    }

    // Call the callback function if the swipe event is valid

    if ( timeOk && distOk ) {

      callback()

      element.removeEventListener( 'touchstart', handleTouchStart )
      element.removeEventListener( 'touchmove', handleTouchMove )
      element.removeEventListener( 'touchend', handleTouchEnd )

    }

  }

  element.addEventListener( 'touchend', handleTouchEnd )

}

/**
 * Merges two objects by recursively combining their properties.
 * If a property is an object in both input objects, it merges them recursively.
 * If a property is a string in both input objects, the value from the second object is used.
 * If a property exists only in one object, it is included in the merged object.
 * @param firstObject The first object to merge.
 * @param secondObject The second object to merge.
 * @returns A new object with properties merged from both input objects.
 */
export function mergeObjects<FirstObject, SecondObject>( firstObject: FirstObject, secondObject: SecondObject ): ( FirstObject & SecondObject ) | SecondObject {

  // Base case: if either first object or second object is not an object, return the value from the second object.

  if ( typeof firstObject !== 'object' || typeof secondObject !== 'object' || firstObject === null || secondObject === null )
    return secondObject

  // Start with a copy of the first object

  const mergedObject: any = { ...firstObject }

  // Iterate through keys in the second object

  for ( const key in secondObject ) {

    if ( Object.prototype.hasOwnProperty.call( secondObject, key )) {

      // If the value in the second object is also an object, recursively merge

      if ( typeof secondObject[key] === 'object' && secondObject[key] !== null ) {

        // If the key exists in the first object and is also an object, merge recursively
        // Otherwise, simply assign the value from the second object

        if ( typeof mergedObject[key] === 'object' && mergedObject[key] !== null )
          mergedObject[key] = mergeObjects( mergedObject[key], secondObject[key] )

        else mergedObject[key] = { ...secondObject[key] }

      }

      else if (
        typeof secondObject[key] === 'string'
        && key in mergedObject
        && typeof mergedObject[key] === 'string'
      ) {

        // If the value is a string and exists in both objects, update it

        mergedObject[key] = secondObject[key]

      }

      else {

        // If the value is not an object or is a new key, directly assign from the second object

        mergedObject[key] = secondObject[key]

      }

    }

  }

  return mergedObject

}

/**
 * Compares two objects to check if they are equal.
 * @param firstObject The first object to compare.
 * @param secondObject The second object to compare.
 * @returns true if the objects are equal, false otherwise.
 */
export function compareObjects( firstObject: any, secondObject: any ): boolean {

  if ( !firstObject || !secondObject )
    return false

  if ( typeof firstObject !== 'object' || typeof secondObject !== 'object' )
    return false

  if ( Object.keys( firstObject ).length !== Object.keys( secondObject ).length )
    return false

  for ( const key in firstObject ) {

    const isKeyObject = typeof firstObject[key] === 'object' && firstObject[key] !== null

    if ( !isKeyObject && ( firstObject[key] !== secondObject[key] ))
      return false

    if ( isKeyObject && !compareObjects( firstObject[key], secondObject[key] ))
      return false

  }

  return true

}

/**
 * Check which entry from the entries array has value and returns them as string.
 * If none of the entries has value then it returns null.
 * @param entries Array of strings
 */
export function handleErrors( entries: string | string[] ): string | null {

  if ( Array.isArray( entries )) {

    if ( entries.some( e => e ))
      return entries.filter( e => e !== null && e !== undefined ).join( ', ' )

    else
      return null

  }

  else if ( entries ) {
    return entries
  }

  else { return null }

}

/**
 * Takes an object and converts it's properties to
 * record of { value: property value, valid: false }
 * @param entry The object that you want to convert to validatable
 * @param pickKeys Select exclusive keys to transform and discard the rest.
 * @param omitKeys Array of object keys to pass as valid.
 * @returns New Validatable Object.
 */

export function convertObjectToValidatable<T>( entry: T, pickKeys?: Array<keyof T>, omitKeys?: Array<keyof T> ): Validatable<T> {

  const convertedObject = {} as Validatable<T>

  Object.keys( entry ).forEach(( key ) => {

    if ( pickKeys ) {

      if ( pickKeys.includes( key as keyof T )) {

        if ( omitKeys ) {

          if ( omitKeys.includes( key as keyof T ))
            convertedObject[key] = { value: entry[key], valid: true }

          else convertedObject[key] = { value: entry[key], valid: false }

        }

        else { convertedObject[key] = { value: entry[key], valid: false } }

      }

    }

    else {

      if ( omitKeys ) {

        if ( !omitKeys.includes( key as keyof T ))
          convertedObject[key] = { value: entry[key], valid: false }

        else convertedObject[key] = { value: entry[key], valid: true }

      }

      else { convertedObject[key] = { value: entry[key], valid: false } }

    }

  })

  return convertedObject

}

/**
 *
 * @param entry Converts validatable object to plain.
 * @returns New plain object.
 */

export function convertObjectToPlain<T>( entry: Validatable<T> ): T {

  const plainEntry = {} as T

  Object.keys( entry ).forEach(( key ) => {

    plainEntry[key] = entry[key].value

  })

  return plainEntry

}

/**
 * Removes all keys that have no value from object.
 * @param entry - The object from which you want to remove all the keys with no value.
 * @returns New object only with the keys that have values.
 */
export function removeEmptyKeysFromObject<T>( entry: T ): Partial<T> {

  const newObject = {} as Partial<T>

  Object.keys( entry ).forEach(( key ) => {

    if ( checkValue( entry[key] ))
      newObject[key] = entry[key]

    if ( typeof entry[key] === 'boolean' )
      newObject[key] = entry[key]

  })

  return newObject

}

/**
 * Maps the query parameters to an object.
 *
 * @param entry - The object to map the query parameters to.
 * @param queryParams - The query parameters to map.
 * @returns The object with the mapped query parameters.
 */
export function mapQueryParamsToObject<T>( entry: T, queryParams: LocationQuery ): T {

  // Iterate over each key in the entry object

  Object.keys( entry ).forEach(( key ) => {

    // Map the query parameter to the corresponding key in the entry object

    entry[key] = queryParams[key] ?? entry[key] ?? null

  })

  return entry

}

/**
 * Sanitizes the query parameters by converting their values to numbers if they are numeric strings.
 *
 * @param params - The query parameters to sanitize.
 * @returns The sanitized query parameters.
 */
export function sanitizeQueryParams<T>( params: T | LocationQuery ): T {

  if ( !params )
    return null

  const cleanParams = structuredClone( toRaw( params )) // --------- Clone the object to avoid mutating the original

  Object.keys( cleanParams ).forEach(( key ) => {

    if ( /^\d+$/.test( cleanParams[key] ))
      cleanParams[key] = Number.parseInt( cleanParams[key] ) // ---- Convert numeric strings to numbers

    if ( [ 'true', 'false' ].includes( cleanParams[key] ))
      cleanParams[key] = cleanParams[key] === 'true' // ------------ Convert boolean strings to boolean

  })

  return cleanParams as T

}

/**
 * Extracts pagination information from the headers of an Axios response.
 *
 * @param headers - The headers object from the Axios response.
 * @returns An object containing the total number of rows and the maximum number of pages.
 */
export function extractPaginationFromHeader( headers: AxiosResponseHeaders ): { total: number, maxPages: number } {

  // Get the 'x-pagination' header from the headers object

  const paginationHeader = headers['x-pagination']

  // If the 'x-pagination' header is not present, return an object with default values

  if ( !paginationHeader ) {
    return {
      total:    0,
      maxPages: 0
    }
  }

  // Parse the pagination data from the 'x-pagination' header

  const paginationData = JSON.parse( paginationHeader )

  // Extract the total number of rows and the maximum number of pages from the pagination data

  return {
    total:    paginationData?.TotalRows ?? 0,
    maxPages: paginationData?.TotalPages ?? 0
  }

}

/**
 * Creates a pagination of entries based on the given parameters.
 *
 * @param entries - The array of entries to paginate.
 * @param params - The pagination parameters.
 * @returns The paginated entries.
 */
export function createPagination<T>( entries: T[], params: BaseParams ): T[] {

  // Get the current page and page size from the parameters

  const page = params.page
  const size = params.pageSize

  // Calculate the total number of entries

  const total = entries.length

  // Calculate the start and end index of the slice

  const startSlice = size * ( page - 1 )
  const endSlice = size * page

  // Ensure the start and end indices are within the valid range

  const start = startSlice > total ? 0 : startSlice
  const end = total > endSlice ? endSlice : total

  // Return the paginated entries

  return entries.slice( start, end )

}

/*
 * ---- VIEW SETUP Utilities
 */

export { blockPageUpdate, viewSetup }
