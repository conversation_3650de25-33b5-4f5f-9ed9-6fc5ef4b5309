import { useDateFormat } from '@vueuse/core'

interface CalendarGeneratorConfig {
  day?:                number
  year?:               number
  month?:              number
  generateFromMonday?: boolean
}

export interface WeekDay {
  index:     number
  name:      string
  shortName: string
}

export interface CalendarDay {
  date:         Date
  day:          number
  year:         number
  month:        number
  isoDate:      string
  current:      boolean
  formatedDate: string
}

export interface Calendar {
  currentDay:                   number
  currentYear:                  number
  currentMonth:                 number
  daysInCurrentMonth:           CalendarDay[]
  daysRemainingInNextMonth:     CalendarDay[]
  daysRemainingInPreviousMonth: CalendarDay[]
  calendarDays:                 CalendarDay[]
  weekDays:                     WeekDay[]
}

const initialConfig: CalendarGeneratorConfig = {
  day:                null,
  year:               null,
  month:              null,
  generateFromMonday: null
}

function mergeDateWithCurrentTime( year: number, month: number, day: number, addTime: boolean = true ) {

  if ( addTime )
    return new Date( year, month, day, new Date().getHours(), new Date().getMinutes(), new Date().getSeconds(), new Date().getMilliseconds())

  return new Date( year, month, day )

}

export function generateCalendar( config: CalendarGeneratorConfig = initialConfig ): Calendar {

  // If day / month / year values are not specified
  // in the config, Create them from the current date.

  const day =     config.day ?? new Date().getDate()
  const year =    config.year ?? new Date().getFullYear()
  const month =   config.month ?? new Date().getMonth()

  // Week Days Lists from Monday and Sunday

  const weekDaysFromSunday = [
    {
      index:     0,
      name:      'Sunday',
      shortName: 'Sun'
    },
    {
      index:     1,
      name:      'Monday',
      shortName: 'Mon'
    },
    {
      index:     2,
      name:      'Tuesday',
      shortName: 'Tue'
    },
    {
      index:     3,
      name:      'Wednesday',
      shortName: 'Wed'
    },
    {
      index:     4,
      name:      'Thursday',
      shortName: 'Thu'
    },
    {
      index:     5,
      name:      'Friday',
      shortName: 'Fri'
    },
    {
      index:     6,
      name:      'Saturday',
      shortName: 'Sat'
    }
  ]

  const weekDaysFromMonday = [
    {
      index:     0,
      name:      'Monday',
      shortName: 'Mon'
    },
    {
      index:     1,
      name:      'Tuesday',
      shortName: 'Tue'
    },
    {
      index:     2,
      name:      'Wednesday',
      shortName: 'Wed'
    },
    {
      index:     3,
      name:      'Thursday',
      shortName: 'Thu'
    },
    {
      index:     4,
      name:      'Friday',
      shortName: 'Fri'
    },
    {
      index:     5,
      name:      'Saturday',
      shortName: 'Sat'
    },
    {
      index:     6,
      name:      'Sunday',
      shortName: 'Sun'
    }
  ]

  // Get index of the first day in the month - ( from 0 to 6 / where sunday = 0, saturday = 6 )
  // If generateFromMonday is true in the config, we will remap this values to monday = 0, sunday = 6

  let firstDay = new Date( year, month ).getDay()

  // If generateFromMonday is not set in the config it's value defaults to true

  const generateFromMonday = typeof config.generateFromMonday === 'boolean' ? config.generateFromMonday : true

  // Remap first day value if generateFromMonday is true

  if ( generateFromMonday )
    firstDay = ( firstDay - 1 + 7 ) % 7

  // Generate Array with all the days in the current month

  const indexOfDaysInCurrentMonth = [ ...Array.from( Array.from({ length: 32 - new Date( year, month, 32 ).getDate() }).keys()) ]

  const daysInCurrentMonth: CalendarDay[] = indexOfDaysInCurrentMonth.map( d => ({
    day:          d + 1,
    year,
    month,
    current:      true,
    date:         mergeDateWithCurrentTime( year, month, d + 1 ),
    isoDate:      mergeDateWithCurrentTime( year, month, d + 1 ).toISOString(),
    formatedDate: useDateFormat( mergeDateWithCurrentTime( year, month, d + 1 ), 'YYYY-MM-DD' ).value
  }))

  // Generate Array with the days remaining from the previous month

  const indexOfDaysRemainingInPreviousMonth = [ ...Array.from( Array.from({ length: 32 - new Date( year, month - 1, 32 ).getDate() }).keys()) ]
    .reverse()
    .splice( 0, firstDay )
    .reverse()

  const daysRemainingInPreviousMonth: CalendarDay[] = indexOfDaysRemainingInPreviousMonth.map( d => ({
    day:          d + 1,
    year,
    month:        month - 1,
    current:      false,
    date:         mergeDateWithCurrentTime( year, month - 1, d + 1 ),
    isoDate:      mergeDateWithCurrentTime( year, month - 1, d + 1 ).toISOString(),
    formatedDate: useDateFormat( mergeDateWithCurrentTime( year, month - 1, d + 1 ), 'YYYY-MM-DD' ).value
  }))

  // Generate Array with the days remaining in the next month

  const indexOfDaysRemainingInNextMonth = [ ...Array.from( Array.from({ length: 42 - ( daysInCurrentMonth.length + firstDay ) }).keys()) ]

  const daysRemainingInNextMonth: CalendarDay[] = indexOfDaysRemainingInNextMonth.map( d => ({
    day:          d + 1,
    year,
    month:        month + 1,
    current:      false,
    date:         mergeDateWithCurrentTime( year, month + 1, d + 1 ),
    isoDate:      mergeDateWithCurrentTime( year, month + 1, d + 1 ).toISOString(),
    formatedDate: useDateFormat( mergeDateWithCurrentTime( year, month + 1, d + 1 ), 'YYYY-MM-DD' ).value
  }))

  return {
    currentDay:   day,
    currentYear:  year,
    currentMonth: month,
    daysInCurrentMonth,
    daysRemainingInNextMonth,
    daysRemainingInPreviousMonth,
    calendarDays: [
      ...daysRemainingInPreviousMonth,
      ...daysInCurrentMonth,
      ...daysRemainingInNextMonth
    ].slice( 0, 42 ),
    weekDays: generateFromMonday ? weekDaysFromMonday : weekDaysFromSunday,
  }

}

export function generateYearsList( range: number = 100 ) {

  const startYear: number = new Date().getFullYear() - range
  const yearsList: number[] = []

  for ( let i = startYear; i < startYear + ( range * 2 ); i++ )
    yearsList.push( i )

  return yearsList

}
