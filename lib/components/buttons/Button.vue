<script setup lang="ts">

import { computed, onBeforeUnmount, onMounted, ref } from 'vue'
import { closeListOnEscapeKey, focusFirstOptionOnKeyDown } from '@lib/scripts/droplistUtils'

import Icon from '@lib/components/blocks/Icon.vue'
import Droplist from '@lib/components/utils/Droplist.vue'

import type { IconProps } from '@lib/store/icon'
import type { ButtonProps } from '@lib/types/buttonTypes'

const props = withDefaults( defineProps<ButtonProps>(), {
  size:            'l',
  type:            'rect',
  mode:            'primary',
  teleportList:    true,
  toggleOptionsOn: 'click',
})

const emits = defineEmits<{
  blur:  [ Event: FocusEvent ]
  click: [ Event: MouseEvent ]
  focus: [ Event: FocusEvent ]
}>()

const block = ref<boolean>( false )
const focus = ref<boolean>( false )
const element = ref<HTMLButtonElement>( null )
const toggleList = ref<boolean>( false )

const iconProp = computed<IconProps>(() => typeof props.icon === 'string' ? { name: props.icon } : props.icon as IconProps )

function setFocus( e: FocusEvent ) {
  focus.value = true
  emits( 'focus', e )
}

function removeFocus( e: FocusEvent ) {
  focus.value = false
  emits( 'blur', e )
}

function openList( e?: MouseEvent ) {

  if ( props.stopPropagation && e )
    e.stopPropagation()

  if ( props.preventDefault && e )
    e.preventDefault()

  if ( !props.options ) {
    if ( e )
      emits( 'click', e )
    return
  }

  if ( block.value ) {
    if ( e )
      emits( 'click', e )
    return
  }

  toggleList.value = true

}

async function closeList() {

  block.value = true
  toggleList.value = false

  setTimeout(() => block.value = false, 150 )

}

function focusFirstOption( e: KeyboardEvent ) {

  const { open } = focusFirstOptionOnKeyDown( e, element.value, toggleList.value )

  if ( open )
    openList()

}

function handleEscape( e: KeyboardEvent ) {
  const close = closeListOnEscapeKey( e, element.value, toggleList.value )
  if ( close )
    closeList()
}

onMounted(() => {
  window.addEventListener( 'keydown', handleEscape )
  element.value.addEventListener( 'keydown', focusFirstOption )
})

onBeforeUnmount(() => {
  window.removeEventListener( 'keydown', handleEscape )
  element.value.removeEventListener( 'keydown', focusFirstOption )
})

</script>

<template>

  <button
    :id="id"
    ref="element"
    :disabled="disabled || pending"
    :tabindex="tabindex"
    class="group/button truncate relative border disabled:pointer-events-none overflow-hidden focus-visible:outline-main-40 focus-visible:outline-1 focus-visible:outline-offset-1 focus-visible:outline-dashed clicked-active-state cursor-pointer"
    :class="{

      'h-full': size === 'full',
      'h-[3.125rem] min-h-[3.125rem]': size === 'xl',
      'h-[2.75rem] min-h-[2.75rem]': size === 'l',
      'h-[2.5rem] min-h-[2.5rem]': size === 'm',
      'h-[2.25rem] min-h-[2.25rem]': size === 's',
      'h-[2rem] min-h-[2rem]': size === 'xs',
      'h-[1.5rem] min-h-[1.5rem]': size === 'xxs',

      'rounded-0': ['rect', 'box'].includes(type),
      'rounded-[0.156rem]': ['pill', 'badge'].includes(type),

      'grid place-content-center': ['box', 'badge'].includes(type),

      'w-[3.125rem] min-w-[3.125rem]': size === 'xl' && ['badge', 'box'].includes(type),
      'w-[2.75rem] min-w-[2.75rem]': size === 'l' && ['badge', 'box'].includes(type),
      'w-[2.5rem] min-w-[2.5rem]': size === 'm' && ['badge', 'box'].includes(type),
      'w-[2.25rem] min-w-[2.25rem]': size === 's' && ['badge', 'box'].includes(type),
      'w-[2rem] min-w-[2rem]': size === 'xs' && ['badge', 'box'].includes(type),

      'text-core-10 bg-main hover:bg-main': mode === 'primary' && !pending,
      'text-core-10 bg-error hover:bg-error': mode === 'error' && !pending,
      'text-core-10 bg-warning hover:bg-warning': mode === 'warning' && !pending,
      'text-core-10 bg-success hover:bg-success': mode === 'success' && !pending,
      'hover:bg-core-120/[0.03]': ['skinny', 'naked'].includes(mode),

      'border-transparent': ['naked'].includes(mode),
      'border-main hover:border-main button-shadow': ['primary'].includes(mode),
      'border-error hover:border-error': ['error'].includes(mode),
      'border-warning hover:border-warning': ['warning'].includes(mode),
      'border-success hover:border-success': ['success'].includes(mode),
      'text-main bg-core-20 border border-core-30 hover:border-main focus:border-main button-shadow': ['secondary'].includes(mode),
      'border border-core-30 hover:border-core-40': mode === 'skinny',

      'border-b border-b-main hover:border-b-main rounded-b-none': (isActive || toggleList) && mode !== 'primary',
      'border-b border-b-main-90 hover:border-b-main-90 rounded-b-none': (isActive || toggleList) && mode === 'primary',

      'border-b border-b-red-700 hover:border-b-red-700 rounded-b-none': (isActive || toggleList) && mode === 'error',

      'border-b border-b-green-700 hover:border-b-green-700 rounded-b-none': (isActive || toggleList) && mode === 'success',

      'border-b border-b-yellow-700 hover:border-b-yellow-700 rounded-b-none': (isActive || toggleList) && mode === 'warning',

      'loading': pending,
      'opacity-50': disabled,

    }"
    @click="openList"
    @blur="removeFocus"
    @focus="setFocus"
    @mouseenter="(e) => toggleOptionsOn === 'hover' && openList(e)"
    @mouseleave="toggleOptionsOn === 'hover' && closeList()"
  >

    <slot :active="isActive || toggleList">
      <div class="w-full h-full flex items-center justify-center">
        <Icon v-if="['badge', 'box'].includes(type) && props.icon" :name="iconProp?.name" :size="iconProp?.size" />
      </div>
    </slot>

    <Droplist
      v-if="toggleList"
      :input="null"
      :label="label"
      :teleport="teleportList && toggleOptionsOn === 'click'"
      :parent-element="element"
      :options="options"
      @close="toggleOptionsOn !== 'hover' && closeList()"
      @selected="closeList"
    >
      <slot name="droplist" />
    </Droplist>

  </button>

</template>
