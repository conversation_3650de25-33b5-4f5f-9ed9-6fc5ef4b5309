<script setup lang="ts">

import { RouterLink } from 'vue-router'
import { computed, onBeforeUnmount, onMounted, ref } from 'vue'
import { closeListOnEscapeKey, focusFirstOptionOnKeyDown } from '@lib/scripts/droplistUtils'

import Icon from '@lib/components/blocks/Icon.vue'
import Droplist from '@lib/components/utils/Droplist.vue'

import type { TabProps } from '@lib/types/buttonTypes'
import type { IconProps } from '@lib/store/icon'

const props = withDefaults( defineProps<TabProps>(), {
  size:            'l',
  type:            'rect',
  toggleOptionsOn: 'click'
})

const emits = defineEmits<{
  blur:  [ Event: FocusEvent ]
  click: [ Event: MouseEvent ]
  focus: [ Event: FocusEvent ]
}>()

const block = ref<boolean>( false )
const focus = ref<boolean>( false )
const element = ref<HTMLButtonElement>( null )
const toggleList = ref<boolean>( false )

const iconProp = computed<IconProps>(() => typeof props.icon === 'string' ? { name: props.icon } : props.icon as IconProps )

function setFocus( e: FocusEvent ) {
  focus.value = true
  emits( 'focus', e )
}

function removeFocus( e: FocusEvent ) {
  focus.value = false
  emits( 'blur', e )
}

function openList( e?: MouseEvent ) {

  if ( block.value ) {
    if ( e )
      emits( 'click', e )
    return
  }

  if ( !props.options ) {
    if ( e )
      emits( 'click', e )
    return
  }

  toggleList.value = true

}

function closeList() {

  block.value = true
  toggleList.value = false

  setTimeout(() => block.value = false, 150 )

}

function focusFirstOption( e: KeyboardEvent ) {

  const { open } = focusFirstOptionOnKeyDown( e, element.value, toggleList.value )

  if ( open )
    openList()

}

function handleEscape( e: KeyboardEvent ) {
  const close = closeListOnEscapeKey( e, element.value, toggleList.value )
  if ( close )
    closeList()
}

onMounted(() => {

  window.addEventListener( 'keydown', handleEscape )

  if ( !props.to )
    element.value.addEventListener( 'keydown', focusFirstOption )

})

onBeforeUnmount(() => {
  window.removeEventListener( 'keydown', handleEscape )

  if ( !props.to )
    element.value.removeEventListener( 'keydown', focusFirstOption )

})

</script>

<template>

  <component
    :is="props.to ? RouterLink : 'button'"
    ref="element"
    :to="to"
    :disabled="disabled || pending"
    class="truncate relative flex items-center justify-center disabled:pointer-events-none overflow-hidden focus-visible:outline-main-40 focus-visible:outline-offset-1 focus-visible:outline-dashed md:hover:bg-core-20 clicked-active-state cursor-pointer"
    :class="{

      'h-[3.125rem] min-h-[3.125rem]': size === 'xl',
      'h-[2.75rem] min-h-[2.75rem]': size === 'l',
      'h-[2.5rem] min-h-[2.5rem]': size === 'm',
      'h-[2.25rem] min-h-[2.25rem]': size === 's',
      'h-[2rem] min-h-[2rem]': size === 'xs',

      'grid place-content-center': ['box', 'badge'].includes(type),

      'w-[3.125rem] min-w-[3.125rem]': size === 'xl' && ['badge', 'box'].includes(type),
      'w-[2.75rem] min-w-[2.75rem]': size === 'l' && ['badge', 'box'].includes(type),
      'w-[2.5rem] min-w-[2.5rem]': size === 'm' && ['badge', 'box'].includes(type),
      'w-[2.25rem] min-w-[2.25rem]': size === 's' && ['badge', 'box'].includes(type),
      'w-[2rem] min-w-[2rem]': size === 'xs' && ['badge', 'box'].includes(type),

      'text-core-10 bg-main hover:bg-main': mode === 'primary' && !pending,
      'text-core-80 hover:text-core-100': ['skinny', 'naked'].includes(mode),

      'border-b border-b-core-30 hover:border-b-main': !isActive && !toggleList,
      'border-b bg-core-40 hover:bg-core-30 border-b-main': (isActive || toggleList),

      'loading': pending,
      'opacity-50': disabled,

    }"
    active-class="tab-active-state"
    @click="openList"
    @blur="removeFocus"
    @focus="setFocus"
    @mouseenter="(e:MouseEvent) => toggleOptionsOn === 'hover' && openList(e)"
    @mouseleave="toggleOptionsOn === 'hover' && closeList()"
  >

    <slot :active="isActive || toggleList">
      <Icon v-if="['badge', 'box'].includes(type) && props.icon" :name="iconProp?.name" :size="iconProp?.size" />
    </slot>

    <Droplist
      v-if="toggleList"
      :input="null"
      :teleport="toggleOptionsOn === 'click'"
      :parent-element="element"
      :options="options"
      @close="toggleOptionsOn !== 'hover' && closeList()"
      @selected="closeList()"
    >
      <slot name="droplist" />
    </Droplist>

  </component>

</template>
