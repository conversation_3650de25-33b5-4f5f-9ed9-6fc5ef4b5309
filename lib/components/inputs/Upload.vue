<script setup lang="ts">

import { computed, ref, watch } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

import type { UploadInputProps } from '@lib/types/inputTypes'

defineOptions({
  name: 'UploadInput'
})

const props = withDefaults( defineProps<UploadInputProps<File>>(), {
  mode:         'primary',
  label:        'Upload File',
  existingFile: null,
  fileType:     'application/pdf'
})

const emits = defineEmits<{
  'update:valid':      [ payload: boolean ]
  'update:modelValue': [ payload: File ]
}>()

const dropActive = ref<boolean>( false )
const inputElement = ref<HTMLInputElement>( null )
const hasExistingFile = ref<string>( props.existingFile )

const input = computed({
  get: () => props.modelValue,
  set: value => emits( 'update:modelValue', value )
})

function handleUpload( e: Event ) {

  const target = e.target as HTMLInputElement

  input.value = target.files[0]

}

function dragLeave( e: any ) {

  if ( e.relatedTarget.parentElement === null || e.currentTarget.contains( e.relatedTarget ))
    return

  dropActive.value = false

}

function handleDrop( e: DragEvent ) {

  const file: File = e.dataTransfer.files[0]

  if ( file )
    input.value = file

  dropActive.value = false

}

function removeUpload() {

  input.value = null
  hasExistingFile.value = null
  inputElement.value.value = null

}

watch( input, ( n ) => {
  if ( n ) {
    emits( 'update:valid', true )
  }
  else {
    if ( props.required )
      emits( 'update:valid', false )
    else emits( 'update:valid', true )
  }
}, { immediate: true })

</script>

<template>

  <div
    class="w-full relative bg-core-10 flex items-center focus-within:outline-dashed focus-within:outline-1 focus-within:outline-offset-1 focus-within:outline-main-40"
    :class="{
      'h-[3.375rem]': size !== 'auto',
      'input-disabled': disabled,
      'input-mode-naked': mode === 'naked',
      'input-mode-ghost': mode === 'ghost',
      'bg-core-30 upload-border': mode === 'primary' && !input,
      'bg-core-10 border-core-30 border': mode === 'primary' && input,
      'border-dashed': !input,
      'border-core-30': !dropActive,
      '*:pointer-events-none border-main': dropActive,
      'loading pointer-events-none': pending,
    }"
    @dragover.prevent
    @dragenter.prevent="dropActive = true"
    @dragleave.prevent="dragLeave"
    @drop.prevent="handleDrop"
  >

    <div
      v-if="!!input || !!hasExistingFile"
      class="truncate w-full h-full pl-4 flex items-center space-x-2"
    >

      <Icon name="folder" size="m" class="text-main" />

      <p class="truncate text-main font-medium grow">
        {{ input?.name ?? hasExistingFile }}
      </p>

      <Button
        size="xl"
        type="box"
        mode="naked"
        class="text-error hover:text-error"
        :icon="{ name: 'delete', size: 'm' }"
        @click="removeUpload"
      />

    </div>

    <label
      v-show="!input && !hasExistingFile"
      class="w-full h-full flex items-center justify-center cursor-pointer"
    >

      <slot name="prefix" />

      <div
        class="text-main relative h-full px-4 flex items-center justify-center space-x-2 clicked-active-state"
        :class="{
          'pointer-events-none': dropActive,
        }"
      >
        <p>{{ label }}</p>
        <Icon name="upload" size="m" />
      </div>

      <input
        ref="inputElement"
        type="file"
        class="absolute opacity-0 pointer-events-none"
        :accept="fileType"
        @change="handleUpload"
      >

    </label>

  </div>

</template>
