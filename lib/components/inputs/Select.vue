<script setup lang="ts">

import { useI18n } from 'vue-i18n'
import { checkValue } from '@lib/scripts/utils'
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { closeListOnEscapeKey, focusFirstOptionOnKeyDown } from '@lib/scripts/droplistUtils'

import Icon from '@lib/components/blocks/Icon.vue'
import Label from '@lib/components/utils/Label.vue'
import Button from '@lib/components/buttons/Button.vue'
import Droplist from '@lib/components/utils/Droplist.vue'
import ValidationWrapper from '@lib/components/utils/ValidationWrapper.vue'

import type { SelectInputProps } from '@lib/types/inputTypes'

type SelectModelType = string | number | ( string | number )[]

defineOptions({
  name: 'SelectInput'
})

const props = withDefaults( defineProps<SelectInputProps<SelectModelType>>(), {
  type:       'select',
  mode:       'primary',
  size:       'l',
  options:    () => ( [] ),
  teleport:   true,
  required:   true,
  nullable:   true,
  returnType: 'id',
  showButton: true
})

const { t }           = useI18n()
const focus           = ref<boolean>( false )
const input           = defineModel<SelectModelType>()
const block           = ref<boolean>( false )
const element         = ref<HTMLButtonElement>( null )
const toggleList      = ref<boolean>( false )
const booleanModel = defineModel<boolean>( 'booleanModel', {
  default: null
})

const wrapperElement  = ref<HTMLDivElement>( null )

function openList() {

  if ( block.value || props.readonly )
    return

  toggleList.value = !toggleList.value

}

function closeList() {

  block.value = true
  toggleList.value = false

  setTimeout(() => block.value = false, 150 )

}

function selectOption( option: DropListOption ) {

  if ( props.multiple ) {

    const isInputArray = Array.isArray( input.value )

    if ( isInputArray ) {

      let list = [ ...input.value as ( string | number )[] ]

      if ( list.includes( option[props.returnType] )) {

        if ( !props.nullable ) {

          if ( list.length === 1 )
            return

        }

        list = list.filter( o => o !== option[props.returnType] )

      }

      else { list.push( option[props.returnType] ) }

      input.value = list

    }

    else {

      const list: typeof props.returnType[] = []
      list.push( option[props.returnType] )

      input.value = list

    }

  }

  else {

    if ( input.value === option[props.returnType] ) {

      if ( !props.nullable )
        return

      input.value = null
      booleanModel.value = null

    }

    else {

      input.value = option[props.returnType]
      booleanModel.value = option?.mapToBoolean === 'true'
      element.value.focus()

      if ( !option?.action )
        closeList()

    }

  }

}

function displayInput() {

  if ( checkValue( input.value )) {

    if ( props.multiple ) {

      const list = Array.isArray( input.value ) ? input.value as typeof props.returnType[] : [ input.value ]
      const displayList = []

      list.forEach(( value ) => {

        const currentValue = props.options.find( option => option[props.returnType] === value )

        if ( currentValue )
          displayList.push( currentValue.name )

      })

      return displayList.join( ', ' )

    }

    return props.options.find( option => option[props.returnType] === input.value )?.name

  }

  return props.label && props.size === 'l' ? `<span>${props.placeholder || t( 'global.label.selectOption' )}</span> <span class="text-main" >${props.required ? '*' : ''}</span>` : null

}

const canRemoveInput = computed(() => props.nullable && checkValue( input.value ))

function removeInput( e: PointerEvent ) {

  if ( canRemoveInput.value ) {
    e.stopPropagation()
    input.value = null
    booleanModel.value = null
  }

}

function focusFirstOption( e: KeyboardEvent ) {

  const { open } = focusFirstOptionOnKeyDown( e, element.value, toggleList.value )

  if ( open )
    openList()

}

function handleEscape( e: KeyboardEvent ) {
  const close = closeListOnEscapeKey( e, element.value, toggleList.value )
  if ( close )
    closeList()
}

onMounted(() => {
  window.addEventListener( 'keydown', handleEscape )
  wrapperElement.value.addEventListener( 'keydown', focusFirstOption )
})

onBeforeUnmount(() => {
  window.removeEventListener( 'keydown', handleEscape )
  wrapperElement.value.removeEventListener( 'keydown', focusFirstOption )
})

watch( [ booleanModel, input ], () => {

  if (( typeof input.value === 'boolean' || input.value === null || input.value === undefined ) && typeof booleanModel.value === 'boolean' ) {

    input.value = props.options.find(( item ) => {
      let booleanValue: boolean = null

      if ( [ 'true', 'false' ].includes( item.mapToBoolean ))
        booleanValue = item.mapToBoolean === 'true'

      return booleanValue === booleanModel.value

    })?.id

  }

}, { immediate: true })

</script>

<template>

  <ValidationWrapper
    v-bind="props"
    @expose-element="(el) => wrapperElement = el"
  >

    <div
      class="w-full flex cursor-pointer"
      :class="{
        'h-10': size === 'm',
        'h-auto': size === 'auto',
        'h-[3.375rem]': size === 'l',
        'input-disabled': disabled,
        'input-mode-naked': mode === 'naked' && !readonly,
        'input-mode-ghost': mode === 'ghost' && !readonly,
        'input-mode-primary': mode === 'primary' && !readonly,
      }"
    >

      <slot name="prefix" />

      <Label
        :name="name"
        :mode="mode"
        :size="size"
        :input="input"
        :label="label"
        :disabled="disabled"
        :required="required"
        :force-focus="!!label && size === 'l' && toggleList"
        :placeholder="placeholder"
        @click.stop="openList"
      >
        <button
          ref="element"
          class="truncate w-full h-full px-4 flex items-center outline-hidden"
          :disabled="disabled"
          @focus="focus = true"
          @blur="focus = false"
        >
          <p
            class="truncate"
            :class="{
              'opacity-0': !focus && !toggleList && !checkValue(input),
              'text-core-60': (focus || toggleList) && !checkValue(input),
            }"
            v-html="displayInput()"
          />
        </button>

        <template #icon>

          <div
            v-if="showButton && !readonly"
            class="h-full grid place-content-center"
            :class="{
              'px-2': size === 'l',
            }"
          >

            <Button
              size="m"
              mode="naked"
              type="box"
              :tabindex="canRemoveInput ? 0 : -1"
              @click="removeInput"
              @focus="focus = true"
              @blur="focus = false"
            >
              <Icon
                size="m"
                :name="canRemoveInput ? 'close' : 'chevron-down'"
                class="transition-transform text-main hidden md:block"
                :class="{
                  'rotate-180': !canRemoveInput && toggleList,
                }"
              />
              <Icon
                size="m"
                :name="canRemoveInput ? 'close' : 'chevron-right'"
                class="transition-transform text-main md:hidden"
                :class="{
                  'rotate-180': !canRemoveInput && toggleList,
                }"
              />
            </Button>

          </div>

        </template>

      </Label>

      <slot name="suffix" />

      <Droplist
        v-if="toggleList"
        :label="props.placeholder ?? props.label ?? props.name ?? $t('global.label.selectOption')"
        :input="input"
        :options="options"
        :multiple="multiple"
        :teleport="teleport"
        :max-width="listMaxWidth"
        :return-type="returnType"
        :parent-element="wrapperElement"
        @close="closeList"
        @selected="(option) => selectOption(option)"
      >
        <slot name="droplist" />

      </Droplist>

    </div>

  </ValidationWrapper>

</template>
