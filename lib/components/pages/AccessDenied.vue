<script setup lang="ts">

import But<PERSON> from '@lib/components/buttons/Button.vue'

</script>

<template>

  <div
    class="h-full w-full flex items-center justify-center flex-col bg-core-120 bg-center bg-no-repeat bg-contain"
    :style="{ backgroundImage: 'url(https://y.yarn.co/a44324b2-3d53-438d-ac3a-34a4382f06b2_text.gif)' }"
  >

    <!-- <h1 class="text-6xl text-error">
      ACCESS DENIED
    </h1> -->

    <p class="text-white text-lg md:max-w-sm max-w-xs text-center">
      You don't have permission to access this page.
    </p>
    <Button class="px-6 md:mt-8 mt-4" size="xl" type="rect" @click="$router.push('/')">
      {{ $t('global.button.returnHome') }}
    </Button>
  </div>

</template>
