<script setup lang="ts">

import But<PERSON> from '@lib/components/buttons/Button.vue'

</script>

<template>

  <div class="h-full w-full flex items-center justify-center flex-col">
    <h1 class="md:text-9xl text-6xl text-main">
      404
    </h1>
    <p class="md:text-2xl md:max-w-sm max-w-xs text-center text-xl text-core">
      {{ $t('global.phrase.pageNotFound') }}
    </p>
    <Button class="px-6 md:mt-8 mt-4" size="xl" type="rect" @click="$router.push('/')">
      {{ $t('global.button.returnHome') }}
    </Button>
  </div>

</template>
