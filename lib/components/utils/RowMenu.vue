<script setup lang="ts">

import { computed } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

interface Props {
  show:    boolean
  options: DropListOption[]
}

const props = defineProps<Props>()

const hasOptions = computed(() => props.options.filter( option => !option.hidden ).length > 0 )

function handleAction( option: DropListOption ) {

  if ( option?.action )
    option.action()

}

</script>

<template>

  <div class="min-w-0 min-h-[2rem] h-full relative z-1">

    <Transition name="row-menu">

      <div
        v-if="show && hasOptions"
        class="h-full absolute top-0 right-0 z-1 flex origin-right bg-core-20 border-l border-core-30"
      >

        <Button
          v-for="option in options"
          v-show="!option.hidden"
          :key="option.id"
          v-tooltip="{ content: option.name, wait: 300 }"
          :disabled="option.disabled"
          :options="option.options"
          :teleport-list="false"
          mode="naked"
          type="box"
          size="auto"
          class="min-w-[2.5rem] min-h-[2.5rem]"
          @click="() => handleAction(option)"
        >

          <Icon
            :name="option.icon?.name"
            :size="option.icon?.size ?? 'm'"
            :color="option.icon?.color"
            class="text-main"
          />

        </Button>

      </div>

    </Transition>

  </div>

</template>
