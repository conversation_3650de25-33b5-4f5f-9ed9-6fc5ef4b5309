<script setup lang="ts">

import { checkValue, formatDate } from '@lib/scripts/utils'
import { computed, onMounted, ref, watch } from 'vue'
import { generateCalendar, generateYearsList } from '@lib/scripts/calendarUtils'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

import type { Calendar, CalendarDay } from '@lib/scripts/calendarUtils'
import type { CalendarProps, DateModel } from '@lib/types/calendarTypes'

const props = withDefaults( defineProps<CalendarProps>(), {
  day:       new Date().getDate(),
  year:      new Date().getFullYear(),
  month:     new Date().getMonth(),
  startFrom: 'sunday'
})

const emits = defineEmits<{
  'update:modelValue': [ payload: DateModel ]
}>()

const currentDate = new Date()

function extractDays( model: DateModel ) {

  if ( Array.isArray( model ))
    return new Date( model[0] )?.getDate() ?? null

  if ( typeof model === 'string' )
    return new Date( model )?.getDate() ?? null

  return model?.getDate() ?? null

}

function extractMonths( model: DateModel ) {

  if ( Array.isArray( model ))
    return new Date( model[0] )?.getMonth() ?? null

  if ( typeof model === 'string' )
    return new Date( model )?.getMonth() ?? null

  return model?.getMonth() ?? null

}

function extractYears( model: DateModel ) {

  if ( Array.isArray( model ))
    return new Date( model[0] )?.getFullYear() ?? null

  if ( typeof model === 'string' )
    return new Date( model )?.getFullYear() ?? null

  return model?.getFullYear() ?? null

}

const d = ref( extractDays( props.modelValue ) ?? ( checkValue( props.day ) ? props.day : currentDate.getDate()))
const m = ref( extractMonths( props.modelValue ) ?? ( checkValue( props.month ) ? props.month : currentDate.getMonth()))
const y = ref( extractYears( props.modelValue ) ?? ( checkValue( props.year ) ? props.year : currentDate.getFullYear()))

const calendar = computed<Calendar>(() => generateCalendar({ year: y.value, month: m.value, day: d.value, generateFromMonday: props.startFrom === 'monday' }))

function getDateDetails(): { year: number, month: number } {

  const date = new Date( y.value, m.value, d.value )

  return {
    year:  date.getFullYear(),
    month: date.getMonth()
  }

}

const input = computed({
  get: () => props.modelValue,
  set: value => emits( 'update:modelValue', value )
})

function selectDate( date: CalendarDay ) {

  if ( props.range ) {

    if ( Array.isArray( input.value )) {

      if ( input.value.length === 2 ) {
        input.value = [ formatDate( date.date, 'YYYY-MM-DD' ) ]
        return
      }

      input.value = [ formatDate( input.value[0], 'YYYY-MM-DD' ), formatDate( date.date, 'YYYY-MM-DD' ) ]
      return

    }

    input.value = [ formatDate( date.date, 'YYYY-MM-DD' ) ]
    return

  }

  input.value = formatDate( date.date, 'YYYY-MM-DD' )

}

function isDateSelected( date: CalendarDay ): boolean {

  if ( props.range && Array.isArray( input.value ))
    return input.value.map( d => formatDate( d, 'YYYY-MM-DD' )).includes( formatDate( date.date, 'YYYY-MM-DD' ))

  if ( date?.date && input.value && !Array.isArray( input.value ))
    return formatDate( date.date, 'YYYY-MM-DD' ) === formatDate( input.value, 'YYYY-MM-DD' )

}

const tempRangeDate = ref<Date>( null )

function setTempRangeDate( date: Date ) {

  if ( !props.range )
    return

  tempRangeDate.value = date

}

function isDateInRange( date: CalendarDay ): boolean {

  if ( !props.range )
    return

  if ( Array.isArray( input.value )) {

    const rangeDate = input.value[1] ? input.value[1] : tempRangeDate.value

    if ( rangeDate )
      return date.date >= new Date( input.value[0] ) && date.date <= new Date( rangeDate )

    else return false

  }

}

function disableBefore( date: CalendarDay ): boolean {

  if ( props.range ) {

    if ( Array.isArray( input.value ) && input.value.length === 1 )
      return new Date( input.value[0] ) > date.date

  }

  if ( !props.limitFrom )
    return false

  return date.date <= new Date( props.limitFrom )

}

function disableAfter( date: CalendarDay ): boolean {

  if ( !props.limitTo )
    return false

  return date.date >= new Date( props.limitTo )

}

/*
 ** Handle Months
 */

const cM                = computed(() => getDateDetails().month )
const mPicker           = ref<boolean>( false )
const monthsContainer   = ref<HTMLDivElement>( null )
const monthsSearchInput = ref<HTMLInputElement>( null )

const months = [
  {
    name:      'January',
    index:     0,
    shortName: 'Jan'
  },
  {
    name:      'February',
    index:     1,
    shortName: 'Feb'
  },
  {
    name:      'March',
    index:     2,
    shortName: 'Mar'
  },
  {
    name:      'April',
    index:     3,
    shortName: 'Apr'
  },
  {
    name:      'May',
    index:     4,
    shortName: 'May'
  },
  {
    name:      'June',
    index:     5,
    shortName: 'Jun'
  },
  {
    name:      'July',
    index:     6,
    shortName: 'Jul'
  },
  {
    name:      'August',
    index:     7,
    shortName: 'Aug'
  },
  {
    name:      'September',
    index:     8,
    shortName: 'Sep'
  },
  {
    name:      'October',
    index:     9,
    shortName: 'Oct'
  },
  {
    name:      'November',
    index:     10,
    shortName: 'Nov'
  },
  {
    name:      'December',
    index:     11,
    shortName: 'Dec'
  }
]

function toggleMonthPicker() {
  yPicker.value = false
  mPicker.value = !mPicker.value
}

function incrementCurrentMonth() {
  m.value++
}

function decrementCurrentMonth() {
  m.value--
}

function isMonthSelected( index: number ): boolean {
  return index === cM.value
}

function selectMonth( index: number ) {
  m.value = index
  mPicker.value = false
}

function findMonth( e: InputEvent ) {

  const target = e.target as HTMLInputElement
  const value = target.value.toLowerCase()

  if ( value.length > 2 ) {

    const closestMatch = months.map( m => m.shortName ).find( m => m.toLowerCase().match( value ))?.toLowerCase()

    const element = document.querySelector( `#month-${closestMatch}` ) as HTMLDivElement

    if ( element )
      element.focus()

  }

}

function focusMonthsSearch( e: KeyboardEvent ) {

  if (( e.ctrlKey || e.metaKey ) && e.key === 'f' ) {
    e.preventDefault()
    monthsSearchInput.value?.focus()
  }

}

watch( monthsContainer, ( n ) => {

  if ( n ) {

    const monthElement = document.getElementById( `month-${months.find( month => month.index === m.value ).shortName.toLowerCase()}` ) as HTMLButtonElement
    monthElement.focus()

    window.addEventListener( 'keydown', focusMonthsSearch )

  }

  else { window.removeEventListener( 'keydown', focusMonthsSearch ) }

})

/*
 ** Handle Years
 */

const cY                = computed(() => getDateDetails().year )
const yPicker           = ref<boolean>( false )
const yearsList         = generateYearsList()
const yearsContainer    = ref<HTMLDivElement>( null )
const yearsSearchInput  = ref<HTMLInputElement>( null )

function toggleYearPicker() {
  mPicker.value = false
  yPicker.value = !yPicker.value
}

function incrementCurrentYear() {
  y.value++
}

function decrementCurrentYear() {
  y.value--
}

function isYearSelected( year: number ): boolean {
  return year === cY.value
}

function selectYear( year: number ) {

  m.value = getDateDetails().month
  y.value = year
  yPicker.value = false

}

function findYear( e: InputEvent ) {

  const target = e.target as HTMLInputElement
  const value = target.value

  if ( value.length > 3 ) {

    const closestMatch = yearsList.find( y => String( y ).match( value ))

    const element = document.querySelector( `#year-${closestMatch}` ) as HTMLDivElement

    if ( element ) {

      const button  = element.querySelector( 'button' )

      yearsContainer.value.scrollTop = element.offsetTop - 20
      button.focus()

    }

  }

}

function focusYearSearch( e: KeyboardEvent ) {

  if (( e.ctrlKey || e.metaKey ) && e.key === 'f' ) {
    e.preventDefault()
    yearsSearchInput.value?.focus()
  }

}

watch( yearsContainer, ( n ) => {

  if ( n ) {

    const yearElement = document.getElementById( `year-${String( y.value )}` )

    if ( yearElement ) {

      const yearButton = yearElement.querySelector( 'button' )

      n.scrollTop = yearElement.offsetTop - 20
      yearButton?.focus()
      yearsContainer.value.style.scrollBehavior = 'smooth'

    }

    window.addEventListener( 'keydown', focusYearSearch )

  }

  else { window.removeEventListener( 'keydown', focusYearSearch ) }

})

function focusFirstDate() {

  if ( !Array.isArray( input.value ) && input.value ) {
    const dateElement = document.getElementById( formatDate( input.value, 'YYYY-MM-DD' )) as HTMLButtonElement
    dateElement.focus()
  }

  else {
    const dateElement = document.querySelector( '.calendar-day' ) as HTMLButtonElement
    dateElement.focus()
  }

}

onMounted(() => focusFirstDate())

</script>

<template>

  <div class="h-full flex bg-core-20 border border-core-30 overflow-y-auto snap-y snap-mandatory">

    <div class="min-w-[20rem] w-[20rem]">

      <!-- Calendar Navigation -->

      <div class="flex items-center border-b border-core-30">

        <div class="grow flex items-center justify-between border-r border-core-30">

          <Button
            type="box"
            mode="naked"
            size="m"
            :icon="{ name: 'chevron-left', size: 'm' }"
            @click.stop="decrementCurrentMonth"
          />

          <Button
            mode="naked"
            size="m"
            @click.stop="toggleMonthPicker"
          >
            <p class="text-xs px-2">
              {{ formatDate(new Date(calendar.currentYear, calendar.currentMonth), 'MMMM') }}
            </p>
          </Button>

          <Button
            type="box"
            mode="naked"
            size="m"
            :icon="{ name: 'chevron-right', size: 'm' }"
            @click.stop="incrementCurrentMonth"
          />

        </div>

        <div class="flex items-center">

          <Button
            type="box"
            mode="naked"
            size="m"
            :icon="{ name: 'chevron-left', size: 'm' }"
            @click.stop="decrementCurrentYear"
          />

          <Button
            mode="naked"
            size="m"
            @click.stop="toggleYearPicker"
          >
            <p class="text-xs px-2">
              {{ formatDate(new Date(calendar.currentYear, calendar.currentMonth), 'YYYY') }}
            </p>
          </Button>

          <Button
            type="box"
            mode="naked"
            size="m"
            :icon="{ name: 'chevron-right', size: 'm' }"
            @click.stop="incrementCurrentYear"
          />

        </div>

      </div>

      <!-- Calendar Weekdays -->

      <Transition name="calendar" mode="out-in">

        <div v-if="yPicker" class="w-full h-8 px-4 flex items-center space-x-3 border-b border-core-30">

          <Icon name="search" size="m" />

          <input
            ref="yearsSearchInput"
            type="number"
            placeholder="Find Year"
            class="text-sm w-full h-full bg-transparent outline-hidden"
            @input="findYear"
          >

        </div>

        <div v-else-if="mPicker" class="w-full h-8 px-4 flex items-center space-x-3 border-b border-core-30">

          <Icon name="search" size="m" />

          <input
            ref="monthsSearchInput"
            placeholder="Find Month"
            class="text-sm w-full h-full bg-transparent outline-hidden"
            @input="findMonth"
          >

        </div>

        <div v-else class="w-full px-3 grid grid-cols-7 place-items-center border-b border-core-30">

          <div
            v-for="weekDay in calendar.weekDays"
            :key="weekDay.index"
            class="w-8 min-w-[2rem] h-8 text-xs text-core-70 font-semibold grid place-content-center"
          >
            {{ weekDay.shortName }}
          </div>

        </div>

      </Transition>

      <!-- Calendar Days -->

      <div class="overflow-hidden relative">

        <Transition name="calendar" mode="out-in">

          <div :key="m + y" class="w-full px-2 py-3 grid grid-cols-7 grid-rows-6 place-items-center">

            <div
              v-for="day in calendar.daysRemainingInPreviousMonth"
              :key="day.day"
              class="h-8 min-h-[2rem] max-h-[2rem] grid place-content-center opacity-50"
            >
              <Button type="box" size="xs" mode="naked" :disabled="true">
                <div class="w-1.5 h-1.5 bg-core-60 rounded-full" />
              </Button>
            </div>

            <div
              v-for="day in calendar.daysInCurrentMonth"
              :key="day.day"
              class="relative w-full h-8 min-h-[2rem] max-h-[2rem] grid place-content-center"
              :class="{
                'before:w-full before:h-[96%] before:absolute before:left-0 before:top-[2%] before:bg-main-20': isDateInRange(day),
              }"
              @mouseenter="setTempRangeDate(day.date)"
              @mouseleave="setTempRangeDate(null)"
            >
              <Button
                :id="day.formatedDate"
                type="box"
                size="xs"
                class="calendar-day"
                :mode="isDateSelected(day) ? 'primary' : 'naked'"
                :disabled="disableBefore(day) || disableAfter(day)"
                @click.stop="() => selectDate(day)"
              >
                <p class="text-xs font-semibold">
                  {{ day.day }}
                </p>
              </Button>
            </div>

            <div
              v-for="day in calendar.daysRemainingInNextMonth"
              :key="day.day"
              class="h-8 min-h-[2rem] max-h-[2rem] grid place-content-center opacity-50"
            >
              <Button type="box" size="xs" mode="naked" :disabled="true">
                <div class="w-1.5 h-1.5 bg-core-60 rounded-full" />
              </Button>
            </div>

          </div>

        </Transition>

        <Transition name="calendar">

          <div
            v-if="mPicker"
            ref="monthsContainer"
            class="w-full h-full absolute top-0 left-0 px-4 grid grid-cols-4 grid-rows-3 place-items-center bg-core-20 z-1"
          >
            <Button
              v-for="month in months"
              :id="`month-${month.shortName.toLowerCase()}`"
              :key="month.index"
              :mode="isMonthSelected(month.index) ? 'primary' : 'naked'"
              size="xs"
              class="w-full max-w-[4rem]"
              @click.stop="selectMonth(month.index)"
            >
              <p class="text-sm font-semibold">
                {{ month.shortName }}
              </p>
            </Button>
          </div>

          <div
            v-else-if="yPicker"
            ref="yearsContainer"
            class="w-full h-full absolute top-0 left-0 bg-core-20 z-1 overflow-y-auto overscroll-contain"
          >

            <div class="w-full h-auto px-2 grid grid-cols-4 gap-y-4 place-items-center place-content-center">

              <div
                v-for="year in yearsList"
                :id="`year-${String(year)}`"
                :key="year"
                class="w-full min-h-[3rem] grid place-content-center"
              >
                <Button
                  :mode="isYearSelected(year) ? 'primary' : 'naked'"
                  size="xs"
                  @click.stop="selectYear(year)"
                >
                  <p class="text-sm font-semibold px-3">
                    {{ year }}
                  </p>
                </Button>
              </div>

            </div>

          </div>

        </Transition>

      </div>

    </div>

  </div>

</template>
