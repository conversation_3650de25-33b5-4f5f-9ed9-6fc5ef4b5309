<script setup lang="ts" generic="T extends number">

import { computed } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

import type { FlowStep } from '@lib/types/flowBarTypes'

const props = defineProps<{
  steps: FlowStep<T>[]
}>()

defineEmits<{
  setActiveStep: [step: FlowStep<T>]
}>()

const flowSteps = computed(() => props.steps.filter( step => !step.hidden ))

const index     = computed(() => flowSteps.value.findIndex( step => step.inProgress ))
const currStep  = computed(() => flowSteps.value.find( step => step.inProgress ))
const nextStep  = computed(() => flowSteps.value[index.value + 1] )
const prevStep  = computed(() => flowSteps.value[index.value - 1] )

function isGroupComplete( step: FlowStep<T> ): boolean {
  return step.groupComplete && ( !step.error && !step.inProgress )
}

function isStepInProgress( step: FlowStep<T> ): boolean {
  return ( step.inProgress || step.groupComplete ) && !step.error
}

function isStepError( step: FlowStep<T> ): boolean {
  return step?.error
}

defineExpose({
  index,
  currStep,
  nextStep,
  prevStep,
  flowSteps
})

</script>

<template>

  <div class="h-8">

    <!-- Desktop Flow -->

    <div
      class="w-max h-full hidden md:flex gap-x-px isolate"
      :style="{ gridTemplateColumns: `repeat(${steps.length}, 1fr)` }"
    >

      <button
        v-for="step in steps.filter(s => s.isGroup && !s.hidden)"
        :key="step.id"
        :data-step="step.id"
        data-type="desktop"
        class="
          group/flow-pill text-sm font-medium h-8 relative pl-[1.125rem] first:pl-4 pr-4 flex items-center justify-center
          after:w-full first:after:w-[calc(100%-0.5rem)] last:after:w-[calc(100%-0.5rem)] first:after:left-2 last:after:left-0.5 after:h-4 after:absolute after:top-0 after:skew-x-[24deg]
          before:w-full first:before:w-[calc(100%-0.5rem)] last:before:w-[calc(100%-0.5rem)] first:before:left-2 last:before:left-0.5 before:h-4 before:absolute before:bottom-0 before:skew-x-[-24deg]
          "
        :class="{
          'pointer-events-none opacity-90': step.disabled,
          'after:bg-core-30 before:bg-core-30': !step.inProgress && !step.groupComplete,
          'text-core-10 after:bg-main before:bg-main': isStepInProgress(step),
          'text-core-10 after:bg-error before:bg-error': isStepError(step),
          'text-core-10 after:bg-success before:bg-success': isGroupComplete(step),
        }"
        @click="$emit('setActiveStep', step)"
      >

        <div
          class="w-8 h-full absolute left-0 top-0 rounded-md"
          :class="{
            'group-first/flow-pill:bg-core-30': !step.inProgress && !step.groupComplete,
            'text-core-10 group-first/flow-pill:bg-main': isStepInProgress(step),
            'text-core-10 group-first/flow-pill:bg-error': isStepError(step),
            'text-core-10 group-first/flow-pill:bg-success': isGroupComplete(step),
          }"
        />

        <div
          class="w-8 h-full absolute right-0 top-0 rounded-md"
          :class="{
            'group-last/flow-pill:bg-core-30': !step.inProgress && !step.groupComplete,
            'text-core-10 group-last/flow-pill:bg-main': isStepInProgress(step),
            'text-core-10 group-last/flow-pill:bg-error': isStepError(step),
            'text-core-10 group-last/flow-pill:bg-success': isGroupComplete(step),
          }"
        />

        <div class="w-full h-full flex items-center justify-center gap-x-1.5 z-1 clicked-active-state">

          <Icon v-if="isStepError(step)" name="warning-outline" size="m" />
          <Icon v-else-if="step.groupComplete" name="checkmark-outline" size="m" />
          <Icon v-else-if="isStepInProgress(step)" name="incomplete-normal" size="m" />
          <Icon v-else-if="!step.inProgress && !step.groupComplete && !step.error" name="circle-dash" size="m" />

          <p>
            {{ step.groupName }}
          </p>

        </div>

      </button>

    </div>

    <!-- Mobile Flow -->

    <div class="text-core-10 text-sm font-medium h-full grid md:hidden grid-cols-[2rem_1fr_2rem] items-center">

      <Button
        size="auto"
        type="box"
        mode="naked"
        class="w-8 h-full"
        :icon="{ name: 'chevron-left', size: 'm' }"
        :disabled="!prevStep"
        data-button="prev"
        @click="$emit('setActiveStep', prevStep)"
      />

      <div class="flex items-center gap-x-2">

        <Icon v-if="isStepError(currStep)" name="warning-outline" size="m" />
        <Icon v-else-if="currStep?.stepComplete" name="checkmark-outline" size="m" />
        <Icon v-else-if="isStepInProgress(currStep)" name="incomplete-normal" size="m" />
        <Icon v-else-if="!currStep?.inProgress && !currStep?.stepComplete && !currStep?.error" name="circle-dash" size="m" />

        <p>{{ currStep.stepName }} {{ index + 1 }} / {{ flowSteps.length }}</p>

      </div>

      <Button
        size="auto"
        type="box"
        mode="naked"
        class="text-core-10 w-8 h-full"
        :icon="{ name: 'chevron-right', size: 'm' }"
        :disabled="!nextStep || !currStep.stepComplete"
        data-button="next"
        @click="$emit('setActiveStep', nextStep)"
      />

    </div>

  </div>

</template>
