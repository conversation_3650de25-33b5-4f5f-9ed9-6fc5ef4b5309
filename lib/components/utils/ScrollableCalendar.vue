<script setup lang="ts">

import { generateCalendar } from '@lib/scripts/calendarUtils'
import { checkValue, formatDate } from '@lib/scripts/utils'
import { computed, onMounted, ref, watch } from 'vue'

import Button from '@lib/components/buttons/Button.vue'

import type { CalendarProps } from '@lib/types/calendarTypes'
import type { Calendar, CalendarDay } from '@lib/scripts/calendarUtils'

type DateModel = Date | string | ( Date | string )[]

const props = withDefaults( defineProps<CalendarProps>(), {
  day:       new Date().getDate(),
  year:      new Date().getFullYear(),
  month:     new Date().getMonth(),
  startFrom: 'monday'
})

const emits = defineEmits<{
  'close':             []
  'update:modelValue': [ payload: DateModel ]
}>()

// const calendarMonthHeight = 256

const yearlyContainer = ref<HTMLDivElement>( null )
const yearlyCalendarList = ref<CalendarList[]>( [] )

interface CalendarList {
  id:       string
  calendar: Calendar
}

const currentDate = new Date()

function extractDays( model: DateModel ) {

  if ( Array.isArray( model ))
    return new Date( model[0] )?.getDate() ?? null

  if ( typeof model === 'string' )
    return new Date( model )?.getDate() ?? null

  return model?.getDate() ?? null

}

function extractMonths( model: DateModel ) {

  if ( Array.isArray( model ))
    return new Date( model[0] )?.getMonth() ?? null

  if ( typeof model === 'string' )
    return new Date( model )?.getMonth() ?? null

  return model?.getMonth() ?? null

}

function extractYears( model: DateModel ) {

  if ( Array.isArray( model ))
    return new Date( model[0] )?.getFullYear() ?? null

  if ( typeof model === 'string' )
    return new Date( model )?.getFullYear() ?? null

  return model?.getFullYear() ?? null

}

const d = ref( extractDays( props.modelValue ) ?? ( checkValue( props.day ) ? props.day : currentDate.getDate()))
const m = ref( extractMonths( props.modelValue ) ?? ( checkValue( props.month ) ? props.month : currentDate.getMonth()))
const y = ref( extractYears( props.modelValue ) ?? ( checkValue( props.year ) ? props.year : currentDate.getFullYear()))

const currentCalendar = computed<Calendar>(() => generateCalendar({ year: y.value, month: m.value, day: d.value, generateFromMonday: props.startFrom === 'monday' }))

const input = computed({
  get: () => props.modelValue,
  set: value => emits( 'update:modelValue', value )
})

const selectedDate = ref<DateModel>( props.modelValue ?? null )

const canSubmitDate = computed(() => props.range && Array.isArray( selectedDate.value ) ? selectedDate.value.length > 1 : !!selectedDate.value )

function selectDate( date: CalendarDay ) {

  if ( props.range ) {

    if ( Array.isArray( selectedDate.value )) {

      if ( selectedDate.value.length === 2 ) {
        selectedDate.value = [ date.date ]
        return
      }

      selectedDate.value.push( date.date )
      return

    }

    selectedDate.value = [ date.date ]
    return

  }

  selectedDate.value = date.date

}

function submitDate() {
  input.value = selectedDate.value
  emits( 'close' )
}

function displayDate() {

  if ( props.range ) {

    if ( Array.isArray( selectedDate.value )) {

      if ( selectedDate.value.length === 0 )
        return 'Select Date'

      if ( selectedDate.value.length === 1 )
        return formatDate( selectedDate.value[0], 'DD MMMM YYYY' )

      if ( selectedDate.value.length === 2 ) {

        if ( new Date( selectedDate.value[0] ).getFullYear() === new Date( selectedDate.value[1] ).getFullYear())
          return `${formatDate( selectedDate.value[0], 'DD MMM' )} <span class="text-core-60" >to</span> ${formatDate( selectedDate.value[1], 'DD MMM YYYY' )}`

        else return `${formatDate( selectedDate.value[0], 'DD MMM YYYY' )} <span class="text-core-60" >to</span> ${formatDate( selectedDate.value[1], 'DD MMM YYYY' )}`

      }

    }

  }

  return selectedDate.value && !Array.isArray( selectedDate.value ) ? formatDate( selectedDate.value, 'DD MMMM YYYY' ) : 'Select Date'

}

function isDateSelected( date: CalendarDay ): boolean {

  if ( props.range && Array.isArray( selectedDate.value ))
    return selectedDate.value.map( d => formatDate( d, 'YYYY-MM-DD' )).includes( formatDate( date.date, 'YYYY-MM-DD' ))

  if ( date?.date && selectedDate.value && !Array.isArray( selectedDate.value ))
    return formatDate( date.date, 'YYYY-MM-DD' ) === formatDate( selectedDate.value, 'YYYY-MM-DD' )

}

function isDateInRange( date: CalendarDay ): boolean {

  if ( props.range ) {
    if ( Array.isArray( selectedDate.value ))
      return date.date >= new Date( selectedDate.value[0] ) && date.date <= new Date( selectedDate.value[1] )
  }

}

function disableBefore( date: CalendarDay ): boolean {

  if ( props.range ) {

    if ( Array.isArray( selectedDate.value ) && selectedDate.value.length === 1 )
      return new Date( selectedDate.value[0] ) > date.date

  }

  if ( !props.limitFrom )
    return false

  return date.date <= new Date( props.limitFrom )

}

function disableAfter( date: CalendarDay ): boolean {

  if ( !props.limitTo )
    return false

  return date.date >= new Date( props.limitTo )

}

const currentMonthId = computed(() => input.value ? `scrollable-${y.value}-${m.value}` : `scrollable-${new Date().getFullYear()}-${new Date().getMonth()}` )

function generateYearlyCalendar( year?: number ) {

  const calendarList: CalendarList[] = []

  for ( let i = 0; i < 12; i++ ) {
    calendarList.push({
      id:       `scrollable-${year}-${i}`,
      calendar: generateCalendar({ year, month: i }),
    })
  }

  yearlyCalendarList.value = calendarList

  setTimeout(() => {
    const currentElementList = document.querySelectorAll( '.current' )
    currentElementList.forEach(( el: HTMLDivElement ) => yearlyContainer.value.scrollTo({ top: el.offsetTop - 80, behavior: 'smooth' }))
  }, 100 )

}

watch(() => props.yearCounter, ( n ) => {
  generateYearlyCalendar( y.value + n )
})

onMounted(() => {
  generateYearlyCalendar( y.value )
})

</script>

<template>

  <div
    ref="yearlyContainer"
    class="w-full h-full overflow-hidden overflow-y-auto bg-core-20"
  >

    <div class="w-full sticky top-0 z-1 px-3 grid grid-cols-7 place-items-center bg-core-20 border-b border-core-30">

      <div
        v-for="weekDay in currentCalendar.weekDays"
        :key="weekDay.index"
        class="w-8 min-w-[2rem] h-8 text-xs text-core-70 font-semibold grid place-content-center"
      >
        {{ weekDay.shortName }}
      </div>

    </div>

    <div
      v-for="month in yearlyCalendarList"
      :id="month.id"
      :key="month.id"
      class="w-full h-[16rem] border-b border-b-core-30"
      :class="{
        current: month.id === currentMonthId,
      }"
    >

      <!-- Calendar Details -->

      <div class="w-full h-10 px-4 flex items-end">
        <p class="text-sm font-medium">
          {{ formatDate(new Date(month.calendar.currentYear, month.calendar.currentMonth), 'MMMM') }}
        </p>
      </div>

      <!-- Calendar Days -->

      <div class="w-full px-2 py-3 grid grid-cols-7 grid-rows-6 place-items-center">

        <div
          v-for="day in month.calendar.daysRemainingInPreviousMonth"
          :key="day.day"
          class="h-8 min-h-[2rem] max-h-[2rem] grid place-content-center opacity-50"
        >
          <Button type="box" size="xs" mode="naked" :disabled="true">
            <div class="w-1.5 h-1.5 bg-core-60 rounded-full" />
          </Button>
        </div>

        <div
          v-for="day in month.calendar.daysInCurrentMonth"
          :key="day.day"
          class="relative w-full h-8 min-h-[2rem] max-h-[2rem] grid place-content-center"
          :class="{
            'before:w-full before:h-[96%] before:absolute before:left-0 before:top-[2%] before:bg-main-20': isDateInRange(day),
          }"
        >
          <Button
            :id="day.formatedDate"
            type="box"
            size="xs"
            class="calendar-day"
            :disabled="disableBefore(day) || disableAfter(day)"
            :mode="isDateSelected(day) ? 'primary' : 'naked'"
            @click="selectDate(day)"
          >
            <p class="text-xs font-semibold">
              {{ day.day }}
            </p>
          </Button>
        </div>

        <div
          v-for="day in month.calendar.daysRemainingInNextMonth"
          :key="day.day"
          class="h-8 min-h-[2rem] max-h-[2rem] grid place-content-center opacity-50"
        >
          <Button type="box" size="xs" mode="naked" :disabled="true">
            <div class="w-1.5 h-1.5 bg-core-60 rounded-full" />
          </Button>
        </div>

      </div>

    </div>

    <div
      class="w-full sticky bottom-0 flex bg-core-10 border-t border-core-30 transition-[height] overflow-hidden"
      :class="{
        'h-0': !canSubmitDate,
        'h-12': canSubmitDate,
      }"
    >

      <div class="grow px-4 grid place-content-center">
        <p class="text-xs text-main font-medium" v-html="displayDate()" />
      </div>

      <Button
        size="xl"
        class="px-4 grow"
        :disabled="!canSubmitDate"
        @click="submitDate"
      >
        Submit
      </Button>

    </div>

  </div>

</template>
