<script setup lang="ts">

import { useRoute } from 'vue-router'
import { ref, watch } from 'vue'
import { vOnClickOutside } from '@vueuse/components'

import Button from '@lib/components/buttons/Button.vue'

const props = withDefaults( defineProps<{
  name?:           string
  debounce?:       number
  modelValue?:     string
  expandedSearch?: boolean
}>(), {
  debounce: 1000
})

const route         = useRoute()
const searchQuery   = defineModel<string>()
const searchInput   = ref<HTMLInputElement>( null )
const toggleSearch  = ref<boolean>( props.expandedSearch )
const searchPending = ref<boolean>( false )
const searchTimeout = ref<NodeJS.Timeout>( null )

function search( e: Event ) {

  clearTimeout( searchTimeout.value )

  const target = e.target as HTMLInputElement
  const query = target.value

  if ( !query ) {
    searchQuery.value = query
    return
  }

  searchPending.value = true

  searchTimeout.value = setTimeout(() => {
    searchQuery.value = query
    searchPending.value = false
  }, props.debounce )

}

function openSearch() {
  toggleSearch.value = true
}

function closeSearch() {

  if ( !props.expandedSearch )
    toggleSearch.value = false

  searchInput.value.value = null

  if ( searchQuery.value )
    searchQuery.value = null

}

watch( searchInput, ( n ) => {

  if ( !n )
    return

  n.focus()

  if ( !searchQuery.value )
    return

  searchInput.value.value = searchQuery.value
  n.focus()
  openSearch()

})

watch( route, () => {

  if ( searchQuery.value ) { // ------------ If there is an existing search query
    openSearch() // ------------------------ Open the search bar.
  }

  else { // -------------------------------- If there is no search query

    if ( searchInput.value ) { // ---------- If the search input has value
      searchInput.value.value = null // ---- Clear the search input,
      closeSearch() // --------------------- Close the search bar.
    }

  }

}, { immediate: true })

</script>

<template>

  <div
    v-on-click-outside="() => {
      if (!searchQuery && toggleSearch && !expandedSearch)
        closeSearch()
    }"
    class="h-full right-0 grid grid-cols-[max-content_1fr_max-content] justify-start justify-self-end bg-core-20 overflow-hidden"
    :class="{
      'relative w-10': !toggleSearch && !expandedSearch,
      'absolute z-1 transition-[width]': toggleSearch && !expandedSearch,
      'w-full': expandedSearch || toggleSearch,
    }"
  >

    <div class="h-full w-10 min-w-10 border-l border-core-30">

      <Button
        mode="naked"
        type="box"
        size="auto"
        class="h-full w-full"
        :icon="{
          name: searchPending ? 'loading' : 'search',
          size: 'm',
        }"
        @click="openSearch"
      />

    </div>

    <div v-if="toggleSearch || expandedSearch" class="w-full h-full border-4 border-transparent">

      <input
        ref="searchInput"
        class="w-full h-full px-2 bg-transparent input-mode-ghost"
        :placeholder="$t('global.label.search', { name: name ?? $t('global.label.records') })"
        @input="search"
      >

    </div>

    <div v-if="searchQuery || !expandedSearch" class="h-full w-10 min-w-10 border-l border-core-30">

      <Button
        mode="naked"
        type="box"
        size="auto"
        class="h-full w-full"
        :disabled="!toggleSearch"
        :icon="{
          name: 'close',
          size: 'm',
        }"
        @click="closeSearch"
      />

    </div>

  </div>

</template>
