<script setup lang="ts">

import { setupDroplist } from '@lib/scripts/droplistUtils'
import { vOnClickOutside } from '@vueuse/components'
import { onSwipe, searchModel } from '@lib/scripts/utils'
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'
import DroplistOption from '@lib/components/utils/DroplistOption.vue'

import type { SelectInputProps } from '@lib/types/inputTypes'

interface Props {
  label?:            string
  input?:            string | number | ( string | number )[]
  options?:          DropListOption[]
  maxWidth?:         string | number
  multiple?:         boolean
  teleport?:         boolean
  returnType?:       SelectInputProps<string | number | ( string | number )[]>['returnType']
  parentElement:     HTMLDivElement | HTMLButtonElement
  keepContentWidth?: boolean
}

const props = withDefaults( defineProps<Props>(), {
  options:  () => ( [] ),
  teleport: true
})

const emits = defineEmits<{
  close:    []
  selected: [ option: DropListOption ]
}>()

const element           = ref<HTMLDivElement>( null )
const mobileElement     = ref<HTMLDivElement>( null )
const overflowElement   = ref<any>( null )
const dropListMaxWidth  = computed(() => props?.maxWidth ? typeof props.maxWidth === 'string' ? props.maxWidth : `${props.maxWidth}px` : 'none' )

const canSearch         = computed(() => props.options.filter( option => !option.hidden ).length > 8 )
const searchQuery       = ref<string>( '' )
const searchableOptions = computed(() => searchModel( props.options, searchQuery.value, [ 'name', 'description' ] ))

function setupList() {
  setupDroplist( element.value, props.parentElement, props.keepContentWidth )
}

function isOptionSelected( option: DropListOption ) {

  if ( props.multiple ) {
    const list = Array.isArray( props.input ) ? props.input as typeof props.returnType[] : []
    return list.includes( option[props.returnType] )
  }

  else { return props.input === option[props.returnType] }

}

function closeList() {
  emits( 'close' )
}

watch( searchableOptions, () => {
  setTimeout(() => {
    setupList()
  }, 10 )
})

onMounted(() => {

  overflowElement.value = document.getElementById( 'overflow-element' ) ?? window

  setupList()
  window.addEventListener( 'resize', setupList )
  overflowElement.value.addEventListener( 'scroll', setupList )

  onSwipe( mobileElement.value, () => closeList())

})

onBeforeUnmount(() => {
  window.removeEventListener( 'resize', setupList )
  overflowElement.value.removeEventListener( 'scroll', setupList )
})

</script>

<template>

  <Teleport v-if="teleport" to="body">

    <div v-on-click-outside="closeList" class="ignore-outside">

      <div
        ref="element"
        class="max-h-[20rem] w-max max-w-[38rem] fixed z-1000 hidden md:block overflow-hidden overflow-y-auto overscroll-contain bg-core-10 shadow-2xl"
        :style="{ maxWidth: dropListMaxWidth }"
      >

        <slot>

          <div v-if="canSearch" class="w-full h-12 sticky top-0 z-1 pr-5 flex items-center bg-core-10 border-b border-core-30 focus-within:border-main">
            <input
              v-model="searchQuery"
              name="search-option"
              :placeholder="$t('global.label.search')"
              class="w-full h-full px-4 bg-transparent outline-hidden caret-main"
            >
            <Icon name="search" size="m" class="text-core-60" />
          </div>

          <div v-if="canSearch && searchQuery.length > 0 && searchableOptions.length === 0" class="w-full p-4">
            <p class="text-core-70 text-sm">
              {{ $t('global.phrase.noMatchingResults') }}
              <span class="text-main">{{ searchQuery }}</span>
            </p>
          </div>

          <DroplistOption
            v-for="option, index in searchableOptions"
            v-show="!option.hidden"
            :key="option.id ?? `${option.name} ${index}`"
            :total="options.length"
            :index="index"
            :option="option"
            :selected="isOptionSelected(option)"
            @click="() => emits('selected', option)"
            @close="emits('close')"
          />

        </slot>

      </div>

      <Transition name="droplist" :appear="true">

        <div
          v-if="teleport"
          ref="mobileElement"
          class="w-full h-full fixed z-1000 left-0 top-0 md:hidden bg-core-10 shadow-2xl overflow-y-auto overscroll-contain snap-mandatory"
          :style="{ maxWidth: dropListMaxWidth }"
        >

          <slot name="header">
            <div class="w-full h-10 sticky top-0 z-1 pl-4 flex items-center bg-core-10 border-b border-core-30">
              <p class="text-sm font-semibold text-core grow">
                {{ label }}
              </p>
              <Button size="m" mode="naked" type="box" :icon="{ name: 'close', size: 'm' }" @click="closeList" />
            </div>
          </slot>

          <slot>

            <div v-if="canSearch" class="w-full h-12 sticky top-10 z-1 pr-5 flex items-center bg-core-10 border-b border-core-30 focus-within:border-main">
              <input v-model="searchQuery" name="search-option" :placeholder="$t('global.label.search')" class="w-full h-full px-4 bg-transparent outline-hidden caret-main">
              <Icon name="search" size="m" class="text-core-60" />
            </div>

            <div v-if="canSearch && searchQuery.length > 0 && searchableOptions.length === 0" class="w-full p-4">
              <p class="text-core-70 text-sm">
                {{ $t('global.phrase.noMatchingResults') }}
                <span class="text-main">{{ searchQuery }}</span>
              </p>
            </div>

            <DroplistOption
              v-for="option, index in searchableOptions "
              :key="option.id ?? `${option.name} ${index}`"
              :total="options.length"
              :index="index"
              :option="option"
              :selected="isOptionSelected(option)"
              @click="() => emits('selected', option)"
              @close="emits('close')"
            />

          </slot>

        </div>

      </Transition>

    </div>

  </Teleport>

  <div
    v-else
    ref="element"
    v-on-click-outside="() => emits('close')"
    class="w-max max-h-[18rem] fixed z-1000 overflow-hidden overflow-y-auto overscroll-contain bg-core-10 shadow-2xl"
    :style="{ maxWidth: dropListMaxWidth }"
  >

    <slot>

      <div v-if="canSearch" class="w-full h-12 sticky top-0 z-1 pr-5 flex items-center bg-core-10 border-b border-core-30 focus-within:border-main">
        <input v-model="searchQuery" name="search-option" :placeholder="$t('global.label.search')" class="w-full h-full px-4 bg-transparent outline-hidden">
        <Icon name="search" size="m" class="text-core-60" />
      </div>

      <div v-if="canSearch && searchQuery.length > 0 && searchableOptions.length === 0" class="w-full p-4">
        <p class="text-core-70 text-sm">
          {{ $t('global.phrase.noMatchingResults') }}
          <span class="text-main">{{ searchQuery }}</span>
        </p>
      </div>

      <DroplistOption
        v-for="option, index in searchableOptions "
        :key="option.id ?? `${option.name} ${index}`"
        :total="options.length"
        :index="index"
        :option="option"
        :selected="isOptionSelected(option)"
        @click.stop="() => emits('selected', option)"
        @close="emits('close')"
      />

    </slot>

  </div>

</template>
