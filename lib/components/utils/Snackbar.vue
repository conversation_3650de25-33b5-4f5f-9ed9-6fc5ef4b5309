<script setup lang="ts">

import { onMounted, ref } from 'vue'
import { alertQueue, notificationQueue } from '@lib/store/snackbar'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

import type { SnackbarProps } from '@lib/types/snackbarTypes'

const props = withDefaults( defineProps<SnackbarProps>(), {
  showCloseButton: true
})

function resetSnackbarOptions() {

  if ( props.type === 'alert' )
    alertQueue.value = alertQueue.value.filter( alert => alert.id !== props.id )

  else
    notificationQueue.value = notificationQueue.value.filter( notification => notification.id !== props.id )

}

const remainingTime   = ref( 0 )
const lastStartTime   = ref( 0 )
const snackbarTimeout = ref<NodeJS.Timeout>( null )

function startTimeout() {

  clearTimeout( snackbarTimeout.value )

  snackbarTimeout.value = setTimeout(() => {

    resetSnackbarOptions()

  }, remainingTime.value )

  lastStartTime.value = Date.now()

}

function pauseTimeout() {

  clearTimeout( snackbarTimeout.value )

  const elapsedTime = Date.now() - lastStartTime.value

  if ( remainingTime.value > elapsedTime )
    remainingTime.value -= elapsedTime

}

function restartTimeout() {

  clearTimeout( snackbarTimeout.value )

  if ( remainingTime.value > 0 )
    startTimeout()

}

onMounted(() => {

  if ( !props.strict ) {

    remainingTime.value = props.duration
    startTimeout()

  }

  else { remainingTime.value = 0 }

})

</script>

<template>

  <div
    class="relative flex items-center justify-center"
    :class="{
      'w-max': !grow,
      'w-full': grow,
      'sm:justify-end': type === 'notification',
    }"
  >

    <div
      class="w-full bg-core-20 grid gap-x-3 border-[0.063rem] border-l-2"
      :class="{
        'grid-cols-[max-content_1fr_max-content]': type === 'alert',
        'pl-4 max-w-[20rem] grid-cols-[1fr_max-content]': type === 'notification',
        'border-main-30 border-l-main': type === 'notification' || (type === 'alert' && severity === 'info'),
        'border-error-30 border-l-error': type === 'alert' && severity === 'error',
        'border-core-30 border-l-core-30': type === 'alert' && severity === 'naked',
        'border-success-30 border-l-success': type === 'alert' && severity === 'success',
        'border-warning-30 border-l-warning': type === 'alert' && severity === 'warning',
        'w-80 min-w-[20rem] max-w-[20rem] sm:w-[28rem] sm:min-w-[28rem] sm:max-w-[28rem]': !grow && type !== 'notification',
      }"
      @mouseenter="pauseTimeout"
      @mouseleave="restartTimeout"
    >

      <div
        v-if="type === 'alert' && ['info', 'success', 'warning', 'error'].includes(severity)"
        class="pl-3 pt-4"
        :class="{
          'text-main': severity === 'info',
          'text-error': severity === 'error',
          'text-warning': severity === 'warning',
          'text-success': severity === 'success',
        }"
      >

        <Icon v-if="severity === 'info'" name="info" size="m" />
        <Icon v-if="severity === 'error'" name="issue-circle" size="m" />
        <Icon v-if="severity === 'warning'" name="warning-sign" size="m" />
        <Icon v-if="severity === 'success'" name="checkmark" size="m" />

      </div>

      <div
        class="py-3.5 overflow-hidden"
      >

        <p
          class="text-sm space-x-1.5"
          :class="{
            'line-clamp-3': type === 'notification',
          }"
        >
          <span v-if="message" :class="{ 'font-medium': !!details }">
            {{ message }}
          </span>

          <span v-if="details">
            {{ details }}
          </span>
        </p>

        <div
          v-if="action && actionName"
          class="w-full pt-2 flex"
        >

          <Button
            size="s"
            mode="secondary"
            class="text-sm px-4"
            @click="() => {
              action()
              resetSnackbarOptions()
            }"
          >
            {{ actionName }}
          </Button>

        </div>

      </div>

      <div v-if="showCloseButton" class="pr-2 pt-2">
        <Button size="xs" mode="naked" type="box" @click="resetSnackbarOptions">
          <Icon name="close" size="m" class="text-error" />
        </Button>
      </div>

    </div>

  </div>

</template>
