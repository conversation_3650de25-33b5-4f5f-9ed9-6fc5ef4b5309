<script setup lang="ts">

import { confirmOptions, resetConfirm } from '@lib/store/confirm'

import Modal from '@lib/components/blocks/Modal.vue'
import Dialog from '@lib/components/blocks/Dialog.vue'

</script>

<template>

  <Modal
    v-if="confirmOptions.open"
    v-slot="{ close }"
    @close="resetConfirm"
  >

    <Dialog
      mode="naked"
      class="absolute bottom-0 sm:top-1/2 left-1/2 -translate-x-1/2 sm:-translate-y-1/2"
      :header="confirmOptions.header"
      :action="confirmOptions.action"
      :on-success="confirmOptions.onSuccess"
      :description="confirmOptions.description"
      :cancel-cta-text="confirmOptions.cancelCtaText"
      :confirm-cta-text="confirmOptions.confirmCtaText"
      @close="close"
    />

  </Modal>

</template>
