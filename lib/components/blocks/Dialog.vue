<script setup lang="ts">

import { ref } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

import type { DialogProps } from '@lib/types/dialogTypes'

const props = withDefaults( defineProps<DialogProps>(), {
  mode: 'default'
})

const emits = defineEmits<{
  close:   []
  confirm: []
}>()

const error = ref<PayloadError[]>( null )
const pending = ref<boolean>( false )

async function handleAction() {

  error.value = null
  pending.value = true

  const actionData = await props.action()

  if ( !actionData?.error ) {

    if ( props?.onSuccess ) {

      const successData = await props.onSuccess()

      if ( successData?.error ) {

        error.value = successData.error
        pending.value = false

      }

      else {
        pending.value = false
        emits( 'close' )
      }

    }

    else {
      pending.value = false
      emits( 'close' )
    }

  }

  else {
    error.value = actionData.error
    pending.value = false
  }

}

async function handleCta() {

  if ( props?.action ) {
    await handleAction()
    return
  }

  emits( 'confirm' )

}

</script>

<template>

  <div class="w-full h-max max-h-[40rem] md:w-[25rem] bg-core-10 border border-core-30">

    <div v-if="mode === 'default'" class="w-full h-11 pl-4 flex items-center border-b border-neutral bg-core-20">

      <p class="font-semibold grow">
        <slot name="label">
          {{ label }}
        </slot>
      </p>

      <Button mode="naked" type="box" @click="() => emits('close')">
        <Icon name="close" size="m" class="text-error" />
      </Button>

    </div>

    <div class="w-full px-10 py-[2.875rem]">

      <slot>

        <div>

          <p v-if="header" class="text-lg pb-[0.875rem]">
            <slot name="header">
              <p v-html="header" />
            </slot>
          </p>

          <p v-if="error">

            <slot name="error">

              <div v-for="err, index in error" :key="index" class="py-2 border-b border-core-30 last:border-none">
                <p
                  class="line-clamp-4"
                  :class="{
                    'text-sm': !err?.details,
                    'text-error font-medium': err?.details,
                  }"
                >
                  {{ err.message }}
                </p>
                <p class="text-sm line-clamp-4">
                  {{ err.details }}
                </p>
              </div>

            </slot>

          </p>

          <p v-if="description && !error">
            <slot name="description">
              <p v-html="description" />
            </slot>
          </p>

          <slot name="content" />

        </div>

      </slot>

      <div class="w-full pt-[1.625rem] flex items-center justify-end space-x-4">

        <Button
          :disabled="pending"
          mode="secondary"
          @click="() => {
            error = null
            emits('close')
          }"
        >
          <slot name="cancel-button">
            <p class="px-4">
              {{ $t('global.button.cancel') }}
            </p>
          </slot>
        </Button>

        <Button
          :pending="pending"
          :disabled="disabled"
          @click="handleCta"
        >
          <slot name="confirm-button">
            <p class="px-4">
              {{
                error
                  ? $t('global.button.tryAgain')
                  : $t('global.button.confirm')
              }}
            </p>
          </slot>
        </Button>

      </div>

    </div>

  </div>

</template>
