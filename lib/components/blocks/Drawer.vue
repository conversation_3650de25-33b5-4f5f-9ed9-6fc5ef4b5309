<script setup lang="ts">

import { computed, ref } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

import type { IconName } from '@lib/store/icon'

interface Props {
  icon?:     IconName
  label:     string
  initOpen?: boolean
}

const props = defineProps<Props>()

const toggleDrawer  = ref<boolean>( props?.initOpen )
const drawerElement = ref<HTMLDivElement>( null )
const drawerHeight  = computed(() => `${toggleDrawer.value ? 32 + drawerElement.value?.clientHeight : 32}px` )

</script>

<template>

  <div class="w-full overflow-hidden border-b border-core-30 transition-[height]" :style="{ height: drawerHeight }">

    <Button mode="naked" size="xs" class="w-full px-4 flex items-center space-x-2" @click="() => toggleDrawer = !toggleDrawer">
      <Icon v-if="icon" :name="icon" size="m" />
      <p class="text-sm text-left font-semibold grow">
        {{ label }}
      </p>
      <Icon name="chevron-down" size="s" :class="{ 'rotate-180': toggleDrawer }" />
    </Button>

    <div ref="drawerElement">
      <slot />
    </div>

  </div>

</template>
