<script setup lang="ts">

import { notificationQueue } from '@lib/store/snackbar'

import Snackbar from '@lib/components/utils/Snackbar.vue'

</script>

<template>

  <div class="w-max h-0 fixed right-4 top-[3.75rem] z-1000 grid grid-cols-1 gap-y-4">

    <TransitionGroup name="notification">

      <Snackbar
        v-for="notification in notificationQueue"
        :key="notification.id"
        type="notification"
        v-bind="notification"
      />

    </TransitionGroup>

  </div>

</template>
