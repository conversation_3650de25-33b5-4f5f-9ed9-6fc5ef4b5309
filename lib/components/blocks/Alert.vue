<script setup lang="ts">

import { alertQueue } from '@lib/store/snackbar'

import Snackbar from '@lib/components/utils/Snackbar.vue'

</script>

<template>

  <div class="w-full h-0 sticky bottom-0 z-1000">

    <TransitionGroup class="w-max h-auto absolute bottom-4 left-1/2 -translate-x-1/2 grid grid-cols-1 gap-y-2 overflow-hidden" tag="div" name="snackbar">
      <Snackbar v-for="alert in alertQueue" :key="alert.id" v-bind="alert" type="alert" />
    </TransitionGroup>

  </div>

</template>
