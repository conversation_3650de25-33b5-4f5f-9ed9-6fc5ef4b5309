<script setup lang="ts" generic="R extends TabledRecord<any, any, NestedProps>, NestedProps = Record<string, any>">

import { formatRecordValue } from '@lib/store/table'
import { computed, reactive, ref, watch } from 'vue'

import Row from '@lib/components/table/Row.vue'
import Cell from '@lib/components/table/Cell.vue'
import Icon from '@lib/components/blocks/Icon.vue'
import Table from '@lib/components/table/Table.vue'
import Loader from '@lib/components/blocks/Loader.vue'
import Pagination from '@lib/components/utils/Pagination.vue'

import type { Component, Ref } from 'vue'
import type { TableReference } from '@lib/types/tableTypes'

interface Props {
  name?:            string
  type?:            NestedType
  schema?:          ( record: R ) => TableSchema<R>
  request?:         NestedRequest<R[]>
  component?:       Component
  lockWidth?:       boolean
  nestedProps?:     NestedProps
  parentWidth?:     number
  parentSchema?:    ( record: any ) => TableSchema<any>
  recordMapKey?:    keyof R
  nestedRecords?:   R[]
  recordOptions?:   ( record: R ) => DropListOption[]
  tableReference?:  TableReference
  hasExpandOffset?: boolean
  hasStatusOffset?: boolean
  hasSelectOffset?: boolean
}

const props = withDefaults( defineProps<Props>(), { name: 'nested-table', type: 'nested', records: () => [] })

const total               = ref<number>( 0 )
const params              = reactive<BaseParams>({ page: 1, pageSize: 10 })
const pending             = ref<boolean>( false )
const records             = ref<R[]>( props?.nestedRecords ?? null ) as Ref<R[]>
const maxPages            = ref<number>( 0 )
const sharedReference     = defineModel<TableReference>( 'tableReference' )
const expandedParentWidth = computed(() => props.parentWidth - 4 ) // minus 4px for the border on the expanded parent.

async function getRecords( viewParams: BaseParams ) {

  if ( !props.request )
    return

  pending.value = true

  const { payload, error } = await props.request( viewParams )

  if ( !error ) {
    total.value = payload.totalRows
    records.value = payload.records
    maxPages.value = payload.totalPages
  }

  pending.value = false

}

watch( params, n => getRecords( n ), { deep: true, immediate: true })

</script>

<template>

  <div
    :style="{ width: pending || lockWidth ? `${expandedParentWidth}px` : '100%' }"
    :class="{
      'sticky left-[0.125rem]': lockWidth || pending,
    }"
  >

    <!-- [ VIEW ] :: Extended -->

    <Transition v-if="type === 'extended'" name="view" mode="out-in">

      <div
        v-if="pending"
        :style="{ width: `${expandedParentWidth}px` }"
        class="min-h-24 sticky left-[0.125rem] flex items-center gap-x-2 justify-center"
      >
        <Loader :name="name" />
      </div>

      <div
        v-else-if="!pending && (!records || records?.length === 0)"
        :style="{ width: `${expandedParentWidth}px` }"
        class="min-h-24 sticky left-[0.125rem] flex items-center gap-x-2 justify-center"
      >

        <Icon class="text-main" name="folder" size="m" />

        <p class="text-sm">
          {{ $t('global.phrase.noRecordsFound', { name: name ?? $t('global.label.records') }) }}
        </p>

      </div>

      <div v-else>

        <div>

          <Row
            v-for="record, recordIndex in records"
            :key="recordIndex"
            :child="true"
            :record="record"
            :options="recordOptions ? recordOptions(record) : []"
            :table-name="name"
            :selectable="hasSelectOffset"
            :expandable="hasExpandOffset"
            :has-status="hasStatusOffset"
          >

            <template #default="{ headRow, rowSelected, expand }">

              <Cell
                v-for="column, columnIndex in parentSchema(record)"
                :key="column.key"
                v-model:cell="sharedReference.cells[columnIndex]"
                :flex="true"
                :link="column.link"
                :align="column.align ?? sharedReference.cells[columnIndex].align"
                :value="formatRecordValue(record, column)"
                :resize="column.resize"
                :sticky="column.sticky"
                :status="column.status"
                :nested="true"
                :head-cell="headRow"
                :reference="sharedReference"
                :row-selected="rowSelected"
                :row-expanded="expand"
                :cell-class="column.class"
              />

            </template>

          </Row>

        </div>

        <div
          v-if="!pending && records?.length > 0"
          :style="{ width: `${expandedParentWidth}px` }"
          class="sticky left-[0.125rem] flex items-center justify-end bg-core-30 border-t border-core-50 overflow-hidden"
        >

          <Pagination
            v-model:page="params.page"
            :total="total"
            :compact="true"
            :max-pages="maxPages"
            :data-length="records.length"
            :compact-size="10"
            :state-pagination="true"
            @update-size="(newSize) => params.pageSize = newSize"
          />

        </div>

      </div>

    </Transition>

    <!-- [ VIEW ] :: Nested -->

    <div
      v-if="type === 'nested'"
      class="sticky left-[0.125rem] p-4 grid bg-core-30 overflow-hidden"
      :style="{ width: (typeof lockWidth === 'boolean' ? lockWidth : true) ? `${expandedParentWidth}px` : '100%' }"
    >

      <Table
        v-model:params="params"
        :name="name"
        :schema="schema"
        :records="records"
        :pending="pending"
        :record-map-key="recordMapKey"
        :pagination="{
          total,
          compact: true,
          maxPages,
          compactSize: 10,
          statePagination: true,
        }"
      />

    </div>

    <!-- [ VIEW ] :: Slot -->

    <div v-if="type === 'slot'">

      <Component :is="component" v-bind="nestedProps" />

    </div>

  </div>

</template>
