<script setup lang="ts" generic="R, C">

import { checkValue } from '@lib/scripts/utils'
import { computed, ref } from 'vue'
import { vOnClickOutside } from '@vueuse/components'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'
import RowMenu from '@lib/components/utils/RowMenu.vue'
import Checkbox from '@lib/components/inputs/Checkbox.vue'

type TwHeight = `h-[${number}${CssSizeUnit}]` | `h-${'full' | 'max' | 'min' | number}`
type RowHeight = `${number}${CssSizeUnit}` | TwHeight | `${TwMediaQueries}:${TwHeight} ${TwHeight}` | `${TwHeight} ${TwMediaQueries}:${TwHeight}` | number

type TwWidth = `w-[${number}${CssSizeUnit}]` | `w-${'full' | 'max' | 'min' | number}`
type RowWidth = `${number}${CssSizeUnit}` | TwWidth | `${TwMediaQueries}:${TwWidth} ${TwWidth}` | `${TwWidth} ${TwMediaQueries}:${TwWidth}` | number

interface RowProps {
  child?:            boolean
  width?:            RowWidth
  record?:           Tablify<R, C, Record<string, any>> | null
  height?:           RowHeight
  headRow?:          boolean
  options?:          DropListOption[]
  selected?:         boolean
  tableName:         string
  hasStatus?:        boolean
  selectable?:       boolean
  expandable?:       boolean
  disableRowSelect?: boolean
  disableRowExpand?: boolean
}

const props           = withDefaults( defineProps<RowProps>(), { height: 'h-10', width: 'w-max' })
const emits           = defineEmits<{ select: [ payload: boolean ], rowClick: [ payload: void ] }>()

const parent          = computed<boolean>(() => !!props.record?.nested )
const status          = computed<TableRecordStatus>(() => props.record?.tableRecordStatus )
const disableRow      = computed<boolean>(() => props.record?.tableRecordDisabled )
const disableMenu     = computed<boolean>(() => props.record?.tableRecordMenuDisabled )
const disableClick    = computed<boolean>(() => props.record?.tableRecordClickDisabled )
const disableSelect   = computed<boolean>(() => props?.disableRowSelect || props.record?.tableRecordSelectDisabled )
const disableExpand   = computed<boolean>(() => props?.disableRowExpand || props.record?.tableRecordExpandDisabled )

const keyTriggers     = computed(() => handleTriggers( props.record )?.keyTriggers ?? [] )
const menuTriggers    = computed(() => handleTriggers( props.record )?.menuTriggers ?? [] )
const defaultTrigger  = computed(() => handleTriggers( props.record )?.defaultTrigger )

const expand          = computed<boolean>(() => expandByKey.value || expandByMenu.value || expandDefault.value )
const expandByKey     = computed<boolean>(() => keyTriggers.value.map( t => t.name ).includes( activeName.value ))
const expandByMenu    = computed<boolean>(() => activeName.value && !expandByKey.value && !expandDefault.value )
const expandDefault   = computed<boolean>(() => activeName.value === defaultTrigger.value )

const openMenu        = ref<boolean>( false )
const rowOptions      = computed(() => menuTriggers.value.length > 0 ? [ ...menuTriggers.value, ...props.options ] : props.options )
const hasOptions      = computed(() => rowOptions.value?.length > 0 )
const activeName      = ref<string>( null )
const activeView      = computed(() => Array.isArray( props.record?.nested ) ? props.record?.nested?.find( n => n.name === activeName.value ) ?? null : activeName.value ? props.record?.nested : null )
const rowSelected     = defineModel<boolean>( 'selected' )

const rowWidthStyle   = computed(() => typeof props.width === 'string' ? props.width.includes( 'w-' ) ? 'none' : props.width : `${props.width}px` )
const rowWidthClass   = computed(() => typeof props.width === 'string' ? props.width.includes( 'w-' ) ? props.width : '' : '' )

const rowHeightStyle  = computed(() => typeof props.height === 'string' ? props.height.includes( 'h-' ) ? 'none' : props.height : `${props.height}px` )
const rowHeightClass  = computed(() => typeof props.height === 'string' ? props.height.includes( 'h-' ) ? props.height : '' : '' )

/**
 * Handles the triggers for the row.
 * It checks if the record has nested records and if so, it processes
 * them to determine the key triggers, menu triggers, and default trigger.
 * @param record The record to handle triggers for.
 * @returns An object containing the key triggers, menu triggers, and default trigger.
 */
function handleTriggers( record: Tablify<R, C, Record<string, any>> ) {

  if ( !record?.nested ) // ------------------------------------------------------------------------- If there are no nested records, return.
    return

  const kTriggers: { name: string, key: keyof R }[] = []
  const mTriggers: DropListOption[] = []
  const dTriggers: string[] = []

  const nested = record.nested

  if ( Array.isArray( nested )) { // ---------------------------------------------------------------- If nested is an array

    for ( const item of nested ) { // --------------------------------------------------------------- For each item in the array,

      if ( !item.trigger ) { // --------------------------------------------------------------------- If there is no trigger,
        dTriggers.push( item.name ) // -------------------------------------------------------------- Add a default trigger.
      }

      if ( item?.trigger?.type === 'key' ) // ------------------------------------------------------- If the trigger is a key,
        kTriggers.push({ name: item.name, key: item.trigger.key }) // ------------------------------- Add a key trigger.

      if ( item?.trigger?.type === 'menu' ) // ------------------------------------------------------ If the trigger is a menu,
        mTriggers.push({ ...item.trigger.option, action: () => toggleExpand( item.name ) }) // ------ Add a menu trigger.

      if ( item?.trigger?.type === 'default' ) // --------------------------------------------------- If the trigger is a default,
        dTriggers.push( item.name ) // -------------------------------------------------------------- Add a default trigger.

    }

    return {
      keyTriggers:    kTriggers,
      menuTriggers:   mTriggers,
      defaultTrigger: dTriggers[0],
    }

  }

  switch ( nested?.trigger?.type ) { // ------------------------------------------------------------- If nested is an object, check each trigger type,

    case 'key': // ---------------------------------------------------------------------------------- If the trigger is a key,
      kTriggers.push({ name: nested.name, key: nested.trigger.key }) // ----------------------------- Add a key trigger.
      break

    case 'menu': // --------------------------------------------------------------------------------- If the trigger is a menu,
      mTriggers.push({ ...nested.trigger.option, action: () => toggleExpand( nested.name ) }) // ---- Add a menu trigger.
      break

    case 'default': // ------------------------------------------------------------------------------ If the trigger is a default,
      dTriggers.push( nested.name ) // -------------------------------------------------------------- Add a default trigger.
      break

    default: // ------------------------------------------------------------------------------------- If there is no trigger,
      dTriggers.push( nested.name ) // -------------------------------------------------------------- Add a default trigger.
      break

  }

  return {
    keyTriggers:    kTriggers,
    menuTriggers:   mTriggers,
    defaultTrigger: dTriggers[0],
  }

}

function toggleExpand( viewName: string ) {

  if ( disableExpand.value )
    return

  activeName.value === viewName
    ? activeName.value = null
    : activeName.value = viewName

}

function handleRowClick() {

  if ( disableClick.value )
    return

  emits( 'rowClick' )

}

</script>

<template>

  <div
    class="isolate group/row min-w-full h-auto relative grid grid-cols-[max-content_1fr_max-content]"
    :style="{ width: rowWidthStyle }"
    :class="[rowWidthClass, {
      'bg-core-20': child,
      'group/child': child,
      'group/parent': parent,
      'bg-core-10 even:bg-core-20': !headRow,
      'sticky top-0 z-6 bg-core-20': headRow,
      'bg-core-30 z-7 first:hover:z-8': expand,
      'pointer-events-none opacity-50': disableRow,
      'bg-main-20 even:bg-main-20 group/selected': rowSelected && !headRow,
      'after:w-full after:h-full after:absolute after:inset-0 hover:after:bg-main/5 after:pointer-events-none': !expand && !headRow,
      'after:w-full after:h-full after:absolute after:top-0 after:left-0 after:z-4 after:pointer-events-none after:border-y-2 after:border-main': expand,
    }]"
  >

    <!-- This div simulates the left border of the expanded row. -->

    <div v-if="expand" class="w-[0.125rem] h-full sticky left-0 z-10 bg-main">

      <!-- Nested Row Header -->

      <div
        v-if="expand"
        class="
            text-xs text-core-10 absolute -left-2 -top-5 group-first/row:top-full z-10 h-5 scale-y-100 md:scale-y-0 group-hover/row:scale-y-100 px-3 flex items-center gap-x-2 bg-main transition-[scale] origin-bottom group-first/row:origin-top cursor-pointer
            before:w-5 before:h-5 md:before:scale-y-0 group-hover/row:before:scale-y-100 before:transition-[scale] before:origin-bottom before:absolute before:z-1 before:-right-3 before:bg-main before:skew-x-24 group-first/row:before:-skew-x-24 group-first/row:before:-right-2 group-first/row:before:origin-top before:pointer-events-none
            "
        @click="() => toggleExpand(activeName)"
      >

        <Icon
          name="chevron-down"
          size="s"
          class="group-first/row:rotate-180"
        />

        <p class="truncate z-2 md:opacity-0 pointer-events-none group-hover/row:opacity-100">
          {{ activeView?.title ?? activeView?.name ?? props?.tableName ?? 'Records' }}
        </p>

      </div>

    </div>

    <!-- Row Content -->

    <div>

      <div
        class="grid grid-rows-1 grid-cols-[max-content_1fr] content-center border-b"
        :style="{ height: rowHeightStyle }"
        :class="[rowHeightClass, {
          'border-core-30 group-last/row:border-b-0': !expand,
          'border-core-50': expand,
        }]"
      >

        <div
          class="w-max h-full flex sticky z-3 border-r"
          :class="{
            'bg-core-20': headRow || child,
            'bg-main-20': rowSelected && !headRow,
            'left-0': !expand || !child,
            'left-[0.125rem]': expand || child,
            'bg-core-30 border-core-50': expand,
            'bg-core-10 group-even/child:bg-core-20': !headRow && !rowSelected,
            'group-even/row:bg-core-20 border-core-30': !child && !expand && !rowSelected,
            'group-hover/row:after:w-full group-hover/row:after:h-full group-hover/row:after:absolute group-hover/row:after:bg-main/5 group-hover/row:after:pointer-events-none': !child && !expand && !headRow,
            'group-hover/child:after:w-full group-hover/child:after:h-full group-hover/child:after:absolute group-hover/child:after:bg-main/5 group-hover/child:after:pointer-events-none': child && !expand && !headRow,
          }"
        >

          <div
            v-if="hasStatus"
            class="w-10 min-w-[2.5rem] h-full grid place-content-center overflow-hidden"
          >

            <div
              v-if="(status && status?.type) && checkValue(status.count)"
              v-tooltip:[status.type].right="{ content: status?.message }"
              class="h-4 min-w-4 px-1 grid place-content-center text-xs font-medium rounded-xl cursor-default"
              :class="{
                'bg-error': status.type === 'error',
                'bg-success': status.type === 'success',
                'bg-warning': status.type === 'warning',
              }"
            >
              <span class="text-core-10">{{ status.count }}</span>
            </div>

            <Icon
              v-else-if="status && status?.type && !status.count"
              v-tooltip:[status.type].right="{ content: status?.message }"
              :name="status.type === 'success' ? 'checkmark' : 'issue-circle'"
              size="m"
              :class="{
                'text-success': status.type === 'success',
                'text-warning': status.type === 'warning',
                'text-error': status.type === 'error',
              }"
            />

          </div>

          <div
            v-if="expandable"
            class="h-full w-10 min-w-[2.5rem] grid place-content-center"
          >

            <Button
              v-if="parent"
              type="box"
              size="s"
              mode="naked"
              :icon="{ name: expandDefault ? 'chevron-up' : 'chevron-down', size: 'm' }"
              :class="{ 'text-main hover:text-main': expandDefault }"
              :disabled="disableExpand"
              @click="() => toggleExpand(defaultTrigger)"
            />

            <div
              v-if="child"
              class="w-10 h-10 flex justify-center"
            >

              <div
                class="
                w-[0.063rem] h-full self-center relative bg-main-40
                group-first/child:self-end group-first/child:h-6 group-first/child:after:absolute group-first/child:after:top-0 group-first/child:after:w-2 group-first/child:after:h-2 group-first/child:after:bg-main-40 group-first/child:after:rounded-full group-first/child:after:-left-1
                group-last/child:self-start group-last/child:h-6 group-last/child:after:absolute group-last/child:after:bottom-0 group-last/child:after:w-2 group-last/child:after:h-2 group-last/child:after:bg-main-40 group-last/child:after:rounded-full group-last/child:after:-left-1
                group-first/child:group-last/child:self-center group-first/child:group-last/child:h-1 group-first/child:group-last/child:after:-top-0.5
              "
              />

            </div>

          </div>

          <div
            v-if="selectable"
            class="h-full w-10 min-w-[2.5rem] grid place-content-center"
          >

            <Checkbox
              v-if="!child"
              v-model="rowSelected"
              class="relative left-1"
              :disabled="disableSelect"
              @update:model-value="(value) => emits('select', value)"
            />

          </div>

        </div>

        <div
          v-on-click-outside="() => openMenu = false"
          class="flex items-center"
          @mouseenter="() => openMenu = true"
          @mouseleave="() => openMenu = false"
          @click="() => openMenu = true"
        >

          <div
            class="w-full h-full flex items-center"
            @click="handleRowClick"
          >

            <slot
              :expand="expand"
              :head-row="headRow"
              :triggers="keyTriggers"
              :row-selected="rowSelected"
              :toggle-expand="toggleExpand"
              :expanded-by-key="expandByKey"
            />

          </div>

          <RowMenu
            v-if="hasOptions && !disableMenu"
            class="sticky right-0 z-3"
            :show="openMenu"
            :class="{ 'hidden md:block': headRow }"
            :options="rowOptions"
          />

        </div>

      </div>

      <!-- Drawer for the nested records -->

      <div
        :class="{
          'h-0 overflow-hidden': !expand,
          'h-auto overflow-visible': expand,
        }"
      >

        <slot v-if="expand" name="drawer" :expand-view-name="activeName" />

      </div>

    </div>

    <!-- This div simulates the right border of the expanded row. -->

    <div v-if="expand" class="w-[0.125rem] h-full sticky right-0 z-10 bg-main" />

  </div>

</template>
