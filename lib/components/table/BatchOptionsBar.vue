<script setup lang="ts" generic="R">

import { pluralize } from '@lib/scripts/utils'
import { vOnClickOutside } from '@vueuse/components'
import { computed, ref, watch } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

import type { BatchOption } from '@lib/types/tableTypes'

interface BatchOptionsGroup {
  name:    BatchOption<R>['group']
  list:    DropListOption[]
  options: BatchOption<R>[]
}

const props = defineProps<{
  total:             number // ----------------------------------------- The total number of records.
  records:           R[] // -------------------------------------------- Selected records.
  filtered:          R[] // -------------------------------------------- The records that have been filtered by the active action. [ These are exposed to the parent component. ]
  pageTotal:         number // ----------------------------------------- The total number of records on the page.
  batchOptions:      BatchOption<R>[] | undefined // ------------------- The batch options to be displayed.
  resourceName:      string // ----------------------------------------- The name of the resource.
  allOnPageSelected: boolean // ---------------------------------------- Whether all records are selected.
}>()

const emits = defineEmits<{
  close:     [ void ]
  selectAll: [ void ]
}>()

const groups                = ref<BatchOptionsGroup[]>( [] )
const pending               = ref<boolean>( false )
const activeId              = ref<number>( null )
const openOptions           = ref<boolean>( false )
const allSelected           = computed(() => props.records.length === props.total )
const activeAction          = computed(() => props.batchOptions.find( a => a.id === activeId.value ))
const filteredRecords       = defineModel( 'filtered', { default: [] })
const activeActionType      = computed(() => activeAction.value?.type || 'neutral' )
const noOptionsAvailable    = computed(() => groups.value.map( g => g.list.filter( o => !o.hidden ).length ).reduce(( a, b ) => a + b, 0 ) === 0 )
const filteredRecordsCount  = computed(() => filteredRecords.value.length )
const selectedRecordsCount  = computed(() => props.records.length )

function setActiveId( id: number ) {
  activeId.value = id
}

/**
 * Handle the message displayed when all or some of the records are selected.
 * @param {boolean} all Whether all records are selected.
 * @param {string} resource The name of the resource.
 * @param {number} count The number of selected records.
 */

function handleSelectedMessage( all: boolean, resource: string, count: number ) {
  return all
    ? `All ${pluralize( resource, 2 )} Selected`
    : `${pluralize( resource, count )} Selected`
}

/**
 * Map the batch options to a list of DropListOptions.
 * Hide the option if there are no records that match the filter.
 */

function mapOptionsToList( option: BatchOption<R> ): DropListOption {

  function createDescription() {

    if ( props.records?.length === 1 ) // ----------------------------- If there is only one record selected,
      return `1 ${pluralize( props.resourceName, 1 )}` // ------------- Return the singular form of the resource name.

    const selectedCount = props.records?.length

    const count = option?.filter // ----------------------------------- If there is a filter function,
      ? props.records?.filter( option.filter )?.length // ------------- Count the number of records that match the filter.
      : selectedCount // ---------------------------------------------- Otherwise, return the total number of selected records.

    return `${count} of ${selectedCount} ${pluralize( props.resourceName, selectedCount )}`

  }

  return {
    id:          option.id,
    name:        option.actionName,
    icon:        { name: option.icon, size: 'm', position: 'right' },
    color:       option.type === 'negative' ? 'error' : option.type === 'positive' ? 'success' : 'primary',
    hidden:      option.filter ? props.records?.filter( option.filter )?.length === 0 : false,
    action:      () => setActiveId( option.id ),
    description: createDescription(),
  }

}

/**
 * Group the batch options by their group name.
 * Each group will have a list of DropListOptions and a list of unmapped options.
 */

function groupOptions( batchOptions: BatchOption<R>[] ) {

  if ( !batchOptions )
    return []

  const groups: BatchOptionsGroup[] = []

  batchOptions.forEach(( option ) => {

    const group = groups.find( g => g.name === option.group ) // ------ Find the group with the same name as the option.

    if ( group ) { // ------------------------------------------------- If the group exists,
      group.options.push( option ) // --------------------------------- Add the option to the group.
    }

    else { // --------------------------------------------------------- Otherwise,
      groups.push({ // ------------------------------------------------ Create a new group.
        name:    option.group,
        list:    [],
        options: [ option ]
      })
    }

  })

  groups.forEach(( group ) => {
    group.list = group.options.map( mapOptionsToList ) // ------------- Map the options to a list of DropListOptions.
  })

  return groups

}

/**
 * Execute the active action.
 * If the action is asynchronous, set the pending state to true.
 * When the action is complete, clear the filtered records and close the batch options bar.
 */

async function executeAction( action: () => Promise<void> | void ) {

  pending.value = true

  await action()

  openOptions.value = false
  filteredRecords.value = []
  setActiveId( null )
  emits( 'close' )

  pending.value = false

}

// Watch for changes in the batch options or in the selected
// records count and group the options by their group name.

watch( [ () => props.batchOptions, selectedRecordsCount ], options => groups.value = groupOptions( options[0] ), { immediate: true })

// Watch for changes in the active action and the number of selected records.
// We are watching the number of selected records to update the filtered records when the selection changes.
// If the watcher is on the list of selected records, it will not trigger when there is a deep change.

watch( [ activeId, selectedRecordsCount ], ( n ) => {

  if ( !n[0] ) { // --------------------------------------------------- If there is no active action,
    filteredRecords.value = [] // ------------------------------------- Clear the filtered records.
  }

  else {

    filteredRecords.value
      = activeAction.value?.filter // --------------------------------- If there is a filter function,
        ? props.records.filter( activeAction.value.filter ) // -------- Filter the records.
        : props.records // -------------------------------------------- Otherwise, return all selected records.

  }

}, { immediate: true })

</script>

<template>

  <div v-on-click-outside="() => openOptions = false" class="w-full h-full transition-[height]">

    <div
      class="w-full h-full relative z-9 overflow-hidden"
      :class="{
        'bg-main': !activeId,
        'bg-red-100': activeId && activeActionType === 'negative',
        'bg-green-300': activeId && activeActionType === 'positive',
        'bg-purple-300': activeId && activeActionType === 'neutral',
      }"
    >

      <Transition name="view" mode="out-in">

        <!-- Active Action Content -->

        <div
          v-if="activeId"
          class="h-full flex items-center"
          :class="{
            'text-red-700': activeActionType === 'negative',
            'text-green-700': activeActionType === 'positive',
            'text-purple-700': activeActionType === 'neutral',
          }"
        >

          <div
            class="text-xs md:text-sm px-4 flex items-center space-x-2"
            :class="{
              'text-red-700': activeActionType === 'negative',
              'text-green-700': activeActionType === 'positive',
              'text-purple-700': activeActionType === 'neutral',
            }"
          >

            <p>{{ activeAction.actionName }}:</p>

            <div
              class="text-xs h-6 grid place-content-center px-3 rounded-full"
              :class="{
                'bg-red-700 text-core-10': activeActionType === 'negative',
                'bg-green-700 text-core-10': activeActionType === 'positive',
                'bg-purple-700 text-core-10': activeActionType === 'neutral',
              }"
            >
              <span>{{ filteredRecordsCount }}</span>
            </div>

            <p>
              {{ activeAction.description ?? pluralize(resourceName, filteredRecordsCount) }}
            </p>

          </div>

          <div class="grow" />

          <div v-if="pending" class="h-full truncate px-4 grid grid-cols-[1fr_max-content] items-center gap-x-2">

            <p class="truncate text-xs md:text-sm">
              {{ activeAction.pendingMessage ?? 'Pending' }}
            </p>

            <Icon name="loading" />

          </div>

          <div v-else class="h-full">

            <!-- Desktop Options -->

            <div class="h-full flex items-center">

              <Button
                size="m"
                mode="naked"
                @click="executeAction(() => activeAction.action(filteredRecords))"
              >
                <p class="text-xs md:text-sm px-2 md:px-4">
                  Confirm
                </p>
              </Button>

              <div
                class="w-px h-4"
                :class="{
                  'bg-red-700': activeActionType === 'negative',
                  'bg-green-700': activeActionType === 'positive',
                  'bg-purple-700': activeActionType === 'neutral',
                }"
              />

              <Button
                size="m"
                mode="naked"
                @click="activeId = null"
              >
                <div class="h-full w-8 grid place-content-center">
                  <Icon name="close" />
                </div>
              </Button>

            </div>

          </div>

        </div>

        <!-- No Action Content -->

        <div v-else class="h-full flex items-center">

          <div class="text-sm pl-2 md:px-4 flex space-x-2 items-center">

            <p class="text-xs md:text-sm text-main h-6 grid place-content-center px-3 bg-main-30 rounded-full">
              {{ records.length }}
            </p>

            <p class="text-core-10 hidden md:inline-block">
              {{ handleSelectedMessage(allSelected, resourceName, selectedRecordsCount) }}
            </p>

          </div>

          <div v-if="!allOnPageSelected" class="w-px h-4 bg-core-10 hidden md:block" />

          <Button
            v-if="!allOnPageSelected"
            size="m"
            mode="naked"
            @click="$emit('selectAll')"
          >
            <p class="text-xs md:text-sm text-core-10 px-2 md:px-4">
              Select All ({{ pageTotal }})
            </p>
          </Button>

          <p v-else class="text-xs text-core-10 md:hidden px-2">
            All Selected
          </p>

          <div class="grow" />

          <!-- No Options Available -->

          <div v-if="noOptionsAvailable" class="h-full flex items-center space-x-2">

            <p class="text-xs md:text-sm text-core-10 px-2 md:px-3">
              No Options Available
            </p>

            <div class="w-px h-4 bg-core-10 hidden md:block" />

          </div>

          <!-- Available Options -->

          <div v-else class="h-full flex items-center">

            <!-- Option Selects :: Desktop -->

            <div
              v-for="group in groups"
              :key="String(group.name)"
              class="h-full hidden md:flex items-center"
            >

              <Button
                v-if="group.list.filter(o => !o.hidden).length > 1"
                size="m"
                mode="naked"
                :options="group.list"
              >
                <div class="text-sm text-core-10 px-4 flex items-center space-x-2">
                  <p>{{ group.name }}</p>
                  <Icon name="dots-vertical" />
                </div>
              </Button>

              <Button
                v-else-if="group.list.filter(o => !o.hidden).length === 1"
                size="m"
                mode="naked"
                @click="setActiveId(group.list.filter(o => !o.hidden)[0].id)"
              >
                <div class="text-core-10 px-4 flex items-center space-x-4">
                  <p class="text-sm">
                    {{ group.list.filter(o => !o.hidden)[0].name }}
                  </p>
                  <Icon :name="group.list.filter(o => !o.hidden)[0]?.icon?.name" size="m" />
                </div>
              </Button>

              <div v-if="group.list.filter(o => !o.hidden).length > 0" class="w-px h-4 bg-core-10" />

            </div>

            <!-- Option Select :: Mobile -->

            <Button
              size="m"
              mode="naked"
              class="md:hidden"
              @click="openOptions = !openOptions"
            >

              <div class="text-xs text-core-10 px-2 flex items-center space-x-2">

                <p>Options</p>
                <Icon
                  name="chevron-up"
                  :class="{
                    'transform rotate-180': openOptions,
                  }"
                />

              </div>

            </Button>

          </div>

          <div class="w-px h-4 bg-core-10 md:hidden" />

          <Button
            size="m"
            mode="naked"
            @click="$emit('close')"
          >

            <div class="h-8 w-8 text-core-10 grid place-content-center">
              <Icon name="close" />
            </div>

          </Button>

        </div>

      </Transition>

    </div>

    <!-- Options :: Mobile -->

    <Transition name="sidebar-modal-bottom" appear>

      <div v-if="openOptions && !activeId" class="w-full h-auto max-h-[26rem] absolute bottom-full z-8 md:hidden px-2 bg-core-10 border-t border-core-30 overflow-y-auto">

        <div
          v-for="group in groups"
          :key="String(group.name)"
          class="w-full py-4 grid gap-y-2 border-core-30"
          :class="{
            'border-b': group.list.filter(i => !i.hidden).length > 0,
            'hidden': group.list.filter(i => !i.hidden).length === 0,
          }"
        >

          <p v-if="group.list.filter(i => !i.hidden).length > 1" class="text-xs text-core-70 px-4">
            {{ group.name }}
          </p>

          <Button
            v-for="item in group.list.filter(i => !i.hidden)"
            :key="item.id"
            mode="naked"
            size="auto"
            @click="setActiveId(item.id)"
          >
            <div
              class="h-full text-left px-4 flex items-center bg-core-20 justify-between rounded-sm"
              :class="{
                'bg-green-100': item.color === 'success',
                'bg-red-100': item.color === 'error',
                'bg-purple-100': item.color === 'primary',
              }"
            >

              <div class="py-2">

                <p
                  class="text-xs font-medium"
                  :class="{
                    'text-green-700': item.color === 'success',
                    'text-red-700': item.color === 'error',
                    'text-purple-700': item.color === 'primary',
                  }"
                >
                  {{ item.name }}
                </p>

                <p class="text-xs text-core-70">
                  {{ item.description }}
                </p>

              </div>

              <Icon
                :name="item.icon.name"
                size="m"
                :class="{
                  'text-green-700': item.color === 'success',
                  'text-red-700': item.color === 'error',
                  'text-purple-700': item.color === 'primary',
                }"
              />

            </div>

          </Button>

        </div>

      </div>

    </Transition>

  </div>

</template>
