<script setup lang="ts" generic="R, Params extends BaseParams">

import { useRoute } from 'vue-router'
import { computed, ref } from 'vue'
import { saveTableToStorage } from '@lib/store/table'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/buttons/Button.vue'

import type { RouteLocationRaw } from 'vue-router'
import type { TableReference, TableReferenceCell } from '@lib/types/tableTypes'

interface CellProps {
  cell:                    TableReferenceCell
  flex?:                   boolean
  link?:                   RouteLocationRaw
  value?:                  string | boolean | TableSchemaItemComponent
  align?:                  'left' | 'center' | 'right'
  sticky?:                 'left' | 'right'
  resize?:                 boolean
  params?:                 Params
  status?:                 TableRecordStatus
  nested?:                 boolean
  trigger?:                { key: keyof R, name: string }
  sortKey?:                LooseAutoComplete<any>
  headCell?:               boolean
  reference:               TableReference
  cellClass?:              string
  rowSelected?:            boolean
  rowExpanded?:            boolean
  rowExpandedByKey?:       boolean
  sortDirectionUppercase?: boolean
}

const props = withDefaults( defineProps<CellProps>(), { align: 'left', resize: true })

const emits = defineEmits<{
  ( eventName: 'trigger', payload: string ): void
  ( eventName: 'dragging' ): void
  ( eventName: 'stopDrag' ): void
  ( eventName: 'setActive', payload: boolean ): void
}>()

const route           = useRoute()
const initX           = ref<number>( 0 )
const initW           = ref<number>( 0 )
const dragging        = ref<boolean>( false )
const hasStatus       = computed(() => props?.status?.type )
const viewParams      = defineModel<Params>( 'params' )
const sortActive      = computed(() => viewParams.value?.sortBy === props.sortKey )
const cellElement     = ref<HTMLDivElement>( null )
const isAscending     = computed<boolean>(() => props.sortDirectionUppercase ? viewParams.value?.sortDirection === 'ASC' : viewParams.value?.sortDirection === 'asc' )
const sortElement     = computed(() => ( props?.sortKey || props.trigger ) ? Button : 'div' )
const isCellHidden    = computed(() => cellReference.value.hide || !cellReference.value.toggle )
const cellReference   = defineModel<TableReferenceCell>( 'cell' )
const sortClickCount  = ref<number>( 1 )

function cellWidth( ref: TableReferenceCell ) {

  if ( !ref )
    return

  if ( ref.hide || !ref.toggle )
    return 0

  if ( !props.flex || ref.lockFlex )
    return ref.sizes.drag ? ref.sizes.drag : ref.sizes.init

  if ( ref.sizes.drag )
    return ref.sizes.drag

  return ref.sizes.flex

}

function sort() {

  if ( !props.sortKey )
    return

  if ( sortClickCount.value === 2 && viewParams.value?.sortBy === props.sortKey ) {

    viewParams.value.sortBy = null
    sortClickCount.value = 1
    return
  }

  sortClickCount.value++

  if ( viewParams.value?.sortBy !== props.sortKey ) {
    sortClickCount.value = 1
    viewParams.value.sortBy = props.sortKey

    if ( !viewParams.value?.sortDirection ) {
      viewParams.value.sortDirection = props.sortDirectionUppercase ? 'ASC' : 'asc'
    }

  }

  else {
    viewParams.value.sortDirection = props.sortDirectionUppercase
      ? viewParams.value?.sortDirection === 'ASC'
        ? 'DESC'
        : 'ASC'
      : viewParams.value?.sortDirection === 'asc'
        ? 'desc'
        : 'asc'
  }

}

function handleCellClick() {

  if ( props?.trigger )
    emits( 'trigger', props.trigger.name )

  if ( props?.sortKey )
    sort()

}

function startDrag( e: MouseEvent ) {

  if ( !props.resize )
    return

  const cellW = cellWidth( cellReference.value )

  initX.value = e.clientX
  initW.value = cellW
  dragging.value = true
  cellReference.value.sizes.drag = cellW

  window.addEventListener( 'mousemove', drag )
  window.addEventListener( 'mouseup', stopDrag )

}

function drag( e: MouseEvent ) {

  e.stopImmediatePropagation()
  e.stopPropagation()

  if ( dragging.value ) {

    const dragSize = initW.value + ( e.clientX - initX.value )

    cellReference.value.sizes.drag = Math.max( dragSize, 40 )
    emits( 'dragging' )

  }

}

function stopDrag() {

  dragging.value = false
  emits( 'stopDrag' )
  window.removeEventListener( 'mousemove', drag )
  window.removeEventListener( 'mouseup', stopDrag )

  saveTableToStorage( props.reference, route )

}

function extractSlots( value: TableSchemaItemComponent ) {

  if ( !value?.slots )
    return []

  return Object.entries( value.slots ).map(( [ key, val ] ) => ({ slotName: key, slotValue: val }))

}

</script>

<template>

  <div
    ref="cellElement"
    class="group/cell w-max h-full relative flex items-center"
    :class="{
      'relative': !sticky,
      'bg-core-20': headCell,
      'bg-error-20': hasStatus && status.type === 'error',
      'select-none': dragging,
      'overflow-hidden': isCellHidden,
      'bg-main-20 group-even/row:bg-main-20': rowSelected && !headCell && !hasStatus,
      'bg-core-30 group-even/row:bg-core-30': rowExpanded && !rowSelected && !hasStatus,
      'sticky left-0 z-3 bg-core-10 group-even/row:bg-core-20': sticky === 'left' && !nested,
      'sticky right-0 z-3 bg-core-10 group-even/row:bg-core-20': sticky === 'right' && !nested,
      'sticky left-0 z-3 bg-core-10 group-even/child:bg-core-20': sticky === 'left' && nested,
      'sticky right-0 z-3 bg-core-10 group-even/child:bg-core-20': sticky === 'right' && nested,
    }"
    :style="{ width: `${cellWidth(cellReference)}px` }"
  >

    <!-- Cell Content -->

    <component
      :is="sortElement"
      mode="naked"
      size="auto"
      class="truncate text-sm text-left w-full h-full grid grid-cols-[max-content_1fr_max-content] items-center justify-start"
      :class="{
        'pl-4': sortKey && !hasStatus,
        'px-4': !sortKey && !hasStatus,
        'pr-4': hasStatus,
        'border-l border-core-30': sticky === 'right',
        'border-r border-core-30': sticky === 'left',
        'border-l border-core-50': sticky === 'right' && rowExpanded && !rowSelected,
        'border-r border-core-50': sticky === 'left' && rowExpanded && !rowSelected,
      }"
      @click="handleCellClick"
    >

      <div
        class="h-full grid place-content-center overflow-hidden"
        :class="{
          'w-0 max-w-0': !hasStatus,
          'w-10 min-w-[2.5rem]': hasStatus,
        }"
      >

        <Icon
          v-if="hasStatus"
          v-tooltip:[status.type].bottom="{ content: props.status?.message }"
          :name="props.status.type === 'success' ? 'checkmark' : 'issue-circle'"
          size="m"
          :class="{
            'text-success': status.type === 'success',
            'text-warning': status.type === 'warning',
            'text-error': status.type === 'error',
          }"
        />

      </div>

      <slot>

        <div
          v-if="typeof value === 'object' && value?.hasOwnProperty('component')"
          class="grid"
          :class="{
            'place-content-center': align === 'center',
            'place-content-start': align === 'left',
            'place-content-end': align === 'right',
          }"
        >

          <component
            :is="value.component"
            v-bind="value?.props"
            @click="value?.onClick"
          >
            <p v-html="value?.slots?.default()" />

            <template
              v-for="s in extractSlots(value)"
              #[s.slotName]
              :key="s.slotName"
            >

              <p v-html="s.slotValue()" />

            </template>

          </component>

        </div>

        <div v-else-if="link">

          <router-link
            :to="link"
            class="truncate text-main-50 underline"
            :class="[cellClass, {
              'text-left': align === 'left',
              'text-right': align === 'right',
              'text-center': align === 'center',
            }]"
          >

            <p v-html="value" />

          </router-link>

        </div>

        <div v-else class="truncate flex items-center justify-between gap-x-2">

          <p
            class="truncate"
            :class="[cellClass, {
              'text-left': align === 'left',
              'text-right': align === 'right',
              'text-center': align === 'center',
              'text-error': hasStatus && status.type === 'error',
              'text-warning': hasStatus && status.type === 'warning',
              'text-success': hasStatus && status.type === 'success',
              'font-medium underline': trigger,
            }]"
            v-html="value"
          />

          <div>
            <Icon v-if="trigger" name="chevron-down" size="s" :class="{ 'rotate-180': rowExpandedByKey }" />
          </div>

        </div>

      </slot>

      <div
        v-if="!!sortKey"
        class="h-full px-2 grid place-content-center"
        :class="{
          'opacity-20 group-hover/cell:opacity-40': sortKey !== viewParams?.sortBy,
          'opacity-80 group-hover/cell:opacity-100': sortKey === viewParams?.sortBy,
        }"
      >

        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">

          <path
            d="M4 8L6 10L8 8"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="square"
            :class="{
              'text-core-60': sortActive && isAscending,
              'text-main': sortActive && !isAscending,
            }"
          />

          <path
            d="M8 4L6 2L4 4"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="square"
            :class="{
              'text-core-60': sortActive && !isAscending,
              'text-main': sortActive && isAscending,
            }"
          />

        </svg>

      </div>

    </component>

    <!-- Resize Handle -->

    <div
      v-if="resize && !cellReference.hide && cellReference.toggle && !cellReference.lockFlex"
      class="w-2 h-full absolute -right-1 group-last/cell:right-0 z-2 flex items-center justify-center cursor-col-resize"
      @mousedown="startDrag"
    >
      <div
        class="w-2 min-w-[0.5rem] h-2 relative after:absolute after:h-2 after:w-[1px] after:left-[44%] after:top-1.5 before:absolute before:h-2 before:w-[1px] before:left-[44%] before:-top-2"
        :class="{
          'border-main border rounded-full after:bg-main before:bg-main': dragging,
          'group-hover/cell:after:bg-core-60 group-hover/cell:before:bg-core-60': !dragging,
          'group-hover/cell:border rounded-full border-core-60 hover:border-main-50': !dragging,
          'group-hover/cell:hover:before:bg-main-50 group-hover/cell:hover:after:bg-main-50': !dragging,
        }"
      />
    </div>

  </div>

</template>
