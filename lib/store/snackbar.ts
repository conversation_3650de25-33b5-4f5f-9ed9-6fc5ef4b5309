import { ref } from 'vue'
import { generateUniqueId } from '@lib/scripts/utils'

import type { AlertOptions, NotificationOptions } from '@lib/types/snackbarTypes'

const alertDefaultOptions: AlertOptions = {
  id:       null,
  open:     false,
  duration: 6000,
  message:  null,
  details:  null,
  severity: 'naked',
  strict:   false,
}

export const alertQueue   = ref<AlertOptions[]>( [] )
export const alertOptions = ref<AlertOptions>( alertDefaultOptions )

export function setAlertOptions( alertProps: Omit<AlertOptions, 'id' | 'open'> ) {
  alertQueue.value.push({ ...alertDefaultOptions, ...alertProps, id: generateUniqueId() })
}

const notificationDefaultOptions: NotificationOptions = {
  id:       null,
  open:     false,
  duration: 6000,
  message:  null,
  details:  null,
  strict:   false,
}

export const notificationQueue = ref<NotificationOptions[]>( [] )

export function setNotificationOptions( notificationProps: Omit<NotificationOptions, 'id' | 'open' | 'duration'> ) {
  notificationQueue.value.push({ ...notificationDefaultOptions, ...notificationProps, id: generateUniqueId() })
}
