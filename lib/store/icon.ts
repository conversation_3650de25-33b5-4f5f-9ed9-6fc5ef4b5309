export type IconName =
  | 'empty-box'
  | 'close'
  | 'go-to'
  | 'add'
  | 'minus'
  | 'reports-logo'
  | 'reports-logo-dark'
  | 'edit'
  | 'delete'
  | 'settings'
  | 'loading'
  | 'chevron-down'
  | 'chevron-up'
  | 'chevron-left'
  | 'chevron-right'
  | 'dots-vertical'
  | 'dots-arrow-left'
  | 'checkmark'
  | 'source'
  | 'database'
  | 'folder'
  | 'table'
  | 'view'
  | 'column'
  | 'key'
  | 'trigger'
  | 'constrain'
  | 'index'
  | 'stats'
  | 'report'
  | 'bell'
  | 'search'
  | 'options'
  | 'send'
  | 'red-dot'
  | 'sun'
  | 'moon'
  | 'design-system-logo'
  | 'component'
  | 'locked'
  | 'logout'
  | 'owd-logo'
  | 'calendar'
  | 'upload'
  | 'batch-upload'
  | 'menu'
  | 'sort'
  | 'user'
  | 'file'
  | 'bill'
  | 'info'
  | 'product'
  | 'draft-product'
  | 'refresh'
  | 'download'
  | 'download-file'
  | 'portal-dashboard'
  | 'portal-orders'
  | 'portal-asn'
  | 'portal-inventory'
  | 'portal-low-inventory'
  | 'portal-announcements'
  | 'portal-orders-data'
  | 'portal-orders-create'
  | 'portal-asns-create'
  | 'portal-reports'
  | 'portal-extranet'
  | 'portal-toggle'
  | 'pending'
  | 'shipped'
  | 'arrived'
  | 'received'
  | 'in-progress'
  | 'partial'
  | 'out-of-stock'
  | 'received-platform'
  | 'stacked-boxes'
  | 'at-warehouse'
  | 'canceled'
  | 'portal-invoice'
  | 'portal-contract'
  | 'dashboard'
  | 'activities'
  | 'eye-open'
  | 'export'
  | 'live'
  | 'details'
  | 'clone'
  | 'attributes'
  | 'dollar'
  | 'supplier'
  | 'bulk'
  | 'issue-circle'
  | 'products-group'
  | 'external-link'
  | 'go'
  | 'create-bulk-asns'
  | 'create-bulk-order'
  | 'canceled-asn'
  | 'reset'
  | 'eye-open'
  | 'order-data'
  | 'customs'
  | 'shaded-product'
  | 'exclamation-mark'
  | 'profile'
  | 'no-more-records'
  | 'draft-order'
  | 'resolve'
  | 'locked'
  | 'warehouse'
  | 'warning-sign'
  | 'reset-columns'
  | 'draft-failed-import'
  | 'draft-pending-import'
  | 'draft-ready-to-import'
  | 'draft-validation-errors'
  | 'client-panel-open'
  | 'clients'
  | 'circle-dash'
  | 'incomplete-normal'
  | 'checkmark-outline'
  | 'warning-outline'
  | 'shipped-widget'
  | 'on-hold'
  | 'backorder'
  | 'at-warehouse-widget'
  | 'void'
  | 'arrow-right'
  | 'checkbox-checked'
  | 'checkbox-partial'
  | 'checkbox-unchecked'
  | 'radio-checked'
  | 'radio-unchecked'
  | 'filter'

export interface IconSize {
  s:  12
  m:  16
  l:  24
  xl: 32
}

interface IconVariant {
  dark?:  string
  light?: string
}

export interface IconProps {
  size?:    keyof IconSize
  name?:    IconName
  color?:   string
  variant?: keyof IconVariant
}

interface IconItem {
  name:  IconName
  sizes: Partial<Record<keyof IconSize, string | IconVariant>>
}

/*
 * New Icons
**/

export function iconsList( color: string ): IconItem[] {

  return [
    {
      name:  'empty-box',
      sizes: {
        s:  `<rect x="1" y="1" width="10" height="10" stroke="${color}" stroke-width="2"/>`,
        m:  `<rect x="1" y="1" width="14" height="14" stroke="${color}" stroke-width="2"/>`,
        l:  `<rect x="1" y="1" width="22" height="22" stroke="${color}" stroke-width="2"/>`,
        xl: `<rect x="1" y="1" width="30" height="30" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'chevron-down',
      sizes: {
        s: `<path d="M2 4L6 8L10 4" stroke="${color}" stroke-width="2"/>`,
        m: `<path d="M2 5L8 11L14 5" stroke="${color}" stroke-width="2"/>`,
        l: `<path d="M2 7L12 17L22 7" stroke="${color}" stroke-width="2"/>`,
      }
    },
    {
      name:  'chevron-up',
      sizes: {
        s: `<path d="M10 8L6 4L2 8" stroke="${color}" stroke-width="2"/>`,
        m: `<path d="M14 11L8 5L2 11" stroke="${color}" stroke-width="2"/>`,
        l: `<path d="M22 17L12 7L2 17" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'chevron-left',
      sizes: {
        s: `<path d="M8 2L4 6L8 10" stroke="${color}" stroke-width="2"/>`,
        m: `<path d="M11 2L5 8L11 14" stroke="${color}" stroke-width="2"/>`,
        l: `<path d="M17 2L7 12L17 22" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'chevron-right',
      sizes: {
        s: `<path d="M4 10L8 6L4 2" stroke="${color}" stroke-width="2"/>`,
        m: `<path d="M5 14L11 8L5 2" stroke="${color}" stroke-width="2"/>`,
        l: `<path d="M7 22L17 12L7 2" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'settings',
      sizes: {
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M9 0V2.08296C9.91168 2.23591 10.7536 2.59446 11.4766 3.10929L12.9499 1.63602L14.3641 3.05023L12.8908 4.52352C13.4056 5.24644 13.7641 6.08837 13.917 7L16 7V9H13.917C13.7641 9.91165 13.4056 10.7536 12.8908 11.4765L14.364 12.9498L12.9498 14.364L11.4766 12.8907C10.7536 13.4056 9.91166 13.7641 9 13.917V16H7L7 13.917C6.08835 13.7641 5.24641 13.4056 4.52348 12.8908L3.05029 14.364L1.63608 12.9497L3.10927 11.4766C2.59444 10.7536 2.23591 9.91166 2.08296 9H0V7L2.08296 7C2.2359 6.08835 2.59443 5.24641 3.10924 4.52348L1.63602 3.05026L3.05023 1.63605L4.52345 3.10926C5.24639 2.59444 6.08834 2.23591 7 2.08296L7 0H9ZM4 8C4 5.79086 5.79086 4 8 4C10.2091 4 12 5.79086 12 8C12 10.2091 10.2091 12 8 12C6.90142 12 5.90627 11.5571 5.18336 10.8402L5.15983 10.8166C4.44288 10.0937 4 9.09859 4 8ZM8 10C9.10457 10 10 9.10457 10 8C10 6.89543 9.10457 6 8 6C6.89543 6 6 6.89543 6 8C6 9.10457 6.89543 10 8 10Z" fill="${color}"/>`
      }
    },
    {
      name:  'menu',
      sizes: {
        m:  `<path fill-rule="evenodd" clip-rule="evenodd" d="M0 2H16V4H0V2ZM0 7H16V9H0V7ZM16 12H0V14H16V12Z" fill="${color}"/>`,
        l:  `<path d="M2 6H22" stroke="${color}" stroke-width="2"/><path d="M2 18H22" stroke="${color}" stroke-width="2"/><path d="M2 12H22" stroke="${color}" stroke-width="2"/>`,
        xl: `<path d="M6 8H26" stroke="${color}" stroke-width="2"/><path d="M6 24H26" stroke="${color}" stroke-width="2"/><path d="M6 16H26" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'add',
      sizes: {
        s: `<path fill-rule="evenodd" clip-rule="evenodd" d="M7 0H5V5H0V7H5V12H7V7H12V5H7V0Z" fill="${color}"/>`,
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M9 0H7V7H0V9H7V16H9V9H16V7H9V0Z" fill="${color}"/>`,
      }
    },
    {
      name:  'minus',
      sizes: {
        s: `<path d="M12 5V7L0 7L8.74228e-08 5L12 5Z" fill="${color}"/>`
      }
    },
    {
      name:  'delete',
      sizes: {
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M11 2V0H5V2H3L0 2V4L3 4V16H4H5H11H12H13V4L16 4V2H13L11 2ZM11 4L5 4V14H11V4Z" fill="${color}"/>`
      }
    },
    {
      name:  'close',
      sizes: {
        s: `<path fill-rule="evenodd" clip-rule="evenodd" d="M6 7.41421L9.29289 10.7071L10.7071 9.29289L7.41421 6L10.7071 2.70711L9.29289 1.29289L6 4.58579L2.70711 1.29289L1.29289 2.70711L4.58579 6L1.29289 9.29289L2.70711 10.7071L6 7.41421Z" fill="${color}"/>`,
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M14.364 3.05026L12.9497 1.63604L8 6.58579L3.05025 1.63604L1.63604 3.05025L6.58579 8L1.63604 12.9498L3.05025 14.364L8 9.41422L12.9497 14.364L14.364 12.9498L9.41421 8L14.364 3.05026Z" fill="${color}"/>`,
        l: `<path d="M5 5.1393L19 19" stroke="${color}" stroke-width="2"/><path d="M5 18.8607L19 5" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'checkmark',
      sizes: {
        s: `<path d="M2.5 5.5L5 8L10 3" stroke="${color}" stroke-width="2"/>`,
        m: `<path d="M3 9L6 12L13 4" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'edit',
      sizes: {
        s: `<path d="M1 8.17157L7.58452 1.58706C8.36511 0.806464 9.63168 0.809663 10.4083 1.59419C11.1785 2.37214 11.1764 3.62582 10.4037 4.40125L3.82843 11L0.999998 11L1 8.17157Z" stroke="${color}" stroke-width="2"/><path d="M6.5 3.5L8.5 5.5" stroke="${color}" stroke-width="2"/>`,
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M15.1281 5.09549C16.2955 3.92562 16.2907 2.03121 15.1173 0.867298C13.9485 -0.292029 12.0612 -0.288673 10.8966 0.8748L0.000510455 11.7598L0.000507756 16L4.24591 16L15.1281 5.09549ZM13.7075 2.28572C14.0956 2.6707 14.0972 3.29731 13.7111 3.68426L12.4196 4.97839L11.019 3.57926L12.3114 2.2882C12.6966 1.90336 13.3209 1.90225 13.7075 2.28572ZM9.6042 4.99266L2.00139 12.5878L2.00139 14.0012L3.41532 14.0011L11.0069 6.39395L9.6042 4.99266Z" fill="${color}"/>`
      }
    },
    {
      name:  'search',
      sizes: {
        s: `<circle cx="5" cy="5" r="4" stroke="${color}" stroke-width="2"/><path d="M8 8L11 11" stroke="${color}" stroke-width="2"/>`,
        m: `<circle cx="6.5" cy="6.5" r="5.5" stroke="${color}" stroke-width="2"/><path d="M11 11L15 15" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'dashboard',
      sizes: {
        m: `<rect width="16" height="16" fill="white" fill-opacity="0.01" style="mix-blend-mode:multiply"/><rect x="1" y="1" width="5" height="5" stroke="${color}" stroke-width="2"/><rect x="1" y="6" width="14" height="9" stroke="${color}" stroke-width="2"/><rect x="6" y="1" width="9" height="5" stroke="${color}" stroke-width="2"/>`,
        l: `<rect x="3" y="3" width="7" height="7" stroke="${color}" stroke-width="2"/><rect x="3" y="14" width="7" height="7" stroke="${color}" stroke-width="2"/><rect x="14" y="3" width="7" height="7" stroke="${color}" stroke-width="2"/><rect x="14" y="14" width="7" height="7" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'logout',
      sizes: {
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M16 16L14.0018 16L10.0053 16L10.0053 14L14.0018 14L14.0018 2L10.0053 2L10.0053 -5.24073e-07L14.0018 -1.74691e-07L16 0L16 2L16 14L16 16ZM3.82506 9L12.0035 9L12.0035 7L3.82503 7L6.11592 4.70709L4.70295 3.29288L6.99383e-07 7.99998L4.70295 12.7071L6.11591 11.2929L3.82506 9Z" fill="${color}"/>`
      }
    },
    {
      name:  'portal-announcements',
      sizes: {
        l: `<path fill-rule="evenodd" clip-rule="evenodd" d="M0.888788 15.8616L0.684772 15.9794C0.20648 16.2555 0.0426047 16.8671 0.318747 17.3454L1.31875 19.0774C1.59489 19.5557 2.20648 19.7196 2.68477 19.4435L3.0888 19.2102C3.20961 19.3024 3.33362 19.3898 3.46051 19.4724L10.0007 23.9088L13.9741 21.2069L12.0083 18.8478L17.1068 16.8647L23.4273 15.3706L20.5856 10.4487C21.9542 9.59777 22.4098 7.80788 21.5981 6.40192C20.7864 4.99597 19.0085 4.49562 17.5873 5.25536L14.5833 0.0522461L11.5594 3.26099C10.626 4.25146 9.58889 5.13878 8.46592 5.90766L3.30631 9.44035C1.19028 10.8892 0.299257 13.4854 0.888788 15.8616ZM10.0422 19.6126L8.91112 20.0525C8.67933 20.1427 8.44489 20.2172 8.20901 20.2767L9.99934 21.4912L11.0259 20.7931L10.0422 19.6126ZM4.57548 17.812C5.62881 18.4869 6.9691 18.6619 8.18612 18.1885L16.3032 15.0313L11.2585 6.2936C10.726 6.74204 10.1711 7.16404 9.59581 7.55792L4.43621 11.0906C2.72064 12.2652 2.20267 14.6067 3.25189 16.4257L3.25273 16.4272L3.25356 16.4286C3.58318 16.999 4.03611 17.4632 4.56116 17.8028L4.56139 17.8024L4.57548 17.812ZM20.3512 14.0426L18.3192 14.523L12.7615 4.89691C12.8467 4.80958 12.9312 4.72149 13.0149 4.63265L14.1952 3.38018L20.3512 14.0426ZM19.8661 7.40192C20.1254 7.85105 19.9967 8.4177 19.584 8.71379L18.5889 6.99026C19.0517 6.78092 19.6068 6.9528 19.8661 7.40192Z" fill="${color}"/>`
      }
    },
    {
      name:  'portal-orders',
      sizes: {
        l: `<rect x="3" y="1" width="18" height="22" stroke="${color}" stroke-width="2"/><rect x="10" y="5" width="8" height="2" fill="${color}"/><rect x="10" y="11" width="8" height="2" fill="${color}"/><rect x="10" y="17" width="8" height="2" fill="${color}"/><rect x="6" y="5" width="2" height="2" fill="${color}"/><rect x="6" y="11" width="2" height="2" fill="${color}"/><rect x="6" y="17" width="2" height="2" fill="${color}"/>`,
        m: `<path d="M1 1H11C13.2091 1 15 2.79086 15 5V15H1V1Z" stroke="${color}" stroke-width="2"/><rect x="5" y="5" width="6" height="2" fill="${color}"/><rect x="5" y="9" width="6" height="2" fill="${color}"/>`
      }
    },
    {
      name:  'portal-asn',
      sizes: {
        l: `<path d="M14.7778 2H22V9.72727M9.22222 2H2V9.72727M9.22222 22H2V14.2727M14.7778 22H22V14.2727" stroke="${color}" stroke-width="2"/><rect x="7" y="7" width="10" height="2" fill="${color}"/><rect x="7" y="11" width="10" height="2" fill="${color}"/><rect x="7" y="15" width="6" height="2" fill="${color}"/><rect x="15" y="15" width="2" height="2" fill="${color}"/>`,
        m: `<path d="M10.9444 1H15V5.40909M5.05556 1H1V5.40909M5.05556 15H1V10.5909M10.9444 15H15V10.5909" stroke="${color}" stroke-width="2"/><rect x="4" y="5" width="8" height="2" fill="${color}"/><rect x="4" y="9" width="8" height="2" fill="${color}"/>`
      }
    },
    {
      name:  'portal-inventory',
      sizes: {
        l: `<path fill-rule="evenodd" clip-rule="evenodd" d="M19 3H21V11H13V3H15V5H19V3ZM11 13V1H23V13H11Z" fill="${color}"/><path fill-rule="evenodd" clip-rule="evenodd" d="M14.4141 13H21V19.5859L14.4141 13ZM13 14.4144V21H19.5856L13 14.4144ZM11 23V11H23V23H11Z" fill="${color}"/><path fill-rule="evenodd" clip-rule="evenodd" d="M9 13H11V21H3V13H5V15H9V13ZM1 23V11H13V23H1Z" fill="${color}"/>`,
        m: `<rect x="1" y="8" width="7" height="7" stroke="${color}" stroke-width="2"/><rect x="8" y="8" width="7" height="7" stroke="${color}" stroke-width="2"/><rect x="8" y="1" width="7" height="7" stroke="${color}" stroke-width="2"/><path d="M11.5 1V3" stroke="${color}" stroke-width="2"/><path d="M4.5 8V10" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'portal-low-inventory',
      sizes: {
        l: `<path fill-rule="evenodd" clip-rule="evenodd" d="M2.00713 10.5857L1.42143 10L0.00721741 11.4142L3.00721 14.4142L6.00704 11.4142L4.59282 10L4.00713 10.5857L4.00713 4L20.0071 4V7.00001L22.0071 7.00001V2L2.00713 2V10.5857ZM20.0071 20V12.4142L19.4213 13L18.0071 11.5858L21.0071 8.58585L24.0001 11.5788L22.5859 12.993L22.0071 12.4143V22L2.00713 22V17H4.00713V20L20.0071 20ZM9.00713 13H15.0071V11H9.00713V13Z" fill="${color}"/>`
      }
    },
    {
      name:  'portal-invoice',
      sizes: {
        l: `<path d="M3.8753 21.2191L3 21.9194V1H21V21.9194L20.1247 21.2191L19.5 20.7194L18.8753 21.2191L17 22.7194L15.1247 21.2191L14.5 20.7194L13.8753 21.2191L12 22.7194L10.1247 21.2191L9.5 20.7194L8.87531 21.2191L7 22.7194L5.1247 21.2191L4.5 20.7194L3.8753 21.2191Z" stroke="${color}" stroke-width="2"></path><circle cx="12" cy="11" r="4" fill="${color}"></circle>`
      }
    },
    {
      name:  'portal-contract',
      sizes: {
        l: `<rect x="10" y="5" width="8" height="2" fill="${color}"></rect><rect x="10" y="9" width="8" height="2" fill="${color}"></rect><path fill-rule="evenodd" clip-rule="evenodd" d="M20 2H4V14H20V2ZM2 0V24H22V0H2ZM16.516 17.4142L11.298 22.6323L11.0556 22.3898L11.0421 22.4033L8 19.3611L9.41421 17.9469L11.2845 19.8173L15.1018 16L16.516 17.4142Z" fill="${color}"></path><rect x="6" y="5" width="2" height="2" fill="${color}"></rect><rect x="6" y="9" width="2" height="2" fill="${color}"></rect>`
      }
    },
    {
      name:  'portal-reports',
      sizes: {
        l: `<path d="M1 3H23V21H1V3Z" stroke="${color}" stroke-width="2"/><path d="M5 3V21" stroke="${color}" stroke-width="2"/><path d="M9 3V21" stroke="${color}" stroke-width="2"/><path d="M23 9L1 9" stroke="${color}" stroke-width="2"/><path d="M23 15L1 15" stroke="${color}" stroke-width="2"/>`,
        m: `<path d="M1 1H15V15H1V1Z" stroke="${color}" stroke-width="2"/><path d="M6.09091 2V14" stroke="${color}" stroke-width="2"/><path d="M15 7L1 7" stroke="${color}" stroke-width="2"/><path d="M14 11L0 11" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'portal-extranet',
      sizes: {
        l: `<rect x="2" y="16" width="6" height="6" stroke="${color}" stroke-width="2"/><rect x="16" y="2" width="6" height="6" stroke="${color}" stroke-width="2"/><path d="M19 12C19 15.866 15.866 19 12 19M5 12C5 8.13401 8.13401 5 12 5" stroke="${color}" stroke-width="2"/><rect x="1" y="1" width="3" height="3" fill="${color}"/><rect x="20" y="20" width="3" height="3" fill="${color}"/>`,
        m: `<rect x="1" y="11" width="4" height="4" stroke="${color}" stroke-width="2"/><rect x="11" y="1" width="4" height="4" stroke="${color}" stroke-width="2"/><path d="M13 8C13 10.7614 10.7614 13 8 13M3 8C3 5.23858 5.23858 3 8 3" stroke="${color}" stroke-width="2"/><rect width="2" height="2" fill="${color}"/><rect x="14" y="14" width="2" height="2" fill="${color}"/>`
      }
    },
    {
      name:  'pending',
      sizes: {
        l: `<path fill-rule="evenodd" clip-rule="evenodd" d="M7.5 2H2V7.5H4V4H7.5V2ZM16.5 4H20V7.5H22V2H16.5V4ZM4 16.5V20H7.5V22H2V16.5H4ZM20 16.5V20H16.5V22H22V16.5H20ZM13 6.5H11V12.9806L13.8753 15.2809L15.1247 13.7191L13 12.0194V6.5Z" fill="${color}"/>`
      }
    },
    {
      name:  'received',
      sizes: {
        l: `<path fill-rule="evenodd" clip-rule="evenodd" d="M11 11V2H13V11H11ZM4 20V3H2V22H22V3H20V20H4ZM9.20718 11.7928L7.79297 13.2071L12.0001 17.4142L16.2072 13.2071L14.793 11.7928L12.0001 14.5857L9.20718 11.7928Z" fill="${color}"/>`,
        m: `<path d="M4 6L8 10L12 6" stroke="${color}" stroke-width="2"/><path d="M8 9V0" stroke="${color}" stroke-width="2"/><path d="M15 11L15 15H1V11" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'in-progress',
      sizes: {
        m: `<rect width="16" height="16" fill="${color}" fill-opacity="0.01" style="mix-blend-mode:multiply"/><path d="M8 1C6.61553 1 5.26216 1.41054 4.11101 2.17971C2.95987 2.94888 2.06266 4.04213 1.53285 5.32122C1.00303 6.6003 0.86441 8.00776 1.13451 9.36563C1.4046 10.7235 2.07129 11.9708 3.05026 12.9497C4.02922 13.9287 5.2765 14.5954 6.63437 14.8655C7.99224 15.1356 9.3997 14.997 10.6788 14.4672C11.9579 13.9373 13.0511 13.0401 13.8203 11.889C14.5895 10.7378 15 9.38447 15 8C14.9979 6.14413 14.2597 4.36486 12.9474 3.05256C11.6351 1.74026 9.85588 1.00209 8 1ZM8 14C6.4087 14 4.88258 13.3679 3.75736 12.2426C2.63214 11.1174 2 9.5913 2 8C2 6.4087 2.63214 4.88258 3.75736 3.75736C4.88258 2.63214 6.4087 2 8 2V8L12.2406 12.2407C11.6845 12.7988 11.0236 13.2415 10.2958 13.5434C9.56809 13.8453 8.78788 14.0005 8 14Z" fill="${color}"/>`,
        l: `<path fill-rule="evenodd" clip-rule="evenodd" d="M14 1.41421L12.5858 0L9.37868 3.20711L12.5858 6.41421L14 5L13.0711 4.07108C16.9829 4.5945 20 7.94491 20 12C20 14.7817 18.5811 17.2324 16.423 18.6673L17.5304 20.3327C20.2223 18.5429 22 15.4793 22 12C22 6.92702 18.2225 2.73633 13.3269 2.08727L14 1.41421ZM7.4298 5.43297C7.47166 5.40377 7.51382 5.37497 7.55626 5.34656L6.44374 3.68454C6.39071 3.72004 6.33804 3.75603 6.28574 3.7925C3.6971 5.59781 2 8.60064 2 12C2 17.073 5.77747 21.2637 10.6731 21.9127L10 22.5858L11.4142 24L14.6213 20.7929L11.4142 17.5858L10 19L10.9289 19.9289C7.01708 19.4055 4 16.0551 4 12C4 9.28204 5.35449 6.88028 7.4298 5.43297ZM12 14C13.1046 14 14 13.1046 14 12C14 10.8954 13.1046 10 12 10C10.8954 10 10 10.8954 10 12C10 13.1046 10.8954 14 12 14Z" fill="${color}"/>`
      }
    },
    {
      name:  'at-warehouse',
      sizes: {
        l: `<path fill-rule="evenodd" clip-rule="evenodd" d="M18.3799 14.7929L19.7941 16.2072L24.0012 12L19.7941 7.79294L18.3799 9.20715L21.1728 12L18.3799 14.7929ZM20.9999 4L3.9999 4L3.9999 20L20.9999 20L20.9999 22L1.9999 22L1.9999 2L20.9999 2L20.9999 4ZM16.9998 11L13.4998 11L13.4998 7.5L11.4998 7.5L11.4998 11L7.99976 11L7.99976 13L11.4998 13L11.4998 16.5L13.4998 16.5L13.4998 13L16.9998 13L16.9998 11Z" fill="${color}"></path>`
      }
    },
    {
      name:  'shipped',
      sizes: {
        l: `<path fill-rule="evenodd" clip-rule="evenodd" d="M14 16V9.00012V7.00012V5.00018H5V3.00018H14V3H16V7.30712L24 10.3071V17.0001H23.9998V18.0001L19.8291 18.0001C19.9396 18.3129 19.9998 18.6495 19.9998 19.0001C19.9998 20.657 18.6566 22.0001 16.9998 22.0001C15.3429 22.0001 13.9998 20.657 13.9998 19.0001C13.9998 18.6494 14.0599 18.3128 14.1705 18H5.82929C5.93985 18.3128 6 18.6494 6 19C6 20.6569 4.65685 22 3 22C1.34315 22 0 20.6569 0 19C0 17.3431 1.34315 16 3 16H9H14ZM16 16.1707V9.44312L22 11.6931V16.0001L16.9998 16.0001C16.6492 16.0001 16.3127 16.0602 16 16.1707ZM3 18C2.44772 18 2 18.4477 2 19C2 19.5523 2.44772 20 3 20C3.55228 20 4 19.5523 4 19C4 18.4477 3.55228 18 3 18ZM16.9998 18.0001C16.4475 18.0001 15.9998 18.4478 15.9998 19.0001C15.9998 19.5524 16.4475 20.0001 16.9998 20.0001C17.552 20.0001 17.9998 19.5524 17.9998 19.0001C17.9998 18.4478 17.552 18.0001 16.9998 18.0001ZM4.63598 14.14L10.6989 8.21519L9.30107 6.78478L4.67102 11.3093L2.71638 9.30229L1.28359 10.6977L4.63598 14.14Z" fill="${color}"></path>`,
        m: `<path d="M3 2H10V11H2.5" stroke="${color}" stroke-width="2"/><path d="M10 5L15 7V11H11.5" stroke="${color}" stroke-width="2"/><circle cx="2.5" cy="12.5" r="1.5" stroke="${color}" stroke-width="2"/><circle cx="11.5" cy="12.5" r="1.5" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'arrived',
      sizes: {
        l: `<path fill-rule="evenodd" clip-rule="evenodd" d="M11 7V2H13V7H11ZM4 15V3H2V22H22V3H20V15H4ZM14.9194 17H11.4807L9.08068 20H12.5194L14.9194 17ZM4 17H8.91943L6.51943 20H4V17ZM17.4807 17L15.0807 20H20V17H17.4807ZM9.20718 7.29285L7.79297 8.70706L12.0001 12.9142L16.2072 8.70706L14.793 7.29285L12.0001 10.0857L9.20718 7.29285Z" fill="${color}"/>`
      }
    },
    {
      name:  'out-of-stock',
      sizes: {
        l: `<path fill-rule="evenodd" clip-rule="evenodd" d="M20 4H15V6H9V4H4V20H20V4ZM22 2H15H9H2V22H22V2ZM8 10.4141L9.41421 8.99988L11.8661 11.4518L14.3179 9.00003L15.7321 10.4142L13.2803 12.866L15.7321 15.3177L14.3178 16.7319L11.8661 14.2802L9.41424 16.7321L8.00003 15.3179L10.4519 12.866L8 10.4141Z" fill="${color}"/>`
      }
    },
    {
      name:  'column',
      sizes: {
        m: `<rect x="16" y="2" width="2" height="16" transform="rotate(90 16 2)" fill="${color}"/>
        <rect x="2" y="14" width="2" height="12" transform="rotate(-180 2 14)" fill="${color}"/>
        <rect x="7" y="14" width="2" height="12" transform="rotate(-180 7 14)" fill="${color}"/>
        <rect x="16" y="14" width="2" height="12" transform="rotate(-180 16 14)" fill="${color}"/>
        <rect x="16" y="12" width="2" height="16" transform="rotate(90 16 12)" fill="${color}"/>
        <rect x="16" y="7" width="2" height="16" transform="rotate(90 16 7)" fill="${color}"/>
        `
      }
    },
    {
      name:  'user',
      sizes: {
        s: `<circle cx="6" cy="4" r="3" stroke="${color}" stroke-width="2"/><rect x="1" y="10" width="10" height="2" fill="${color}"/>`
      }
    },
    {
      name:  'calendar',
      sizes: {
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M4 0H6V1H10V0H12V1H16V16H0V1H4V0ZM7 5H4V7H7V5ZM4 10H7V12H4V10ZM12 5H9V7H12V5ZM9 10H12V12H9V10ZM2 14V3H14V14H2Z" fill="${color}"/>`
      }
    },
    {
      name:  'folder',
      sizes: {
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M16 2V0H6V2L6 4H2L0 4V6V14V16H2H14H16V14V6V4V2ZM14 2H8L8 4H14V2ZM6 6H2L2 14L14 14V6H8H6Z" fill="${color}"/>`
      }
    },
    {
      name:  'upload',
      sizes: {
        m: `<path d="M11 4.5L8 1.5L5 4.5" stroke="${color}" stroke-width="2"/><path d="M1 6V15H15V6" stroke="${color}" stroke-width="2"/><path d="M8 2V10" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'batch-upload',
      sizes: {
        s: `
        <g clip-path="url(#clip0_444_308)">
        <path d="M3 8L6 5L9 8" stroke="${color}" stroke-width="2"/>
        <path d="M0 1H12" stroke="${color}" stroke-width="2"/>
        <path d="M6 12L6 5" stroke="${color}" stroke-width="2"/>
        </g>
        <defs>
        <clipPath id="clip0_444_308">
        <rect width="12" height="12" fill="${color}"/>
        </clipPath>
        </defs>
        `,
        m: `
        <path d="M11 8.5L8 5.5L5 8.5" stroke="${color}" stroke-width="2"/>
        <path d="M1 5V1H15V5" stroke="${color}" stroke-width="2"/>
        <path d="M8 6V14" stroke="${color}" stroke-width="2"/>
        `
      }
    },
    {
      name:  'download',
      sizes: {
        m: `<path d="M5 11.1111L8 14L11 11.1111" stroke="${color}" stroke-width="2"/><path d="M15 9.66666L15 0.99999L1 0.999988L1 9.66666" stroke="${color}" stroke-width="2"/><path d="M8 14L8 5.81481" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'info',
      sizes: {
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16ZM9 3V5H7V3H9ZM6 9H7V13H9V7H8H7H6V9Z" fill="${color}"/>`
      }
    },
    {
      name:  'portal-orders-data',
      sizes: {
        m: `<path d="M1 9V15H15V9.30769V9" stroke="${color}" stroke-width="2"/><path d="M4 8L7 5L9 7L13 3" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'portal-orders-create',
      sizes: {
        m: `<path d="M13 9V15H1V3H7" stroke="${color}" stroke-width="2"/><path d="M13 0V6" stroke="${color}" stroke-width="2"/><path d="M16 3L10 3" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'portal-asns-create',
      sizes: {
        m: `<path d="M13 3V1H1V8V15H13V13" stroke="${color}" stroke-width="2"/><path d="M13 5V11" stroke="${color}" stroke-width="2"/><path d="M16 8L10 8" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'bill',
      sizes: {
        m: `<rect x="2" y="1" width="12" height="14" stroke="${color}" stroke-width="2"/><path d="M4 5H12" stroke="${color}" stroke-width="2"/><path d="M4 8H12" stroke="${color}" stroke-width="2"/><path d="M10 12H12" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'product',
      sizes: {
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M8 0L16 3.88196V12.118L8 16L0 12.118V3.88196L8 0ZM2 10.882V6.11802L7 8.61802V13.2836L2 10.882ZM9 13.2836L14 10.882V6.11802L9 8.61802V13.2836ZM8 2.23607L12.7391 4.51239L8 6.88195L3.26088 4.51239L8 2.23607Z" fill="${color}"/>`
      }
    },
    {
      name:  'draft-product',
      sizes: {
        s: `<path fill-rule="evenodd" clip-rule="evenodd" d="M12 2.86963L6 0.00158691L0 2.86963V9.13037L6 11.9983L12 9.13037V2.86963ZM2 7.86962V4.13036L6 2.21833L9.86671 4.06665L6 6V9.78161L2 7.86962Z" fill="${color}"/>`,
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M16 3.87237L8 0.00952148L0 3.87237V12.1292L8 15.9692L16 12.1292V3.87237ZM2 10.8707V5.1276L8 2.23047L13.8702 5.06491L8 8V13.7508L2 10.8707Z" fill="${color}"/>`
      }
    },
    {
      name:  'shaded-product',
      sizes: {
        m: `
        <g clip-path="url(#clip0_1269_988)">
        <path d="M1 4.5L8 1L15 4.5V11.5L8 15L1 11.5V4.5Z" fill="#E7ECFF"/>
        <path d="M8 8.16289L15 4.4187V11.5001L8 15.0001V8.16289Z" fill="#A7BBFB"/>
        <path d="M8 8.16289L1 4.4187V11.5001L8 15.0001V8.16289Z" fill="#D3DDFD"/>
        <path d="M1 4.5L8 1L15 4.5V11.5L8 15L1 11.5V4.5Z" stroke="#2356F6" stroke-width="0.5"/>
        <path d="M0.999996 4.4187L8 8.16289L15 4.4187" stroke="#2356F6" stroke-width="0.5"/>
        <path d="M8 8.16284V14.9187" stroke="#2356F6" stroke-width="0.5"/>
        </g>
        <defs>
        <clipPath id="clip0_1269_988">
        <rect width="16" height="16" fill="white"/>
        </clipPath>
        </defs>
        `
      }
    },
    {
      name:  'canceled-asn',
      sizes: {
        l: `<path fill-rule="evenodd" clip-rule="evenodd" d="M11 7V2H13V7H11ZM4 15V3H2V22H22V3H20V15H4ZM14.9194 17H11.4807L9.08068 20H12.5194L14.9194 17ZM4 17H8.91943L6.51943 20H4V17ZM17.4807 17L15.0807 20H20V17H17.4807ZM9.20718 7.29285L7.79297 8.70706L12.0001 12.9142L16.2072 8.70706L14.793 7.29285L12.0001 10.0857L9.20718 7.29285Z" fill="${color}"/>`
      }
    },
    {
      name:  'canceled',
      sizes: {
        l: `<circle cx="12" cy="12" r="9" stroke="${color}" stroke-width="2"/><path d="M9 9L15 15" stroke="${color}" stroke-width="2"/><path d="M15 9L9 15" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'export',
      sizes: {
        m: `
        <path d="M5 11.1111L8 14L11 11.1111" stroke="${color}" stroke-width="2"/>
        <path d="M15 9.66666L15 0.99999L1 0.999988L1 9.66666" stroke="${color}" stroke-width="2"/>
        <path d="M8 14L8 5.81481" stroke="${color}" stroke-width="2"/>
        `
      }
    },
    {
      name:  'live',
      sizes: {
        m: `<path d="M4 13L12 8L4 3L4 13Z" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'details',
      sizes: {
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M1.55253 0H2.33331H14.3333H15.6141L15.3035 1.24254L14.3741 4.96003L16.282 10.6838L16.3741 10.96L16.3035 11.2425L15.3035 15.2425L15.1141 16H14.3333H2.33331H1.05253L1.36317 14.7575L2.29254 11.04L0.384627 5.31623L0.292542 5.03997L0.363168 4.75746L1.36317 0.757464L1.55253 0ZM3.11409 2L2.61409 4H12.5525L13.0525 2H3.11409ZM4.05407 10L2.72074 6H12.6126L13.9459 10H4.05407ZM4.11409 12L3.61409 14H13.5525L14.0525 12H4.11409Z" fill="${color}"/>`
      }
    },
    {
      name:  'dots-vertical',
      sizes: {
        m: `<path d="M9.5 13C9.5 13.8284 8.82843 14.5 8 14.5C7.17157 14.5 6.5 13.8284 6.5 13C6.5 12.1716 7.17157 11.5 8 11.5C8.82843 11.5 9.5 12.1716 9.5 13Z" fill="${color}"/><path d="M9.5 8C9.5 8.82843 8.82843 9.5 8 9.5C7.17157 9.5 6.5 8.82843 6.5 8C6.5 7.17157 7.17157 6.5 8 6.5C8.82843 6.5 9.5 7.17157 9.5 8Z" fill="${color}"/><path d="M9.5 3C9.5 3.82843 8.82843 4.5 8 4.5C7.17157 4.5 6.5 3.82843 6.5 3C6.5 2.17157 7.17157 1.5 8 1.5C8.82843 1.5 9.5 2.17157 9.5 3Z" fill="${color}"/>`
      }
    },
    {
      name:  'clone',
      sizes: {
        s: `<g clip-path="url(#clip0_1985_5568)"><path fill-rule="evenodd" clip-rule="evenodd" d="M10 0H8V2L10 2V4H12V2V0H10ZM6 4H8V6H12V8H8V12H0V4H4V0H6V4ZM2 6V10H6V6H2Z" fill="${color}"/></g><defs><clipPath id="clip0_1985_5568"><rect width="12" height="12" fill="${color}"/></clipPath></defs>`,
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M14 0H10V2L14 2V6H16V2V0H14ZM6 4H12V10H16V12H12V16H0V4H4V0H6V4ZM10 6V10V12V14H2V6H10Z" fill="${color}"/>`
      }
    },
    {
      name:  'attributes',
      sizes: {
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M7.55279 0.105573C7.83431 -0.0351909 8.16569 -0.0351909 8.44721 0.105573L10.1972 0.980573C10.6912 1.22756 10.8914 1.82824 10.6444 2.32221C10.3974 2.81619 9.79677 3.01642 9.30279 2.76943L8 2.11803L6.69721 2.76943C6.20324 3.01642 5.60256 2.81619 5.35557 2.32221C5.10858 1.82824 5.30881 1.22756 5.80279 0.980573L7.55279 0.105573ZM3.23416 4.5C3.70104 4.2421 3.88516 3.65926 3.64443 3.17779C3.39744 2.68381 2.79676 2.48358 2.30279 2.73057L0.572124 3.5959C0.499992 3.6301 0.432604 3.67268 0.371219 3.72238C0.148608 3.9026 0.00493073 4.17648 0.000126225 4.48407C4.21571e-05 4.48937 0 4.49468 0 4.5M1.90735e-06 4.50196V6.25C1.90735e-06 6.80228 0.447717 7.25 1 7.25C1.55229 7.25 2 6.80228 2 6.25V6.11804L2.30279 6.26943C2.79676 6.51642 3.39744 6.31619 3.64443 5.82221C3.88516 5.34074 3.70104 4.7579 3.23416 4.5M12.3556 3.17779C12.6026 2.68381 13.2032 2.48358 13.6972 2.73057L15.4278 3.59588C15.5 3.63009 15.5674 3.67267 15.6288 3.72238C15.6758 3.7604 15.719 3.80242 15.7581 3.8478C15.9088 4.02289 16 4.2508 16 4.5V6.25C16 6.80228 15.5523 7.25 15 7.25C14.4477 7.25 14 6.80228 14 6.25V6.11803L13.6972 6.26943C13.2032 6.51642 12.6026 6.31619 12.3556 5.82221C12.1148 5.34074 12.299 4.7579 12.7658 4.5C12.299 4.2421 12.1148 3.65926 12.3556 3.17779ZM5.35557 6.67779C5.60256 6.18381 6.20324 5.98358 6.69721 6.23057L8 6.88197L9.30279 6.23057C9.79677 5.98358 10.3974 6.18381 10.6444 6.67779C10.8914 7.17176 10.6912 7.77244 10.1972 8.01943L9 8.61803V9.75C9 10.3023 8.55229 10.75 8 10.75C7.44772 10.75 7 10.3023 7 9.75V8.61803L5.80279 8.01943C5.30881 7.77244 5.10858 7.17176 5.35557 6.67779ZM15 8.75C15.5523 8.75 16 9.19772 16 9.75V11.5C16 11.8788 15.786 12.225 15.4472 12.3944L13.6972 13.2694C13.2032 13.5164 12.6026 13.3162 12.3556 12.8222C12.1086 12.3282 12.3088 11.7276 12.8028 11.4806L14 10.882V9.75C14 9.19772 14.4477 8.75 15 8.75ZM8 12.25C8.55229 12.25 9 12.6977 9 13.25V13.382L9.30279 13.2306C9.79676 12.9836 10.3974 13.1838 10.6444 13.6778C10.8914 14.1718 10.6912 14.7724 10.1972 15.0194L8.45766 15.8892C8.42785 15.9045 8.39733 15.9183 8.36621 15.9305C8.12362 16.0259 7.84489 16.0266 7.59077 15.9124C7.57668 15.9061 7.5627 15.8995 7.54885 15.8925L5.80279 15.0194C5.30881 14.7724 5.10859 14.1718 5.35557 13.6778C5.60256 13.1838 6.20324 12.9836 6.69722 13.2306L7 13.382V13.25C7 12.6977 7.44772 12.25 8 12.25ZM1.90735e-06 9.75C1.90735e-06 9.19772 0.447717 8.75 1 8.75C1.55229 8.75 2 9.19772 2 9.75V10.882L3.19722 11.4806C3.69119 11.7276 3.89142 12.3282 3.64443 12.8222C3.39744 13.3162 2.79677 13.5164 2.30279 13.2694L0.552789 12.3944C0.214005 12.225 1.90735e-06 11.8788 1.90735e-06 11.5V9.75Z" fill="${color}"/>`
      }
    },
    {
      name:  'dollar',
      sizes: {
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16ZM7.70617 12.1046V13H8.41481V12.1052C8.9863 12.0782 9.48465 11.9832 9.90988 11.8203C10.4226 11.6224 10.8144 11.3451 11.0852 10.9883C11.3588 10.6289 11.4971 10.2044 11.5 9.71484C11.4971 9.38151 11.4294 9.08594 11.2969 8.82812C11.1673 8.57031 10.9844 8.34635 10.7481 8.15625C10.5119 7.96615 10.2325 7.80599 9.90988 7.67578C9.58724 7.54557 9.23292 7.44141 8.84691 7.36328L8.41481 7.26987V5.32351C8.70693 5.35776 8.9489 5.43872 9.14074 5.56641C9.40288 5.74089 9.55123 5.98568 9.5858 6.30078H11.379C11.3704 5.82422 11.2292 5.40495 10.9556 5.04297C10.6819 4.68099 10.2988 4.39844 9.80617 4.19531C9.39989 4.02679 8.9361 3.92818 8.41481 3.89947V3H7.70617V3.9029C7.21263 3.93549 6.76324 4.03296 6.35802 4.19531C5.85103 4.39844 5.45206 4.68099 5.16111 5.04297C4.87305 5.40495 4.73045 5.82813 4.73333 6.3125C4.73045 6.90365 4.94506 7.3737 5.37716 7.72266C5.80926 8.07162 6.39835 8.32813 7.14444 8.49219L7.70617 8.61971V10.6779C7.51551 10.6575 7.33835 10.619 7.17469 10.5625C6.92119 10.4714 6.71811 10.3372 6.56543 10.1602C6.41564 9.98047 6.33066 9.75651 6.31049 9.48828H4.5C4.5144 10.0638 4.66852 10.5469 4.96235 10.9375C5.25905 11.3255 5.67099 11.6185 6.19815 11.8164C6.63749 11.9805 7.14016 12.0765 7.70617 12.1046ZM8.41481 10.6739C8.58042 10.6537 8.73166 10.6192 8.86852 10.5703C9.10473 10.487 9.28765 10.3711 9.41728 10.2227C9.54691 10.0742 9.61173 9.90365 9.61173 9.71094C9.61173 9.53125 9.55268 9.38021 9.43457 9.25781C9.31934 9.13542 9.14938 9.03125 8.92469 8.94531C8.77706 8.88812 8.6071 8.83438 8.41481 8.78411V10.6739ZM7.70617 7.10975V5.32632C7.55532 5.34547 7.41993 5.37732 7.3 5.42188C7.08971 5.4974 6.92839 5.60287 6.81605 5.73828C6.70658 5.8737 6.65185 6.02734 6.65185 6.19922C6.64609 6.34245 6.67922 6.46745 6.75123 6.57422C6.82613 6.68099 6.92839 6.77344 7.05802 6.85156C7.18765 6.92708 7.33745 6.99349 7.50741 7.05078C7.572 7.07157 7.63825 7.09122 7.70617 7.10975Z" fill="${color}"/>`
      }
    },
    {
      name:  'supplier',
      sizes: {
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M8 0.270996L16 5.98528V15.9999H14V7.01452L8 2.7288L2 7.01452V15.9999H0V5.98528L8 0.270996ZM12 7.99988V15.9999H4V7.99988H12ZM6 9.99988V13.9999H10V9.99988H9V10.9999H7V9.99988H6Z" fill="${color}"/>`
      }
    },
    {
      name:  'bulk',
      sizes: {
        m: `<path d="M4.5 13C4.5 13.8284 3.82843 14.5 3 14.5C2.17157 14.5 1.5 13.8284 1.5 13C1.5 12.1716 2.17157 11.5 3 11.5C3.82843 11.5 4.5 12.1716 4.5 13Z" fill="${color}"/><path d="M4.5 8C4.5 8.82843 3.82843 9.5 3 9.5C2.17157 9.5 1.5 8.82843 1.5 8C1.5 7.17157 2.17157 6.5 3 6.5C3.82843 6.5 4.5 7.17157 4.5 8Z" fill="${color}"/><path d="M4.5 3C4.5 3.82843 3.82843 4.5 3 4.5C2.17157 4.5 1.5 3.82843 1.5 3C1.5 2.17157 2.17157 1.5 3 1.5C3.82843 1.5 4.5 2.17157 4.5 3Z" fill="${color}"/><path d="M9.5 13C9.5 13.8284 8.82843 14.5 8 14.5C7.17157 14.5 6.5 13.8284 6.5 13C6.5 12.1716 7.17157 11.5 8 11.5C8.82843 11.5 9.5 12.1716 9.5 13Z" fill="${color}"/><path d="M9.5 8C9.5 8.82843 8.82843 9.5 8 9.5C7.17157 9.5 6.5 8.82843 6.5 8C6.5 7.17157 7.17157 6.5 8 6.5C8.82843 6.5 9.5 7.17157 9.5 8Z" fill="${color}"/><path d="M9.5 3C9.5 3.82843 8.82843 4.5 8 4.5C7.17157 4.5 6.5 3.82843 6.5 3C6.5 2.17157 7.17157 1.5 8 1.5C8.82843 1.5 9.5 2.17157 9.5 3Z" fill="${color}"/><path d="M14.5 13C14.5 13.8284 13.8284 14.5 13 14.5C12.1716 14.5 11.5 13.8284 11.5 13C11.5 12.1716 12.1716 11.5 13 11.5C13.8284 11.5 14.5 12.1716 14.5 13Z" fill="${color}"/><path d="M14.5 8C14.5 8.82843 13.8284 9.5 13 9.5C12.1716 9.5 11.5 8.82843 11.5 8C11.5 7.17157 12.1716 6.5 13 6.5C13.8284 6.5 14.5 7.17157 14.5 8Z" fill="${color}"/><path d="M14.5 3C14.5 3.82843 13.8284 4.5 13 4.5C12.1716 4.5 11.5 3.82843 11.5 3C11.5 2.17157 12.1716 1.5 13 1.5C13.8284 1.5 14.5 2.17157 14.5 3Z" fill="${color}"/>`
      }
    },
    {
      name:  'issue-circle',
      sizes: {
        s: `<path fill-rule="evenodd" clip-rule="evenodd" d="M6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12ZM5.09951 2.99504C5.04623 2.46228 5.46459 2 6 2C6.53541 2 6.95377 2.46228 6.9005 2.99504L6.54975 6.50248C6.52151 6.78492 6.28385 7 6 7C5.71616 7 5.47849 6.78492 5.45025 6.50248L5.09951 2.99504ZM5.99997 8C6.55225 8 6.99997 8.44772 6.99997 9C6.99997 9.55228 6.55225 10 5.99997 10C5.44768 10 4.99997 9.55228 4.99997 9C4.99997 8.44772 5.44768 8 5.99997 8Z" fill="${color}"/>`,
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16ZM6.32877 4.97279C6.15665 3.94009 6.95302 3 7.99997 3C9.04692 3 9.84329 3.94009 9.67117 4.97279L9.16437 8.01361C9.0695 8.58281 8.57703 9 7.99997 9C7.42292 9 6.93044 8.58281 6.83557 8.01361L6.32877 4.97279ZM7.99997 11C8.55226 11 8.99997 11.4477 8.99997 12C8.99997 12.5523 8.55226 13 7.99997 13C7.44769 13 6.99997 12.5523 6.99997 12C6.99997 11.4477 7.44769 11 7.99997 11Z" fill="${color}"/>`
      }
    },
    {
      name:  'reset',
      sizes: {
        m: `<path d="M3 8.5C3 11.5376 5.46243 14 8.5 14C11.5376 14 14 11.5376 14 8.5C14 5.46243 11.5376 3 8.5 3H3.5" stroke="${color}" stroke-width="2"/><path d="M5.79773 0.702393L3.5 3.00012L5.79773 5.29784" stroke="${color}" stroke-width="2"/>`,
        s: `<path d="M2 7C2 9.20914 3.79086 11 6 11C8.20914 11 10 9.20914 10 7C10 4.79086 8.20914 3 6 3H2.36364" stroke="${color}" stroke-width="2"/><path d="M4.78383 0.716064L2.5 2.99989L4.76194 5.26183" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'go',
      sizes: {
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M9.79289 11.2928L11.2071 12.7071L15.9142 7.99995L11.2071 3.29285L9.79289 4.70706L12.0858 6.99995H0V8.99995H12.0858L9.79289 11.2928Z" fill="${color}"/>`
      }
    },
    {
      name:  'create-bulk-asns',
      sizes: {
        m: `
        <path d="M13 3V1H5V8V15H13V13" stroke="${color}" stroke-width="2"/>
        <path d="M2 3H1V8V13H2" stroke="${color}" stroke-width="2"/>
        <path d="M13 5V11" stroke="${color}" stroke-width="2"/>
        <path d="M16 8L10 8" stroke="${color}" stroke-width="2"/>
        `
      }
    },
    {
      name:  'create-bulk-order',
      sizes: {
        m: `
        <path d="M13 8.625V11H5V3H7.375" stroke="${color}" stroke-width="2"/>
        <path d="M1 5V15H11" stroke="${color}" stroke-width="2"/>
        <path d="M13 0V6" stroke="${color}" stroke-width="2"/>
        <path d="M16 3L10 3" stroke="${color}" stroke-width="2"/>
        `
      }
    },
    {
      name:  'products-group',
      sizes: {
        m: `<path d="M1 1H7V9H5V15H1V1Z" stroke="${color}" stroke-width="2"/><path d="M7 4H15V15H11V9H7L7 4Z" stroke="${color}" stroke-width="2"/><path d="M5 9H11V15H5V9Z" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'external-link',
      sizes: {
        m: `<g clip-path="url(#clip0_1265_27)"><path d="M14 9V14H2V2H7" stroke="${color}" stroke-width="2"/><path d="M15 1L6 10" stroke="${color}" stroke-width="2"/><path d="M9 1H15V7" stroke="${color}" stroke-width="2"/></g><defs><clipPath id="clip0_1265_27"><rect width="16" height="16" fill="white"/></clipPath></defs>`
      }
    },
    {
      name:  'eye-open',
      sizes: {
        m: `
        <path d="M8 4C4.4 4 1.83333 6.66667 1 8C1.83333 9.33333 4.4 12 8 12C11.6 12 14.1667 9.33333 15 8C14.1667 6.66667 11.6 4 8 4Z" stroke="${color}" stroke-width="2" stroke-linejoin="round"/>
        <circle cx="8" cy="8" r="1.5" fill="${color}" stroke="${color}"/>
        `
      }
    },
    {
      name:  'order-data',
      sizes: {
        m: `<path d="M1 9V15H15V9.30769V9" stroke="${color}" stroke-width="2"/><path d="M4 8L7 5L9 7L13 3" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'customs',
      sizes: {
        m: `<path d="M15 5V1H1V15H5" stroke="${color}" stroke-width="2"/><circle cx="10" cy="10" r="4" stroke="${color}" stroke-width="2"/><path d="M13 13L15.28 15.28" stroke="${color}" stroke-width="2"/><rect x="5" y="1" width="6" height="3" fill="${color}"/>`
      }
    },
    {
      name:  'send',
      sizes: {
        m: `
        <g clip-path="url(#clip0_1166_246)">
        <path d="M15 1L1 6L7 9L10 15L15 1Z" stroke="${color}" stroke-width="2" stroke-linejoin="round"/>
        <path d="M14 2L7 9" stroke="${color}" stroke-width="2"/>
        </g>
        <defs>
        <clipPath id="clip0_1166_246">
        <rect width="16" height="16" fill="${color}"/>
        </clipPath>
        </defs>`
      }
    },
    {
      name:  'exclamation-mark',
      sizes: {
        s: `<path fill-rule="evenodd" clip-rule="evenodd" d="M5.99999 0C4.46279 0 3.32301 1.42668 3.66246 2.92593L4.53497 6.77965C4.68994 7.46411 5.29819 7.95004 5.99997 7.95004C6.70175 7.95004 7.30999 7.46411 7.46497 6.77966L8.33751 2.92595C8.67697 1.42669 7.5372 0 5.99999 0ZM7.19997 10.7999C7.19997 10.1372 6.66271 9.59993 5.99997 9.59993C5.33722 9.59993 4.79996 10.1372 4.79996 10.7999C4.79996 11.4627 5.33722 11.9999 5.99997 11.9999C6.66271 11.9999 7.19997 11.4627 7.19997 10.7999Z" fill="${color}"/>`
      }
    },
    {
      name:  'profile',
      sizes: {
        l: `
        <g clip-path="url(#clip0_1166_302)">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 14.541 21.0523 16.8607 19.491 18.6248C17.4384 16.9828 14.8331 16 12 16C9.16689 16 6.56162 16.9828 4.50903 18.6248C2.94775 16.8607 2 14.541 2 12ZM12 18C14.2523 18 16.3293 18.7436 18.001 19.9999C16.3295 21.2558 14.2517 22 12 22C9.74832 22 7.67046 21.2558 5.99896 20C7.67075 18.7436 9.74776 18 12 18ZM12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0ZM9 10C9 8.34315 10.3431 7 12 7C13.6569 7 15 8.34315 15 10C15 11.6569 13.6569 13 12 13C10.3431 13 9 11.6569 9 10ZM12 5C9.23858 5 7 7.23858 7 10C7 12.7614 9.23858 15 12 15C14.7614 15 17 12.7614 17 10C17 7.23858 14.7614 5 12 5Z" fill="${color}"/>
        </g>
        <defs>
        <clipPath id="clip0_1166_302">
        <rect width="24" height="24" fill="${color}"/>
        </clipPath>
        </defs>
        `
      }
    },
    {
      name:  'no-more-records',
      sizes: {
        m: `
        <path d="M1 5V8C1 9.10457 1.89543 10 3 10V10C4.10457 10 5 9.10457 5 8V4C5 2.34315 6.34315 1 8 1V1C9.65685 1 11 2.34315 11 4V11.5" stroke="${color}" stroke-width="2"/>
        <path d="M15 15H7" stroke="${color}" stroke-width="2"/>
        <path d="M8 9L11 12L14 9" stroke="${color}" stroke-width="2"/>
        `
      }
    },
    {
      name:  'draft-order',
      sizes: {
        s: `<path d="M4 11H1V1L9 1L11 3V4" stroke="${color}" stroke-width="2"/><rect x="10" y="6" width="2" height="2" fill="${color}"/><rect x="10" y="10" width="2" height="2" fill="${color}"/><rect x="6" y="10" width="2" height="2" fill="${color}"/>`,
        m: `<path d="M7.5 15H1V1L11.2 1L15 5V8" stroke="${color}" stroke-width="2"/><rect x="14" y="14" width="2" height="2" fill="${color}"/><rect x="14" y="10" width="2" height="2" fill="${color}"/><rect x="10" y="14" width="2" height="2" fill="${color}"/>`
      }
    },
    {
      name:  'resolve',
      sizes: {
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M3.88188 0.549082C4.68414 0.195694 5.57054 0 6.5 0C10.0899 0 13 2.91015 13 6.5C13 7.19879 12.8894 7.87322 12.6843 8.50584L15.1696 11.0603C16.3138 12.2363 16.3009 14.1132 15.1407 15.2734C13.9578 16.4562 12.036 16.4431 10.8694 15.244L8.40886 12.7149C7.80442 12.9004 7.16315 13 6.5 13C2.91015 13 0 10.0899 0 6.5C0 5.57299 0.194662 4.68881 0.546293 3.88822L1.13456 2.54885L4.58578 6L6 6L6 4.61198L2.55011 1.13571L3.88188 0.549082ZM6.23324 2.00775L8 3.78802V8H3.75737L2.00683 6.2495C2.0023 6.33238 2 6.4159 2 6.5C2 8.98528 4.01472 11 6.5 11C7.13366 11 7.73427 10.8696 8.27851 10.6352L8.91081 10.3628L12.3029 13.8494C12.6917 14.249 13.3323 14.2534 13.7265 13.8591C14.1132 13.4725 14.1175 12.8469 13.7362 12.455L10.3322 8.95627L10.6096 8.33677C10.8601 7.77718 11 7.15626 11 6.5C11 4.01472 8.98528 2 6.5 2C6.4104 2 6.32146 2.00261 6.23324 2.00775Z" fill="${color}"/>`
      }
    },
    {
      name:  'locked',
      sizes: {
        s: `<path d="M3 4V1H9V4" stroke="${color}" stroke-width="2"/><path d="M1 5V11H11V5H1Z" stroke="${color}" stroke-width="2"/>`
      }
    },
    {
      name:  'warehouse',
      sizes: {
        s: `<path d="M1 12V1H11V12" stroke="${color}" stroke-width="2"/><path d="M1 12V7H11V12" stroke="${color}" stroke-width="2"/><rect x="4" y="10" width="4" height="2" fill="${color}"/>`
      }
    },
    {
      name:  'warning-sign',
      sizes: {
        s: `<path fill-rule="evenodd" clip-rule="evenodd" d="M0 10.1797C0 11.185 0.814971 12 1.82029 12H10.1797C11.185 12 12 11.185 12 10.1797C12 9.89712 11.9342 9.61841 11.8078 9.36565L7.62812 1.00623C7.31977 0.389547 6.68947 0 6 0C5.31053 0 4.68023 0.389546 4.37188 1.00623L0.192173 9.36565C0.0657947 9.61841 0 9.89712 0 10.1797ZM5.09951 3.99504C5.04623 3.46228 5.46459 3 6 3C6.53541 3 6.95377 3.46228 6.9005 3.99504L6.54975 7.50248C6.52151 7.78492 6.28385 8 6 8C5.71616 8 5.47849 7.78492 5.45025 7.50248L5.09951 3.99504ZM5.99997 9C6.55225 9 6.99997 9.44772 6.99997 10C6.99997 10.5523 6.55225 11 5.99997 11C5.44768 11 4.99997 10.5523 4.99997 10C4.99997 9.44772 5.44768 9 5.99997 9Z" fill="${color}"/>`,
        m: `<path fill-rule="evenodd" clip-rule="evenodd" d="M0 13.5729C0 14.9134 1.08663 16 2.42705 16H13.5729C14.9134 16 16 14.9134 16 13.5729C16 13.1962 15.9123 12.8245 15.7438 12.4875L10.1708 1.34164C9.7597 0.519394 8.9193 0 8 0C7.0807 0 6.2403 0.519395 5.82918 1.34164L0.25623 12.4875C0.0877259 12.8245 0 13.1962 0 13.5729ZM6.32877 5.97279C6.15665 4.94009 6.95302 4 7.99997 4C9.04692 4 9.84329 4.94009 9.67117 5.97279L9.16437 9.01361C9.0695 9.58281 8.57703 10 7.99997 10C7.42292 10 6.93044 9.58281 6.83557 9.01361L6.32877 5.97279ZM7.99997 12C8.55226 12 8.99997 12.4477 8.99997 13C8.99997 13.5523 8.55226 14 7.99997 14C7.44769 14 6.99997 13.5523 6.99997 13C6.99997 12.4477 7.44769 12 7.99997 12Z" fill="${color}"/>`
      }
    },
    {
      name:  'reset-columns',
      sizes: {
        m: `<g clip-path="url(#clip0_4519_19187)"><path d="M1 1L1 15L15 15L15 1L1 1Z" stroke="${color}" stroke-width="2"/><path d="M11.5 5L8.5 8L11.5 11" stroke="${color}" stroke-width="2"/><rect x="4" y="1" width="2" height="14" fill="${color}"/></g><defs><clipPath id="clip0_4519_19187"><rect width="16" height="16" fill="${color}"/></clipPath></defs>`
      }
    },
    {
      name:  'draft-ready-to-import',
      sizes: {
        l: `<path fill-rule="evenodd" clip-rule="evenodd" d="M2 2H22V10.7143H20V4H15V6H9V4H4V20H10.7143V22H2V2ZM16.4888 22.5697L21.8321 14.5547L20.168 13.4453L16.1779 19.4304L13.7071 16.9596L12.2929 18.3738L16.4888 22.5697Z" fill="${color}"/>`
      }
    },
    {
      name:  'draft-pending-import',
      sizes: {
        l: `<path fill-rule="evenodd" clip-rule="evenodd" d="M2 2H22V9.42857H20V4H15V6H9V4H4V20H9.42857V22H2V2ZM17 12C16.5618 12 16.1352 12.0566 15.728 12.1635L16.2357 14.098C16.4786 14.0342 16.7346 14 17 14C17.2654 14 17.5214 14.0342 17.7643 14.098L18.272 12.1635C17.8648 12.0566 17.4382 12 17 12ZM21.3189 14.4794C20.8844 13.7361 20.2639 13.1156 19.5206 12.6811L18.5112 14.4077C18.9577 14.6687 19.3313 15.0423 19.5923 15.4888L21.3189 14.4794ZM14.4794 12.6811C13.7361 13.1156 13.1156 13.7361 12.6811 14.4794L14.4077 15.4888C14.6687 15.0423 15.0423 14.6687 15.4888 14.4077L14.4794 12.6811ZM22 17C22 16.5618 21.9434 16.1352 21.8365 15.728L19.902 16.2357C19.9658 16.4786 20 16.7346 20 17C20 17.2654 19.9658 17.5214 19.902 17.7643L21.8365 18.272C21.9434 17.8648 22 17.4382 22 17ZM12.1635 15.728C12.0566 16.1352 12 16.5618 12 17C12 17.4382 12.0566 17.8648 12.1635 18.272L14.098 17.7643C14.0342 17.5214 14 17.2654 14 17C14 16.7346 14.0342 16.4786 14.098 16.2357L12.1635 15.728ZM19.5206 21.3189C20.2639 20.8844 20.8844 20.2639 21.3189 19.5206L19.5923 18.5112C19.3313 18.9577 18.9577 19.3313 18.5112 19.5923L19.5206 21.3189ZM12.6811 19.5206C13.1156 20.2639 13.7361 20.8844 14.4794 21.3189L15.4888 19.5923C15.0423 19.3313 14.6687 18.9577 14.4077 18.5112L12.6811 19.5206ZM15.728 21.8365C16.1352 21.9434 16.5618 22 17 22C17.4382 22 17.8648 21.9434 18.272 21.8365L17.7643 19.902C17.5214 19.9658 17.2654 20 17 20C16.7346 20 16.4786 19.9658 16.2357 19.902L15.728 21.8365Z" fill="${color}"/>`
      }
    },
    {
      name:  'draft-failed-import',
      sizes: {
        l: `<path fill-rule="evenodd" clip-rule="evenodd" d="M22 2H2V22H10.7143V20H4V4H9V6H15V4H20V10.7143H22V2ZM18.9142 17.5001L21.7071 20.293L20.2929 21.7072L17.5 18.9143L14.7071 21.7072L13.2929 20.293L16.0858 17.5001L13.2929 14.7072L14.7071 13.293L17.5 16.0858L20.2929 13.293L21.7071 14.7072L18.9142 17.5001Z" fill="${color}"/>`
      }
    },
    {
      name:  'draft-validation-errors',
      sizes: {
        l: `<path fill-rule="evenodd" clip-rule="evenodd" d="M22 2H2V7H4V4H9V6H15V4H20V9H22V2ZM14.5 12.25C13.2574 12.25 12.25 13.2574 12.25 14.5C12.25 15.7426 13.2574 16.75 14.5 16.75C15.7426 16.75 16.75 15.7426 16.75 14.5C16.75 13.2574 15.7426 12.25 14.5 12.25ZM10 14.5C10 12.0147 12.0147 10 14.5 10C16.9853 10 19 12.0147 19 14.5C19 15.3806 18.7471 16.202 18.3099 16.8958L21.7071 20.293L20.2929 21.7072L16.8957 18.31C16.202 18.7471 15.3805 19 14.5 19C12.0147 19 10 16.9853 10 14.5ZM2 10H4V12H2V10ZM9 20H7V22H9V20ZM2 20H4V22H2V20ZM4 15H2V17H4V15Z" fill="${color}"/>`
      }
    },
    {
      name:  'owd-logo',
      sizes: {
        m: {
          light: '<g clip-path="url(#clip0_1169_396)"><path d="M8.02388 0C10.197 0 12.0836 0.779912 13.6358 2.33973C15.2119 3.89956 16 5.79025 16 8.03545C16 10.257 15.2358 12.1477 13.6836 13.6839C12.1313 15.2201 10.2687 16 8.04776 16C5.73134 16 3.8209 15.2201 2.29254 13.6366C0.764179 12.0532 0 10.1625 0 7.98818C0 6.5229 0.358209 5.19941 1.07463 3.97046C1.79104 2.74151 2.77015 1.77253 4.01194 1.06352C5.25373 0.354505 6.59104 0 8.02388 0ZM8 3.21418C6.68657 3.21418 5.56418 3.66322 4.6806 4.58493C3.77313 5.48301 3.34328 6.64106 3.34328 8.03545C3.34328 9.59527 3.91642 10.8242 5.03881 11.7459C5.92239 12.4549 6.92537 12.8095 8.07164 12.8095C9.36119 12.8095 10.4597 12.3604 11.3672 11.4387C12.2746 10.517 12.7284 9.38257 12.7284 8.03545C12.7284 6.68833 12.2746 5.55391 11.3672 4.6322C10.4119 3.68685 9.31343 3.21418 8 3.21418Z" fill="#39383A"/><path d="M7.90756 8.4359L10 12L9.62185 4L4 9.58974L7.90756 8.4359Z" fill="#FF5B24"/></g><defs><clipPath id="clip0_1169_396"><rect width="16" height="16" fill="white"/></clipPath></defs>',
          dark:  '<g clip-path="url(#clip0_1169_397)"><path d="M8.02388 0C10.197 0 12.0836 0.779912 13.6358 2.33973C15.2119 3.89956 16 5.79025 16 8.03545C16 10.257 15.2358 12.1477 13.6836 13.6839C12.1313 15.2201 10.2687 16 8.04776 16C5.73134 16 3.8209 15.2201 2.29254 13.6366C0.764179 12.0532 0 10.1625 0 7.98818C0 6.5229 0.358209 5.19941 1.07463 3.97046C1.79104 2.74151 2.77015 1.77253 4.01194 1.06352C5.25373 0.354505 6.59104 0 8.02388 0ZM8 3.21418C6.68657 3.21418 5.56418 3.66322 4.6806 4.58493C3.77313 5.48301 3.34328 6.64106 3.34328 8.03545C3.34328 9.59527 3.91642 10.8242 5.03881 11.7459C5.92239 12.4549 6.92537 12.8095 8.07164 12.8095C9.36119 12.8095 10.4597 12.3604 11.3672 11.4387C12.2746 10.517 12.7284 9.38257 12.7284 8.03545C12.7284 6.68833 12.2746 5.55391 11.3672 4.6322C10.4119 3.68685 9.31343 3.21418 8 3.21418Z" fill="white"/><path d="M7.90756 8.4359L10 12L9.62185 4L4 9.58974L7.90756 8.4359Z" fill="#FF5B24"/></g><defs><clipPath id="clip0_1169_397"><rect width="16" height="16" fill="white"/></clipPath></defs>'
        }
      }
    },
    {
      name:  'activities',
      sizes: {
        m: `<svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0 8H4L6 4L10 12L12 8H16" stroke="${color}" stroke-width="2"/></svg>`
      }
    },
    {
      name:  'client-panel-open',
      sizes: {
        s: `<g clip-path="url(#clip0_14814_8850)"><rect x="0.5" y="0.5" width="11" height="11" stroke="${color}"/><rect x="8.5" y="0.5" width="3" height="11" fill="${color}" stroke="${color}"/><path d="M1 6H6" stroke="${color}"/><path d="M4 4L6 6L4 8" stroke="${color}"/></g><defs><clipPath id="clip0_14814_8850"><rect width="12" height="12" fill="white"/></clipPath></defs>`
      }
    },
    {
      name:  'clients',
      sizes: {
        s: `<g clip-path="url(#clip0_14814_8856)"><circle cx="6" cy="6" r="1.5" stroke="${color}"/><circle cx="3" cy="2" r="1.5" stroke="${color}"/><circle cx="9" cy="2" r="1.5" stroke="${color}"/><path d="M3 12V11C3 9.89543 3.89543 9 5 9H7C8.10457 9 9 9.89543 9 11V12" stroke="${color}"/><path d="M9 5H9.5C10.3284 5 11 5.67157 11 6.5V8" stroke="${color}"/><path d="M3 5H2.5C1.67157 5 1 5.67157 1 6.5V8" stroke="${color}"/></g><defs><clipPath id="clip0_14814_8856"><rect width="12" height="12" fill="white"/></clipPath></defs>`
      }
    },
    {
      name:  'circle-dash',
      sizes: {
        m: `<rect width="16" height="16" fill="white" fill-opacity="0.01" style="mix-blend-mode:multiply"/><path d="M3.84997 2.35C3.27647 2.79009 2.77103 3.31238 2.34997 3.9L3.14997 4.5C3.5172 3.99072 3.9549 3.53618 4.44997 3.15L3.84997 2.35Z" fill="${color}"/><path d="M2.29997 6.15L1.34997 5.85C1.10845 6.54068 0.98999 7.26838 0.999972 8H1.99997C1.99794 7.37106 2.09929 6.74607 2.29997 6.15Z" fill="${color}"/><path d="M1.34997 10.2C1.58185 10.8972 1.91909 11.5548 2.34997 12.15L3.14997 11.55C2.78851 11.0439 2.50221 10.4881 2.29997 9.9L1.34997 10.2Z" fill="${color}"/><path d="M3.89997 13.65C4.49514 14.0809 5.15275 14.4181 5.84997 14.65L6.14997 13.7C5.56184 13.4978 5.00608 13.2115 4.49997 12.85L3.89997 13.65Z" fill="${color}"/><path d="M5.84997 1.35L6.14997 2.3C6.74604 2.09932 7.37103 1.99797 7.99997 2V1C7.26835 0.990021 6.54065 1.10848 5.84997 1.35Z" fill="${color}"/><path d="M12.1 13.65C12.6889 13.2111 13.211 12.6889 13.65 12.1L12.85 11.5C12.4781 12.0219 12.0219 12.4782 11.5 12.85L12.1 13.65Z" fill="${color}"/><path d="M13.7 9.85L14.65 10.15C14.8675 9.45343 14.9853 8.72959 15 8H14C14.002 8.62894 13.9007 9.25394 13.7 9.85Z" fill="${color}"/><path d="M14.6 5.8C14.3681 5.10279 14.0309 4.44517 13.6 3.85L12.8 4.45C13.1614 4.95611 13.4477 5.51187 13.65 6.1L14.6 5.8Z" fill="${color}"/><path d="M12.05 2.3C11.4548 1.86912 10.7972 1.53188 10.1 1.3L9.79997 2.25C10.3881 2.45224 10.9439 2.73854 11.45 3.1L12.05 2.3Z" fill="${color}"/><path d="M10.15 14.65L9.84997 13.7C9.25391 13.9007 8.62891 14.002 7.99997 14V15C8.72667 14.9567 9.44706 14.8394 10.15 14.65Z" fill="${color}"/>`
      }
    },
    {
      name:  'incomplete-normal',
      sizes: {
        m: `<rect width="16" height="16" fill="${color}" fill-opacity="0.01" style="mix-blend-mode:multiply"/><path d="M11.8821 3.42965L12.5246 2.6639C11.904 2.13853 11.1963 1.72553 10.4336 1.4435L10.0918 2.3823C10.745 2.62459 11.3508 2.97902 11.8821 3.42965Z" fill="${color}"/><path d="M13.905 7L14.8889 6.7936C14.7506 5.99468 14.4734 5.22616 14.07 4.52285L13.2044 5C13.5499 5.622 13.7868 6.29836 13.905 7Z" fill="${color}"/><path d="M10.0918 13.6177L10.4336 14.5565C11.1963 14.2745 11.904 13.8615 12.5246 13.3361L11.8821 12.5703C11.3508 13.021 10.745 13.3754 10.0918 13.6177Z" fill="${color}"/><path d="M13.2044 11L14.07 11.5C14.4737 10.7886 14.7508 10.0126 14.8891 9.2064L13.905 9.03295C13.7867 9.72415 13.5497 10.3897 13.2044 11Z" fill="${color}"/><path d="M8 15V1C6.14348 1 4.36301 1.7375 3.05025 3.05025C1.7375 4.36301 1 6.14348 1 8C1 9.85652 1.7375 11.637 3.05025 12.9497C4.36301 14.2625 6.14348 15 8 15Z" fill="${color}"/>`
      }
    },
    {
      name:  'checkmark-outline',
      sizes: {
        m: `<rect width="16" height="16" fill="${color}" fill-opacity="0.01" style="mix-blend-mode:multiply"/><path d="M7 10.707L4.5 8.2065L5.2065 7.5L7 9.293L10.7925 5.5L11.5 6.2075L7 10.707Z" fill="${color}"/><path d="M8 1C6.61553 1 5.26216 1.41054 4.11101 2.17971C2.95987 2.94888 2.06266 4.04213 1.53285 5.32122C1.00303 6.6003 0.86441 8.00776 1.13451 9.36563C1.4046 10.7235 2.07129 11.9708 3.05026 12.9497C4.02922 13.9287 5.2765 14.5954 6.63437 14.8655C7.99224 15.1356 9.3997 14.997 10.6788 14.4672C11.9579 13.9373 13.0511 13.0401 13.8203 11.889C14.5895 10.7378 15 9.38447 15 8C15 6.14348 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85652 1 8 1ZM8 14C6.81332 14 5.65328 13.6481 4.66658 12.9888C3.67989 12.3295 2.91085 11.3925 2.45673 10.2961C2.0026 9.19974 1.88378 7.99334 2.11529 6.82946C2.3468 5.66557 2.91825 4.59647 3.75736 3.75736C4.59648 2.91824 5.66558 2.3468 6.82946 2.11529C7.99335 1.88378 9.19975 2.0026 10.2961 2.45672C11.3925 2.91085 12.3295 3.67988 12.9888 4.66658C13.6481 5.65327 14 6.81331 14 8C14 9.5913 13.3679 11.1174 12.2426 12.2426C11.1174 13.3679 9.5913 14 8 14Z" fill="${color}"/>`
      }
    },
    {
      name:  'warning-outline',
      sizes: {
        m: `<path d="M8 1C6.61553 1 5.26216 1.41054 4.11101 2.17971C2.95987 2.94888 2.06266 4.04213 1.53285 5.32122C1.00303 6.6003 0.86441 8.00776 1.13451 9.36563C1.4046 10.7235 2.07129 11.9708 3.05026 12.9497C4.02922 13.9287 5.2765 14.5954 6.63437 14.8655C7.99224 15.1356 9.3997 14.997 10.6788 14.4672C11.9579 13.9373 13.0511 13.0401 13.8203 11.889C14.5895 10.7378 15 9.38447 15 8C15 6.14348 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85652 1 8 1ZM8 14C6.81332 14 5.65328 13.6481 4.66658 12.9888C3.67989 12.3295 2.91085 11.3925 2.45673 10.2961C2.0026 9.19974 1.88378 7.99334 2.11529 6.82946C2.3468 5.66557 2.91825 4.59647 3.75736 3.75736C4.59648 2.91824 5.66558 2.3468 6.82946 2.11529C7.99335 1.88378 9.19975 2.0026 10.2961 2.45672C11.3925 2.91085 12.3295 3.67988 12.9888 4.66658C13.6481 5.65327 14 6.81331 14 8C14 9.5913 13.3679 11.1174 12.2426 12.2426C11.1174 13.3679 9.5913 14 8 14Z" fill="${color}"/><path d="M8.5 4H7.5V9.5H8.5V4Z" fill="${color}"/><path d="M8 11C7.85167 11 7.70666 11.044 7.58333 11.1264C7.45999 11.2088 7.36386 11.3259 7.30709 11.463C7.25033 11.6 7.23548 11.7508 7.26441 11.8963C7.29335 12.0418 7.36478 12.1754 7.46967 12.2803C7.57456 12.3852 7.7082 12.4566 7.85369 12.4856C7.99917 12.5145 8.14997 12.4997 8.28701 12.4429C8.42406 12.3861 8.54119 12.29 8.6236 12.1667C8.70602 12.0433 8.75 11.8983 8.75 11.75C8.75 11.5511 8.67098 11.3603 8.53033 11.2197C8.38968 11.079 8.19892 11 8 11Z" fill="${color}"/>`
      }
    },
    {
      name:  'on-hold',
      sizes: {
        l: `
            <path d="M21.5 11V3L16.6143 3L3.5 3V21H12.5" stroke="${color}" stroke-width="2"/>
            <rect x="15.5" y="14" width="2" height="7" fill="${color}"/>
            <rect x="19.5" y="14" width="2" height="7" fill="${color}"/>
            <rect x="6.5" y="6" width="2" height="2" fill="${color}"/>
            <rect x="6.5" y="10" width="2" height="2" fill="${color}"/>
            <rect x="6.5" y="14" width="2" height="2" fill="${color}"/>
            <rect x="10.5" y="6" width="2" height="2" fill="${color}"/>
            <rect x="10.5" y="10" width="2" height="2" fill="${color}"/>
            <rect x="14.5" y="6" width="2" height="2" fill="${color}"/>
          `
      }
    },
    {
      name:  'backorder',
      sizes: {
        l: `
          <path d="M21.5 11V3L16.6143 3L3.5 3V21H11.5" stroke="${color}" stroke-width="2"/>
          <rect x="15.5" y="15" width="6" height="6" stroke="${color}" stroke-width="2"/>
          <path d="M15.5 15L9 8.5" stroke="${color}" stroke-width="2"/>
          <path d="M8.5 13V8H13.5" stroke="${color}" stroke-width="2"/>
          `
      }
    },
    {
      name:  'at-warehouse-widget',
      sizes: {
        l: `
          <path d="M21.5 11V3L16.6143 3L3.5 3V21H9.5" stroke="${color}" stroke-width="2"/>
          <path d="M13.5 22V17L17.5 14L21.5 17V22" stroke="${color}" stroke-width="2"/>
          <path d="M17.5 22V19" stroke="${color}" stroke-width="2"/>
          <path d="M6.5 6H15.5V8H6.5V6Z" fill="${color}"/>
          <path d="M6.5 10H10.5V12H6.5V10Z" fill="${color}"/>
          `
      }
    },
    {
      name:  'void',
      sizes: {
        l: `
          <path fill-rule="evenodd" clip-rule="evenodd" d="M17.5 22C19.9853 22 22 19.9853 22 17.5C22 15.0147 19.9853 13 17.5 13C15.0147 13 13 15.0147 13 17.5C13 19.9853 15.0147 22 17.5 22ZM20 16.5H15V18.5H20V16.5Z" fill="${color}"/>
          <rect x="2" y="20" width="5" height="2" fill="${color}"/>
          <rect x="2" y="20" width="5" height="2" fill="${color}"/>
          <rect x="2" y="22" width="5" height="2" transform="rotate(-90 2 22)" fill="${color}"/>
          <path d="M3 7V3H7" stroke="${color}" stroke-width="2"/>
          <path d="M17 3L21 3L21 7" stroke="${color}" stroke-width="2"/>
          <rect x="2" y="10" width="2" height="4" fill="${color}"/>
          <rect x="14" y="2" width="2" height="4" transform="rotate(90 14 2)" fill="${color}"/>
          `
      }
    },
    {
      name:  'shipped-widget',
      sizes: {
        l: `
          <path d="M21 11V3L16.1143 3L3 3V21H10" stroke="${color}" stroke-width="2"/>
          <path d="M6 6H15V8H6V6Z" fill="${color}"/>
          <path d="M6 10H10V12H6V10Z" fill="${color}"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M18.1716 18.0071L16.8787 19.3L18.2929 20.7143L22 17.0072L18.2929 13.3L16.8787 14.7143L18.1715 16.0071L12.5857 16.0071L12.5857 18.0071L18.1716 18.0071Z" fill="${color}"/>
          `
      }
    },
    {
      name:  'arrow-right',
      sizes: {
        m: `<path d="M6 4L11 8L6 12V4Z" fill="${color}"/>`
      }
    },
    {
      name:  'checkbox-checked',
      sizes: {
        m: `<path d="M14.25 0.5H1.75C1.41848 0.5 1.10054 0.631696 0.866116 0.866116C0.631696 1.10054 0.5 1.41848 0.5 1.75V14.25C0.5 14.5815 0.631696 14.8995 0.866116 15.1339C1.10054 15.3683 1.41848 15.5 1.75 15.5H14.25C14.5815 15.5 14.8995 15.3683 15.1339 15.1339C15.3683 14.8995 15.5 14.5815 15.5 14.25V1.75C15.5 1.41848 15.3683 1.10054 15.1339 0.866116C14.8995 0.631696 14.5815 0.5 14.25 0.5ZM6.75 11.4375L3.625 8.33919L4.61925 7.375L6.75 9.466L11.3804 4.875L12.3753 5.86075L6.75 11.4375Z" fill="${color}"/>`
      }
    },
    {
      name:  'checkbox-unchecked',
      sizes: {
        m: `<path d="M14.25 0.5H1.75C1.41848 0.5 1.10054 0.631696 0.866116 0.866116C0.631696 1.10054 0.5 1.41848 0.5 1.75V14.25C0.5 14.5815 0.631696 14.8995 0.866116 15.1339C1.10054 15.3683 1.41848 15.5 1.75 15.5H14.25C14.5815 15.5 14.8995 15.3683 15.1339 15.1339C15.3683 14.8995 15.5 14.5815 15.5 14.25V1.75C15.5 1.41848 15.3683 1.10054 15.1339 0.866116C14.8995 0.631696 14.5815 0.5 14.25 0.5ZM1.75 14.25V1.75H14.25V14.25H1.75Z" fill="${color}"/>`
      }
    },
    {
      name:  'checkbox-partial',
      sizes: {
        m: `<path d="M14.25 0.5H1.75C1.41848 0.5 1.10054 0.631696 0.866116 0.866116C0.631696 1.10054 0.5 1.41848 0.5 1.75V14.25C0.5 14.5815 0.631696 14.8995 0.866116 15.1339C1.10054 15.3683 1.41848 15.5 1.75 15.5H14.25C14.5815 15.5 14.8995 15.3683 15.1339 15.1339C15.3683 14.8995 15.5 14.5815 15.5 14.25V1.75C15.5 1.41848 15.3683 1.10054 15.1339 0.866116C14.8995 0.631696 14.5815 0.5 14.25 0.5ZM11.75 9.25H4.25V6.75H11.75V9.25Z" fill="${color}"/>`
      }
    },
    {
      name:  'radio-checked',
      sizes: {
        m: `<path d="M8 0.125C6.44248 0.125 4.91992 0.586861 3.62489 1.45218C2.32985 2.31749 1.32049 3.5474 0.724452 4.98637C0.128412 6.42534 -0.0275392 8.00874 0.276319 9.53634C0.580178 11.0639 1.3302 12.4671 2.43154 13.5685C3.53288 14.6698 4.93607 15.4198 6.46367 15.7237C7.99127 16.0275 9.57467 15.8716 11.0136 15.2756C12.4526 14.6795 13.6825 13.6702 14.5478 12.3751C15.4131 11.0801 15.875 9.55753 15.875 8C15.875 5.91142 15.0453 3.90838 13.5685 2.43153C12.0916 0.954685 10.0886 0.125 8 0.125ZM8 14.75C6.66498 14.75 5.35994 14.3541 4.2499 13.6124C3.13987 12.8707 2.27471 11.8165 1.76382 10.5831C1.25293 9.34971 1.11925 7.99251 1.3797 6.68314C1.64015 5.37377 2.28303 4.17103 3.22703 3.22703C4.17104 2.28302 5.37377 1.64015 6.68314 1.3797C7.99252 1.11925 9.34972 1.25292 10.5831 1.76381C11.8165 2.2747 12.8707 3.13987 13.6124 4.2499C14.3541 5.35993 14.75 6.66498 14.75 8C14.75 9.79021 14.0388 11.5071 12.773 12.773C11.5071 14.0388 9.79021 14.75 8 14.75Z" fill="${color}"/><path d="M8 4.625C7.33249 4.625 6.67997 4.82294 6.12495 5.19379C5.56994 5.56464 5.13736 6.09174 4.88191 6.70844C4.62646 7.32514 4.55963 8.00374 4.68985 8.65843C4.82008 9.31312 5.14152 9.91448 5.61352 10.3865C6.08552 10.8585 6.68689 11.1799 7.34157 11.3102C7.99626 11.4404 8.67486 11.3735 9.29156 11.1181C9.90826 10.8626 10.4354 10.4301 10.8062 9.87505C11.1771 9.32003 11.375 8.66751 11.375 8C11.375 7.10489 11.0194 6.24645 10.3865 5.61351C9.75355 4.98058 8.89511 4.625 8 4.625Z" fill="${color}"/>`
      }
    },
    {
      name:  'radio-unchecked',
      sizes: {
        m: `<path d="M8 0.125C6.44248 0.125 4.91992 0.586861 3.62489 1.45218C2.32985 2.31749 1.32049 3.5474 0.724452 4.98637C0.128412 6.42534 -0.0275392 8.00874 0.276319 9.53634C0.580178 11.0639 1.3302 12.4671 2.43154 13.5685C3.53288 14.6698 4.93607 15.4198 6.46367 15.7237C7.99127 16.0275 9.57467 15.8716 11.0136 15.2756C12.4526 14.6795 13.6825 13.6702 14.5478 12.3751C15.4131 11.0801 15.875 9.55753 15.875 8C15.875 5.91142 15.0453 3.90838 13.5685 2.43153C12.0916 0.954685 10.0886 0.125 8 0.125V0.125ZM8 14.75C6.66498 14.75 5.35994 14.3541 4.2499 13.6124C3.13987 12.8707 2.27471 11.8165 1.76382 10.5831C1.25293 9.34971 1.11925 7.99251 1.3797 6.68314C1.64015 5.37377 2.28303 4.17103 3.22703 3.22703C4.17104 2.28302 5.37377 1.64015 6.68314 1.3797C7.99252 1.11925 9.34972 1.25292 10.5831 1.76381C11.8165 2.2747 12.8707 3.13987 13.6124 4.2499C14.3541 5.35993 14.75 6.66498 14.75 8C14.75 9.79021 14.0388 11.5071 12.773 12.773C11.5071 14.0388 9.79022 14.75 8 14.75Z" fill="${color}"/>`
      }
    },
    {
      name:  'filter',
      sizes: {
        m: `<path d="M1 4.5V1H15V4.5L10 8.5V14.5375L6 13V8.5L1 4.5Z" stroke="${color}" stroke-width="2"/>`
      }
    }
  ]

}

export function createIcon( options: IconProps ) {

  const n = options?.name
  const s = options?.size as keyof IconSize
  const c = options?.color ?? 'currentColor'
  const v = options?.variant ?? null

  const defaultIcon = iconsList( '#c6c6c6' ).find( i => i.name === 'empty-box' )

  const iconItem: IconItem = iconsList( c ).find( i => i.name === n )
  const icon: string | IconVariant = iconItem?.sizes[s] ?? defaultIcon.sizes[s]
  const iconPath: string = v ? icon[v] : icon

  return iconPath

}
