type UtilsSize = 'full' | 'auto' | 'xxs' | 'xs' | 's' | 'm' | 'l' | 'xl' | 'xxl'
type UtilsMode = ColorsMode | 'skinny' | 'naked' | 'ghost'
type StatusMode = 'success' | 'warning' | 'error'
type ColorsMode = 'primary' | 'secondary' | StatusMode
type CssSizeUnit = 'px' | 'rem'
type SortDirection = 'asc' | 'desc' | 'ASC' | 'DESC'
type TwMediaQueries = 'sm' | 'md' | 'lg' | 'xl' | '2xl'

interface DropListOption {
  id:    any
  name:  string
  icon?: {
    name:      import( '../store/icon' ).IconName
    size?:     keyof import( '../store/icon' ).IconSize
    color?:    string
    position?: 'left' | 'right'
  }
  color?:        ColorsMode
  action?:       () => ( Promise<any> | void )
  hidden?:       boolean
  options?:      DropListOption[]
  selected?:     boolean
  disabled?:     boolean
  description?:  string
  mapToBoolean?: 'true' | 'false'
}

interface BaseParams {
  page?:          number
  sortBy?:        string
  pageSize?:      number
  sortDirection?: SortDirection
}

interface PaginationOptions {
  nextPage:    number
  pageSize:    number
  totalRows:   number
  isLastPage:  boolean
  totalPages:  number
  currentPage: number
}

type PaginatedResponse<RecordsName extends string, Type> = PaginationOptions & { [K in RecordsName]: Type }

type TableItemFormat = 'date' | 'currency' | 'custom'

interface TableRecordStatus {
  type:     StatusMode
  count?:   number
  message?: string
}

/**
 * The type of the nested view.
 */
type NestedType = 'extended' | 'nested' | 'slot'

/**
 * The trigger type to expand the nested view.
 */
type NestedTriggerType = 'default' | 'key' | 'menu'

/**
 * Describes the payload type of the nested records request.
 */
type NestedRequest<Model> = ( params: BaseParams ) => Promise<Payload<PaginatedResponse<'records', Model>>>

interface NestedTriggerOptions<TriggerType> {

  /**
   * The type of the trigger.
   * If set to 'default', the slot will be triggered by the default expand row icon.
   * If set to 'menu', the slot will be triggered by an option in the row menu.
   * If set to 'key', the slot will be triggered by clicking on the table cell that matches that key.
   */
  type: TriggerType

}

interface NestedTriggerKey<Model> {

  /**
   * The key for the cell trigger.
   */
  key: keyof Model

}

interface NestedTriggerMenu {

  /**
   * The option for the row menu if the type is set to 'menu'.
   * If this is not provided, there will be a default menu option created for the slot.
   */
  option: DropListOption

}

type NestedTrigger<Model, Type = NestedTriggerType> =
  Type extends 'default'
    ? NestedTriggerOptions<Type> & {}
    : Type extends 'menu'
      ? NestedTriggerOptions<Type> & NestedTriggerMenu
      : Type extends 'key'
        ? NestedTriggerOptions<Type> & NestedTriggerKey<Model>
        : never

interface NestedOptions<Model, Type> {

  /**
   * The name of the nested view.
   * This will be used for the table unique name.
   */
  name: string

  /**
   * Type of nested data to be used.
   * If set to 'extended', the nested data will continue with the same schema.
   * If set to 'nested', the nested data will be a new schema in a full nested table.
   * If set to 'slot', there needs to be an external component and slot options provided.
   */
  type: Type

  /**
   * The title of the nested view.
   * This will be used for the nested view header.
   */
  title?: string

  /**
   * If this is set to true, the nested view width will be locked to the screen width.
   */
  lockWidth?: boolean

  /**
   * Trigger options for the slot.
   * This will define how the slot will be triggered.
   */
  trigger?: NestedTrigger<Model>

}

interface NestedExtendedOptions<NestedModel> {

  /**
   * Request function to fetch the nested data.
   * If there is a request provided, the nested view will be set in an async mode,
   * with a loading state and error handling.
   */
  request?: NestedRequest<NestedModel[]>

  /**
   * The nested data to be used.
   * If the view is in async mode, this data will be set to the response of the request.
   * Otherwise, the records should already be provided in this list.
   */
  records?: NestedModel[]

  /**
   * The key to map the nested records.
   */
  recordMapKey?: keyof NestedModel

  /**
   * Optional options for the table row menu.
   */
  recordOptions?: ( record: NestedModel ) => DropListOption[]

}

interface NestedNestedOptions<NestedModel> extends NestedExtendedOptions<NestedModel> {

  /**
   * The schema of the nested data.
   * If set to 'extended', this schema will be ignored, and the parent schema will be used.
   * If set to 'nested', this schema will be used to describe the nested table.
   */
  schema: ( record: NestedModel ) => TableSchema<NestedModel>

}

interface NestedSlotOptions<Model, Props> {

  /**
   * The props to be passed to the component.
   */
  props?: Props

  /**
   * The component instance.
   */
  component: import( 'vue' ).Component

}

/**
 * The nested options for the table.
 * This type will allow only the options for the selected nested type.
 */
type Nested<Model, NestedModel, Props, Type = NestedType> =
  Type extends 'extended'
    ? NestedOptions<Model, Type> & NestedExtendedOptions<NestedModel>
    : Type extends 'nested'
      ? NestedOptions<Model, Type> & NestedNestedOptions<NestedModel>
      : Type extends 'slot'
        ? NestedOptions<Model, Type> & NestedSlotOptions<Model, Props>
        : never

/**
 * The model of the table record.
 * Describes the added record properties by the "Tablify" helper
 * required by the table component.
 */
interface TabledRecord<Model, NestedModel, Props> {

  selected?:                  boolean
  tableRecordStatus?:         TableRecordStatus
  tableRecordDisabled?:       boolean
  tableRecordMenuDisabled?:   boolean
  tableRecordClickDisabled?:  boolean
  tableRecordExpandDisabled?: boolean
  tableRecordSelectDisabled?: boolean

  nested?: Nested<Model, NestedModel, Props> | Nested<Model, NestedModel, Props>[]

}

/**
 * Helper type to tablify the record model that is passed to the table.
 */
type Tablify<Model, ChildrenModel = Model, Props = Record<string, any>> = Model & TabledRecord<Model, ChildrenModel, Props>

interface TableSchemaItemComponent {
  name?:     string
  props?:    Record<string, any>
  slots?:    Record<string, () => string>
  onClick?:  () => void
  component: import( 'vue' ).Component
}

interface TableSchemaItem<DataModel> {

  link?:   import( 'vue-router/dist/vue-router.d.ts' ).RouteLocationRaw
  label:   string | TableSchemaItemComponent
  value?:  string | number | TableSchemaItemComponent
  status?: TableRecordStatus

  key:      keyof DataModel
  sortKey?: LooseAutoComplete<keyof DataModel>

  class?:     string
  align?:     'left' | 'center' | 'right'
  sticky?:    'left' | 'right'
  hidden?:    boolean
  format?:    TableItemFormat
  resize?:    boolean
  lockFlex?:  boolean
  fixedSize?: `${number}px` | `${number}rem` | number

  transform?: ( value?: any ) => string

}

type TableSchema<DataModel> = TableSchemaItem<DataModel>[]

interface RouteModelMeta<Params, AccessScope extends string = any> {
  scope?:        AccessScope | AccessScope[]
  queryParams?:  Params
  requiresAuth?: boolean
}

type RouteModel<Params, AccessScope = any> = import( 'vue-router/dist/vue-router.d.ts' ).RouteRecordRaw & {
  meta?:     RouteModelMeta<Params, AccessScope>
  children?: RouteModel<Params, AccessScope>[]
}

/**
 * Define a function that will provide the scopes for a given access token.
 * @param {string} accessToken - The access token to be used to fetch the scopes.
 */
type ScopesProvider<ScopeModel> = ( accessToken: string ) => Promise<Payload<ScopeModel[]>>

/**
 * Defines a guard function that will check if the user has access to a given scope.
 * @param {Scope | Scope[]} scope - The scope or list of scopes to be checked.
 * @param {ScopeModel[]} scopeList - The list of scopes that the user has access to.
 * @param {PublicClientApplication} instance - The MSAL instance to be used to fetch the scopes.
 */
type AccessGuard<ScopeModel, Scope extends string = any> = ( scope: Scope | Scope[], scopeList: ScopeModel[], instance?: import( '@azure/msal-browser' ).PublicClientApplication ) => boolean

/**
 * A type that converts an object to a validatable object,
 * where each key has a value and a valid property.
 */
type Validatable<T> = { [K in keyof T]: { value: T[K], valid: boolean } }

/**
 * A type that represents a string that can be any value, but is not required.
 * Useful for optional string values with autocomplete provided.
 */
type LooseAutoComplete<T extends string> = T | Omit<string, T>

/**
 * Check if two types are exactly the same, including any, never, null, undefined, void and unknown.
 */
type IsExactTypeMatch<T, U> = ( <G>() => G extends T ? 1 : 2 ) extends ( <G>() => G extends U ? 1 : 2 ) ? true : false

/**
 * Extract keys from an object by their exact type.
 */
type ExtractKeysByType<Source, Type, Key = keyof Source> =
  Key extends keyof Source
    ? IsExactTypeMatch<Source[Key], Type> extends true
      ? Key
      : never
    : never

type CapitalizeKeys<T> = { [K in keyof T as Capitalize<K extends string ? K : never>]: T[K] }

type DecapitalizeKeys<T> = { [K in keyof T as Uncapitalize<K extends string ? K : never>]: T[K] }

interface PayloadError {
  message: string
  details: string
}

interface Payload<T> {
  error:    PayloadError[]
  status:   number
  payload:  T
  headers?: any
}
