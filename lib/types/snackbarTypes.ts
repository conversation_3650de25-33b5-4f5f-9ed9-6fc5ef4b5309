export type SnackbarType = 'alert' | 'notification'
export type AlertSeverity = 'info' | 'success' | 'warning' | 'error' | 'naked'

export interface AlertOptions {
  id?:         string
  open:        boolean
  strict?:     boolean
  action?:     () => void
  message?:    string
  details?:    string
  duration?:   number
  severity?:   AlertSeverity
  actionName?: string
}

export type NotificationOptions = Omit<AlertOptions, 'severity'>

export interface SnackbarProps extends Omit<AlertOptions, 'open'> {
  type:             SnackbarType
  grow?:            boolean
  showCloseButton?: boolean
}
